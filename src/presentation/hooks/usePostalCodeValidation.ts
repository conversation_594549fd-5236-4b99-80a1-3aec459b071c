import React from "react";

import { postalCodeValidatingSubject } from "@/shared/validations";

const usePostalCodeValidation = () => {
    const [isValidating, setIsValidating] = React.useState(false);

    React.useEffect(() => {
        const subscription = postalCodeValidatingSubject.subscribe((validating) => {
            setIsValidating(validating);
        });

        return () => {
            subscription.unsubscribe();
        };
    }, []);

    return {
        isValidating
    };
};

export default usePostalCodeValidation;
