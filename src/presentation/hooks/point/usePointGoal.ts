import React from "react";

import { usePointGoalQueries } from "@/data/queries/userQueries";

const usePointGoal = () => {
    const { pointGoal, isLoading, getPointGoal } = usePointGoalQueries();

    const isWithinDailyLimit = React.useMemo(() => {
        if (!pointGoal) return true;

        const todayReward = Number(pointGoal.today_reward) ?? 0;
        const dailyLimit = Number(pointGoal.daily_point_goal) ?? 0;

        return todayReward >= dailyLimit;
    }, [pointGoal]);

    return {
        pointGoal,
        isLoading,
        isWithinDailyLimit,
        getPointGoal
    };
};

export default usePointGoal;
