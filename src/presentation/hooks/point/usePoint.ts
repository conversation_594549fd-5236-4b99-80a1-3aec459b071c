import React from "react";

import { useUserStore } from "@/app/store";

import useShowToast from "../useShowToast";

import { formatPoints } from "@/shared/helper";

interface UsePointReturn {
    point: string;
    addPoint: (amount: number) => boolean;
    subtractPoint: (amount: number) => boolean;
    hasEnoughPoint: (amount: number) => boolean;
    getCurrentPoint: () => number;
    handleSubtractPoint: (amount: number) => boolean;
    canRedeem: (amount: number) => boolean;
    updateTotalPoint: (newTotal?: number) => boolean;
}

const usePoint = (): UsePointReturn => {
    const showToast = useShowToast();
    const { addPoint, subtractPoint, hasEnoughPoint, getCurrentPoint, updateTotalPoint } = useUserStore();

    const point = getCurrentPoint();

    const handleAddPoint = React.useCallback(
        (amount: number): boolean => {
            const isSuccess = addPoint(amount);
            if (isSuccess) {
                showToast?.showSuccess(`Successfully added ${amount} points!`);
            } else {
                showToast?.showError("Failed to add points. Please check the amount.");
            }
            return isSuccess;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [addPoint]
    );

    const handleSubtractPoint = React.useCallback(
        (amount: number): boolean => {
            const isSuccess = subtractPoint(amount, () => {
                showToast?.showError("You don't have enough points to redeem this item");
            });

            if (isSuccess) {
                showToast?.showSuccess(`Successfully redeemed ${amount} points!`);
            }

            return isSuccess;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [subtractPoint]
    );

    const handleUpdateTotalPoint = React.useCallback(
        (newTotal?: number): boolean => {
            const isSuccess = updateTotalPoint(newTotal || 0);

            return isSuccess;
        },
        [updateTotalPoint]
    );

    const canRedeem = React.useCallback(
        (amount: number): boolean => {
            const isEnough = hasEnoughPoint(amount);
            if (!isEnough) {
                showToast?.showError("You don't have enough points to redeem this item");
            }
            return isEnough;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [hasEnoughPoint]
    );

    const formatPoint = React.useMemo(() => {
        return formatPoints(point);
    }, [point]);

    return {
        point: formatPoint,
        addPoint: handleAddPoint,
        subtractPoint: handleSubtractPoint,
        hasEnoughPoint,
        getCurrentPoint,
        handleSubtractPoint,
        canRedeem,
        updateTotalPoint: handleUpdateTotalPoint
    };
};

export default usePoint;
