import { useBinByWasteTypeIdAndAcceptedRecyclablesQueries } from "@/data/queries";

const useSearchBin = () => {
    const { getBinsByWasteTypeIdAndAcceptedRecyclables, isLoading, binsByWasteTypeIdAndAcceptedRecyclables } =
        useBinByWasteTypeIdAndAcceptedRecyclablesQueries();

    return { binsByWasteTypeIdAndAcceptedRecyclables, getBinsByWasteTypeIdAndAcceptedRecyclables, isLoading };
};

export default useSearchBin;
