import React from "react";

import { useAppState } from "../useAppState";

import { binStorageService, BinStorageStatus } from "@/data/services/binStorage";

const useBin = () => {
    const [bins, setBins] = React.useState<BinResponse[]>([]);
    const [binsByWasteTypeId, setBinsByWasteTypeId] = React.useState<BinResponse[] | undefined>(undefined);
    const [activeFilterId, setActiveFilterId] = React.useState<number | null>(null);
    const [storageStatus, setStorageStatus] = React.useState<BinStorageStatus>({
        state: "idle",
        progress: 0,
        totalRecords: 0,
        downloadedRecords: 0,
        lastSync: null,
        isInitialLoad: true
    });

    const { isActive, hasBeenInactive, timeSinceLastActive, offlineDuration } = useAppState();

    React.useEffect(() => {
        const initializeStorage = async () => {
            try {
                await binStorageService.initialize();
                const localBins = await binStorageService.getLocalBins();
                setBins(localBins);
            } catch (e) {
                /* empty */
            }
        };

        initializeStorage();

        const unsubscribe = binStorageService.addStatusListener((status) => {
            setStorageStatus(status);

            if (status.state === "idle" && status.totalRecords > 0) {
                binStorageService.getLocalBins().then(setBins).catch(console.error);
            }
        });

        return unsubscribe;
    }, []);

    React.useEffect(() => {
        if (hasBeenInactive && isActive) {
            const minutesSinceInactive = timeSinceLastActive / (1000 * 60);

            if (minutesSinceInactive >= 30) {
                binStorageService.forceRefresh().catch(console.error);
            }
        }
    }, [hasBeenInactive, isActive, timeSinceLastActive]);

    React.useEffect(() => {
        if (offlineDuration >= 30) {
            binStorageService.forceRefresh().catch(console.error);
        }
    }, [offlineDuration]);

    const handleFilterToggle = async (id: number) => {
        try {
            if (typeof id !== "number" || isNaN(id) || !isFinite(id)) {
                return;
            }

            if (activeFilterId === id) {
                setActiveFilterId(null);
                setBinsByWasteTypeId([]);

                setTimeout(() => {
                    setBinsByWasteTypeId(undefined);
                }, 100);
            } else {
                setActiveFilterId(id);

                setBinsByWasteTypeId([]);

                try {
                    const filteredBins = await binStorageService.getLocalBinsByWasteTypeId(id);

                    if (filteredBins && Array.isArray(filteredBins)) {
                        const validBins = filteredBins.filter((bin) => {
                            if (!bin || typeof bin !== "object") return false;

                            const hasValidId = bin.id && typeof bin.id === "number";
                            const hasValidCoords =
                                bin.lat && bin.long && !isNaN(parseFloat(bin.lat)) && !isNaN(parseFloat(bin.long));
                            const hasValidType = bin.type && typeof bin.type === "object";

                            return hasValidId && hasValidCoords && hasValidType;
                        });

                        setBinsByWasteTypeId(validBins);
                    }
                } catch (error) {
                    setBinsByWasteTypeId([]);
                    setActiveFilterId(null);
                }
            }
        } catch (error) {
            setActiveFilterId(null);
            setBinsByWasteTypeId(undefined);
        }
    };

    const memoBins = React.useMemo(() => {
        if (binsByWasteTypeId === undefined) {
            return bins;
        }
        return binsByWasteTypeId;
    }, [binsByWasteTypeId, bins]);

    const isLoading = storageStatus.state === "downloading" || storageStatus.state === "syncing";

    const isDownloadUIVisible = bins.length === 0 && (isLoading || storageStatus.state === "error");

    const isMapDataReady = bins.length > 0;

    const retryDownload = React.useCallback(async () => {
        try {
            await binStorageService.forceRefresh();
        } catch (error) {
            /* empty */
        }
    }, []);

    return {
        bins,
        getBinsByWasteTypeId: handleFilterToggle,
        binsByWasteTypeId,
        isLoading,
        activeFilterId,
        binsToUse: memoBins,
        storageStatus,
        isDownloadUIVisible,
        retryDownload,
        isMapDataReady
    };
};

export default useBin;
