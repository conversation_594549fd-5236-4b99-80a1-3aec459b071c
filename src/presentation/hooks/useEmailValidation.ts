import React from "react";

import { emailValidatingSubject } from "@/shared/validations";

const useEmailValidation = () => {
    const [isValidating, setIsValidating] = React.useState(false);

    React.useEffect(() => {
        const subscription = emailValidatingSubject.subscribe((validating) => {
            setIsValidating(validating);
        });

        return () => {
            subscription.unsubscribe();
        };
    }, []);

    return {
        isValidating
    };
};

export default useEmailValidation;
