import React from "react";

import { nicknameValidatingSubject } from "@/shared/validations";

const useNicknameValidation = () => {
    const [isValidating, setIsValidating] = React.useState(false);

    React.useEffect(() => {
        const subscription = nicknameValidatingSubject.subscribe((validating) => {
            setIsValidating(validating);
        });

        return () => {
            subscription.unsubscribe();
        };
    }, []);

    return {
        isValidating
    };
};

export default useNicknameValidation;
