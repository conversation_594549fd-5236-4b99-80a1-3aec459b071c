import AsyncStorage from "@react-native-async-storage/async-storage";
import React from "react";
import { AppState, AppStateStatus } from "react-native";

const APP_STATE = {
    ACTIVE: "active" as const,
    BACKGROUND: "background" as const,
    INACTIVE: "inactive" as const
} as const;

interface UseAppStateReturn {
    appState: AppStateStatus;
    isActive: boolean;
    timeSinceLastActive: number;
    hasBeenInactive: boolean;
    offlineDuration: number;
}

const LAST_ACTIVE_TIME_KEY = "last_app_active_time";

export const useAppState = (): UseAppStateReturn => {
    const [appState, setAppState] = React.useState<AppStateStatus>(AppState.currentState);
    const [isActive, setIsActive] = React.useState<boolean>(true);
    const [lastActiveTime, setLastActiveTime] = React.useState<number>(Date.now());
    const [hasBeenInactive, setHasBeenInactive] = React.useState<boolean>(false);
    const [offlineDuration, setOfflineDuration] = React.useState<number>(0);

    React.useEffect(() => {
        const checkOfflineDuration = async () => {
            try {
                const storedTime = await AsyncStorage.getItem(LAST_ACTIVE_TIME_KEY);
                if (storedTime) {
                    const lastStoredTime = parseInt(storedTime, 10);
                    const timeDiff = Date.now() - lastStoredTime;
                    const minutesDiff = timeDiff / (1000 * 60);
                    setOfflineDuration(minutesDiff);
                }
            } catch (error) {
                /* empty */
            }
        };

        checkOfflineDuration();
    }, []);

    React.useEffect(() => {
        const handleAppStateChange = (nextAppState: AppStateStatus) => {
            const now = Date.now();

            if (nextAppState === APP_STATE.ACTIVE) {
                if (!isActive) {
                    setHasBeenInactive(true);
                }
                setIsActive(true);
                setLastActiveTime(now);
                setOfflineDuration(0);

                AsyncStorage.setItem(LAST_ACTIVE_TIME_KEY, now.toString()).catch(() => {});
            } else if (nextAppState === APP_STATE.BACKGROUND || nextAppState === APP_STATE.INACTIVE) {
                setIsActive(false);
                setLastActiveTime(now);

                AsyncStorage.setItem(LAST_ACTIVE_TIME_KEY, now.toString()).catch(() => {});
            }

            setAppState(nextAppState);
        };

        const subscription = AppState.addEventListener("change", handleAppStateChange);

        return () => {
            subscription.remove();
        };
    }, [isActive]);

    const timeSinceLastActive = Date.now() - lastActiveTime;

    return {
        appState,
        isActive,
        timeSinceLastActive,
        hasBeenInactive,
        offlineDuration
    };
};
