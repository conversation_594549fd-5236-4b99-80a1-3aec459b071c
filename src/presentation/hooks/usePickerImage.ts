import * as ImageManipulator from "expo-image-manipulator";
import * as ImagePicker from "expo-image-picker";
import React from "react";
import { Alert } from "react-native";

import { pickerMedia } from "@/shared/helper";

interface OptimizeImageOptions {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    maxFileSize?: number; // in bytes
}

const usePickerImage = (isMultiple?: boolean) => {
    const [isLoadingPicker, setIsLoadingPicker] = React.useState(false);
    const [error, setError] = React.useState<string | null>(null);

    const optimizeImage = React.useCallback(
        async (
            image: ImagePicker.ImagePickerAsset,
            options: OptimizeImageOptions = {}
        ): Promise<ImagePicker.ImagePickerAsset | undefined> => {
            try {
                if (!image.uri) return image;

                const {
                    maxWidth = 2000,
                    maxHeight = 2000,
                    quality: customQuality,
                    maxFileSize = 5 * 1024 * 1024
                } = options;

                if (image.fileSize && image.fileSize > maxFileSize) {
                    console.warn(`Image file size (${image.fileSize} bytes) exceeds limit (${maxFileSize} bytes)`);
                }

                const manipulator = ImageManipulator.ImageManipulator.manipulate(image.uri);

                let newWidth = image.width;
                let newHeight = image.height;

                if (image.width > maxWidth || image.height > maxHeight) {
                    const ratio = Math.min(maxWidth / image.width, maxHeight / image.height);
                    newWidth = Math.round(image.width * ratio);
                    newHeight = Math.round(image.height * ratio);

                    manipulator.resize({
                        width: newWidth,
                        height: newHeight
                    });
                }

                let quality = customQuality || 0.9;
                if (!customQuality && image.fileSize) {
                    if (image.fileSize > 5 * 1024 * 1024) {
                        quality = 0.7;
                    } else if (image.fileSize > 2 * 1024 * 1024) {
                        quality = 0.8;
                    }
                }

                const resizedImage = await manipulator.renderAsync().then((img) =>
                    img.saveAsync({
                        compress: quality,
                        format: ImageManipulator.SaveFormat.JPEG
                    })
                );

                manipulator.release();

                return {
                    ...image,
                    uri: resizedImage.uri,
                    width: resizedImage.width,
                    height: resizedImage.height
                };
            } catch (err) {
                /* empty */
            }
        },
        []
    );

    const pickerImage = React.useCallback(
        async (type: "photo" | "video" = "photo", options?: OptimizeImageOptions) => {
            try {
                setIsLoadingPicker(true);
                const result = await pickerMedia(type, isMultiple);

                if (result?.assets && result.assets.length > 0) {
                    const optimizedAssets = await Promise.all(
                        result.assets.map((asset) => optimizeImage(asset, options))
                    );
                    return optimizedAssets;
                }
                return undefined;
            } catch (err) {
                /* empty */
            } finally {
                setIsLoadingPicker(false);
            }
        },
        [isMultiple, optimizeImage]
    );

    const requestPermissions = React.useCallback(async () => {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== "granted") {
            setError("Sorry, we need camera roll permissions to make this work!");
            return false;
        }
        return true;
    }, []);

    const pickerImageWithValidation = React.useCallback(
        async (
            type: "photo" | "video" = "photo",
            options?: OptimizeImageOptions & {
                maxImages?: number;
                allowedTypes?: string[];
            }
        ) => {
            const hasPermission = await requestPermissions();
            if (!hasPermission) return;

            try {
                const { maxImages, allowedTypes, ...optimizeOptions } = options || {};

                const result = await pickerImage(type, optimizeOptions);

                if (!result) return undefined;

                if (maxImages && result.length > maxImages) {
                    Alert.alert(`Maximum ${maxImages} images allowed`);
                    return;
                }

                if (allowedTypes && result.length > 0) {
                    const invalidImages = result.filter((img) => {
                        const extension = img?.uri?.split(".").pop()?.toLowerCase();
                        return extension && !allowedTypes.includes(extension);
                    });

                    if (invalidImages.length > 0) {
                        throw new Error(`Invalid file types. Allowed: ${allowedTypes.join(", ")}`);
                    }
                }

                return result;
            } catch (err) {
                console.error("Image validation error:", err);
                throw err;
            }
        },
        [pickerImage, requestPermissions]
    );

    return {
        pickerImage,
        pickerImageWithValidation,
        isLoadingPicker,
        optimizeImage,
        error
    };
};

export default usePickerImage;
