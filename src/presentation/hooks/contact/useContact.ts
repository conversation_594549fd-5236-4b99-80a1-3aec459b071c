import React from "react";

import { useContactQueries } from "@/data/queries";

import { createDataFromDataObject } from "@/shared/helper";

const createContactFormData = (contactData: ContactRequest): FormData => {
    const { attachments, ...basicFields } = contactData;
    const formData = createDataFromDataObject(basicFields);

    if (attachments && attachments.length > 0) {
        attachments.forEach((file) => {
            const fileInfo = {
                uri: file.uri,
                type: file.type || "image/jpeg",
                name: file.fileName || `image_${Date.now()}.jpg`
            };
            formData.append("attachments[]", fileInfo as any);
        });
    }

    return formData;
};

const useContact = () => {
    const { sendContact, isLoading } = useContactQueries();

    const handleSendContact = React.useCallback(async (contactData: ContactRequest) => {
        const formData = createContactFormData(contactData);
        const result = await sendContact(formData);
        return result;
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return { sendContact: handleSendContact, isLoading };
};

export default useContact;
