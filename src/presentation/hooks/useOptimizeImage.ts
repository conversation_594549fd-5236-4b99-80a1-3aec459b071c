import * as FileSystem from "expo-file-system";
import * as ImageManipulator from "expo-image-manipulator";
import { PhotoFile } from "react-native-vision-camera";

const useOptimizeImage = () => {
    const optimizeImage = async (image: PhotoFile): Promise<PhotoFile> => {
        try {
            if (!image.path) return image;

            const manipulator = ImageManipulator.ImageManipulator.manipulate(image.path);

            let quality = 0.9;
            try {
                const fileInfo = await FileSystem.getInfoAsync(image.path);
                if (fileInfo.exists && fileInfo.size) {
                    if (fileInfo.size > 5 * 1024 * 1024) {
                        quality = 0.7;
                    } else if (fileInfo.size > 2 * 1024 * 1024) {
                        quality = 0.8;
                    }
                }
            } catch (error) {
                console.error("Error getting file size:", error);
            }

            const resizedImage = await manipulator.renderAsync().then((img) =>
                img.saveAsync({
                    compress: quality,
                    format: ImageManipulator.SaveFormat.JPEG
                })
            );

            manipulator.release();

            return {
                ...image,
                path: resizedImage.uri,
                width: resizedImage.width,
                height: resizedImage.height
            };
        } catch (error) {
            console.error("Error optimizing image:", error);
            return image;
        }
    };

    return { optimizeImage };
};

export default useOptimizeImage;
