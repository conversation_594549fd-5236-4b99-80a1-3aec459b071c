import { useNavigation } from "@react-navigation/native";
import React from "react";

import { useUserStore } from "@/app/store/user.store";
import { HttpClient } from "@/data/services/httpClient";
import { RouteName } from "@/shared/constants";

const useStartApp = () => {
    const navigation = useNavigation();
    const { isGuestMode } = useUserStore();

    React.useEffect(() => {
        const init = async () => {
            if (isGuestMode()) {
                navigation.replace(RouteName.Bottom);
                return;
            }

            const isSuccess = await HttpClient.getTokenService().refreshToken();
            if (isSuccess) {
                navigation.replace(RouteName.Bottom);
            } else {
                navigation.replace(RouteName.Welcome);
            }
        };

        init();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
};

export default useStartApp;
