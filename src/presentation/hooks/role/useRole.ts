import React from "react";

import { useLoginStore } from "@/app/store";

import { BUSINESS_TYPE_INDIVIDUAL } from "@/shared/constants";

const useRole = () => {
    const { login } = useLoginStore();

    const isIndividual = React.useMemo(() => {
        return login?.user?.role_name === BUSINESS_TYPE_INDIVIDUAL;
    }, [login]);

    const isSchool = React.useMemo(() => {
        return login?.user?.role_name !== BUSINESS_TYPE_INDIVIDUAL;
    }, [login]);

    return { isIndividual, isSchool };
};

export default useRole;
