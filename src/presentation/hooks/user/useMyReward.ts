import React from "react";

import { useMyRewardQueries } from "@/data/queries";

import { RewardStatusType } from "@/shared/constants";

const useMyReward = () => {
    const { getMyReward, isLoading, myReward } = useMyRewardQueries();
    const [tabStatus, setStatus] = React.useState<RewardStatusType>(RewardStatusType.active);

    const handleSetStatus = React.useCallback((statusValue: RewardStatusType) => {
        setStatus(statusValue);
    }, []);

    React.useEffect(() => {
        const init = async () => {
            await getMyReward(tabStatus);
        };

        init();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [tabStatus]);

    return {
        isLoading,
        myReward,
        handleSetStatus,
        tabStatus
    };
};

export default useMyReward;
