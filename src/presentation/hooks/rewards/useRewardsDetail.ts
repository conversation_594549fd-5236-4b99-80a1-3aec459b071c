import React from "react";

import { useRewardDetailQueries } from "@/data/queries";

const useRewardsDetail = () => {
    const { isLoading, fetchRewardDetail } = useRewardDetailQueries();

    const handleFetchRewardDetail = React.useCallback(
        async (id: number) => {
            await fetchRewardDetail(id);
        },
        [fetchRewardDetail]
    );

    return { isLoading, handleFetchRewardDetail };
};

export default useRewardsDetail;
