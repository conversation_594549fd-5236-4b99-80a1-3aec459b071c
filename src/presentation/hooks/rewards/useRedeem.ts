import React from "react";

import { usePoint, usePointGoal } from "../point";

import { useRewardRedeemQueries } from "@/data/queries/rewardsQueries";
import { ShowBottomSheetObs } from "@/data/services/observable";

const useRedeem = () => {
    const { fetchRewardRedeem, isLoading } = useRewardRedeemQueries();
    const { updateTotalPoint } = usePoint();
    const { getPointGoal, isLoading: isLoadingPointGoal } = usePointGoal();

    const handleRedeem = React.useCallback(
        async (id: number) => {
            const result = await fetchRewardRedeem(id);
            if (result?.ok) {
                const isSuccess = await getPointGoal();
                if (!isSuccess) return;

                updateTotalPoint(result?.data?.user_point);
                ShowBottomSheetObs.action({
                    title: "Yay! You got the voucher",
                    message: "Check it out on your My Voucher page",
                    titleButtonConfirm: "Okay"
                });
            }
        },
        [fetchRewardRedeem, getPointGoal, updateTotalPoint]
    );

    return {
        handleRedeem,
        isLoading: isLoadingPointGoal || isLoading
    };
};

export default useRedeem;
