import React from "react";

import { phoneNumberValidatingSubject } from "@/shared/validations";

const usePhoneValidation = () => {
    const [isValidating, setIsValidating] = React.useState(false);

    React.useEffect(() => {
        const subscription = phoneNumberValidatingSubject.subscribe((validating) => {
            setIsValidating(validating);
        });

        return () => {
            subscription.unsubscribe();
        };
    }, []);

    return {
        isValidating
    };
};

export default usePhoneValidation;
