import { useNavigation } from "@react-navigation/native";
import React from "react";
import { Keyboard } from "react-native";
import { OtpInputRef } from "react-native-otp-entry";
import { object } from "yup";

import { userApi } from "@/data/api";

import { useUserQueries } from "@/data/queries";

import useCountDown from "../useCountDown";
import { useForm } from "../useForm";
import useRouteParams from "../useRouteParams";

import { RouteName } from "@/shared/constants";
import { otpSchema } from "@/shared/validations";

const useVerification = () => {
    const params = useRouteParams<typeof RouteName.VerificationEmail>();
    const { getUser } = useUserQueries();

    const { isEnable, resetTime, timeFormatted } = useCountDown(600);

    const otpRef = React.useRef<OtpInputRef>(null);
    const [isLoadingOtp, setIsLoadingOtp] = React.useState(false);

    const navigation = useNavigation();

    const onSubmit = React.useCallback(
        async (values: { otp: string }, resetForm: () => void) => {
            Keyboard.dismiss();
            try {
                setIsLoadingOtp(true);
                const res = await userApi.verifyEmail(values.otp);
                if (!res?.ok) return;
                await getUser();
                otpRef.current?.clear();
                resetForm();
                navigation.goBack();
            } catch (error) {
                //  Alert.alert("Error", `Error: ${error}`);
                /* empty */
            } finally {
                setIsLoadingOtp(false);
            }
        },
        [navigation, getUser]
    );

    const { getInputProps, handleSubmit, formik } = useForm({
        enableReinitialize: true,
        initialValues: {
            otp: ""
        },
        validationSchema: object().shape({
            otp: otpSchema
        }),
        onSubmit: (values, formikHelpers) => onSubmit(values, formikHelpers.resetForm)
    });

    const handleResendOtp = React.useCallback(async () => {
        if (!isEnable) return;

        try {
            setIsLoadingOtp(true);
            const res = await userApi.postSendEmailVerification();
            if (!res?.ok) return;
            resetTime();
            otpRef.current?.clear();
            formik.resetForm();
        } catch (error) {
            /* empty */
        } finally {
            setIsLoadingOtp(false);
        }
    }, [formik, isEnable, resetTime]);

    const handleSendOtp = React.useCallback(async () => {
        try {
            setIsLoadingOtp(true);
            const res = await userApi.postSendEmailVerification();
            if (!res?.ok) return;
            navigation.navigate(RouteName.Auth, { screen: RouteName.VerificationEmail });
        } catch (error) {
            /* empty */
        } finally {
            setIsLoadingOtp(false);
        }
    }, [navigation]);

    return {
        isEnable,
        timeFormatted,
        otpRef,
        isLoadingOtp,
        getInputProps,
        handleSubmit,
        handleResendOtp,
        params,
        navigation,
        handleSendOtp
    };
};

export default useVerification;
