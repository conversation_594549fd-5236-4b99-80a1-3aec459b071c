import { useNavigation } from "@react-navigation/native";
import React from "react";
import { Keyboard } from "react-native";

import { useRegisterStore } from "@/app/store";

import useRouteParams from "../useRouteParams";

import { FirebaseAuthService } from "@/data/services/firebase";
import { CreateProfileRefType } from "@/presentation/screens/auth/register/CreateProfile";
import { EmailAndPasswordRefType } from "@/presentation/screens/auth/register/EmailAndPassword";
import { PhoneNumberRefType } from "@/presentation/screens/auth/register/PhoneNumber";
import { RouteName } from "@/shared/constants";

type FormRefType = CreateProfileRefType | EmailAndPasswordRefType | PhoneNumberRefType | null;
type StepFormRef = React.RefObject<FormRefType>;

const useRegister = () => {
    const params = useRouteParams<typeof RouteName.Register>();
    const navigation = useNavigation();

    const isSchool = params?.isSchool;
    const [isChecked, setIsChecked] = React.useState<boolean>(false);
    const [step, setStep] = React.useState<number>(0);
    const [isLoadingSendCode, setIsLoadingSendCode] = React.useState<boolean>(false);

    const { request, clearRegister } = useRegisterStore();

    const createProfileRef = React.useRef<CreateProfileRefType | null>(null);
    const emailAndPasswordRef = React.useRef<EmailAndPasswordRefType | null>(null);
    const phoneNumberRef = React.useRef<PhoneNumberRefType | null>(null);

    React.useEffect(() => {
        clearRegister();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const formRefs = React.useMemo<StepFormRef[]>(
        () => [createProfileRef, emailAndPasswordRef, ...(isSchool ? [] : [phoneNumberRef])],
        [isSchool]
    );

    const handleStep = React.useCallback(
        (stepNumber: number) => () => {
            setStep(stepNumber);
        },
        []
    );

    const previousStep = React.useCallback(() => {
        if (step > 0) {
            setStep((prev) => prev - 1);
        }
    }, [step]);

    const handleNext = React.useCallback(() => {
        if (step < formRefs.length) {
            formRefs[step].current?.onSubmit();
        }
    }, [step, formRefs]);

    const navigateToVerification = React.useCallback(async () => {
        const { request: currentRequest } = useRegisterStore.getState();
        const phoneNumber = currentRequest?.individual?.phone;

        if (!phoneNumber) return;

        try {
            setIsLoadingSendCode(true);

            const isSent = await FirebaseAuthService.sendOTP(phoneNumber);
            if (!isSent) return;

            navigation.navigate(RouteName.Verification, {
                phoneNumber,
                type: "register"
            });
        } catch (e) {
            /* empty */
        } finally {
            setIsLoadingSendCode(false);
        }
    }, [navigation]);

    const handleRegisterSchool = React.useCallback(async () => {
        const { request: currentRequest } = useRegisterStore.getState();
        const phoneNumber = currentRequest?.school?.phone;

        if (!phoneNumber) return;

        try {
            setIsLoadingSendCode(true);

            const isSent = await FirebaseAuthService.sendOTP(phoneNumber);
            if (!isSent) return;

            navigation.navigate(RouteName.Verification, {
                phoneNumber,
                type: "registerSchool"
            });
        } catch (e) {
            /* empty */
        } finally {
            setIsLoadingSendCode(false);
        }
    }, [navigation]);

    const nextStep = React.useCallback(async () => {
        if (step === formRefs.length - 1) {
            Keyboard.dismiss();
            if (isSchool) {
                return handleRegisterSchool();
            }
            await navigateToVerification();
            return;
        }

        setStep((prev) => prev + 1);
    }, [step, formRefs.length, isSchool, navigateToVerification, handleRegisterSchool]);

    const shouldShowCheckbox = React.useMemo(() => {
        return (isSchool && step === 1) || step === 2;
    }, [isSchool, step]);

    const isLoadingPage = React.useMemo(() => isLoadingSendCode, [isLoadingSendCode]);

    const isNextButtonDisabled = React.useMemo(() => {
        if (isSchool && step === 1) {
            return !(isChecked && request?.school);
        }
        if (step === 2) {
            return !(isChecked && request?.individual);
        }
        return false;
    }, [isSchool, step, isChecked, request?.school, request?.individual]);

    return {
        step,
        nextStep,
        previousStep,
        isSchool,
        isChecked,
        setIsChecked,
        shouldShowCheckbox,
        isLoadingPage,
        isNextButtonDisabled,
        request,
        handleNext,
        handleStep,
        createProfileRef,
        emailAndPasswordRef,
        phoneNumberRef
    };
};

export default useRegister;
