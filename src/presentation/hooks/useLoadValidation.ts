import React from "react";

import {
    emailValidatingSubject,
    nicknameValidatingSubject,
    phoneNumberValidatingSubject,
    postalCodeValidatingSubject
} from "@/shared/validations";

const useLoadValidation = () => {
    const [isValidating, setIsValidating] = React.useState(false);

    React.useEffect(() => {
        const subscription = phoneNumberValidatingSubject.subscribe((validating) => {
            setIsValidating(validating);
        });

        return () => {
            subscription.unsubscribe();
        };
    }, []);

    React.useEffect(() => {
        const subscription = emailValidatingSubject.subscribe((validating) => {
            setIsValidating(validating);
        });

        return () => {
            subscription.unsubscribe();
        };
    }, []);

    React.useEffect(() => {
        const subscription = postalCodeValidatingSubject.subscribe((validating) => {
            setIsValidating(validating);
        });

        return () => {
            subscription.unsubscribe();
        };
    }, []);

    React.useEffect(() => {
        const subscription = nicknameValidatingSubject.subscribe((validating) => {
            setIsValidating(validating);
        });

        return () => {
            subscription.unsubscribe();
        };
    }, []);

    return {
        isValidating
    };
};

export default useLoadValidation;
