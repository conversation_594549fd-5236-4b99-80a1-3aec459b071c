import { useFocusEffect, useNavigation } from "@react-navigation/native";
import React from "react";
import { Vibration } from "react-native";
import { Camera, Code, useCameraDevice } from "react-native-vision-camera";

import { useBinDetail } from "../bin";
import { useCameraPermission } from "../camera";
import { useLocationServices } from "../useLocationServices";
import useRouteParams from "../useRouteParams";

import { ShowBottomSheetObs, walkthroughService, WalkthroughStep } from "@/data/services/observable";
import { BottomSheetCustomRef } from "@/presentation/components/bottomSheet";
import { getFramePosition } from "@/presentation/components/frame";
import { BinTypes, EndPoint, ImageAssets, QRScanConstants, RouteName } from "@/shared/constants";
import { BinTypeKey } from "@/shared/constants/BinTypes";
import { calculateDistance, fullHeight, fullWidth, isIos } from "@/shared/helper";

type AcceptedItemType = {
    name: string;
    icon: any;
};

export type BinInfoType = {
    [key in BinTypeKey]: {
        name: string;
        acceptedItems: AcceptedItemType[];
    };
};

const useQRScan = () => {
    const navigation = useNavigation();
    const params = useRouteParams<typeof RouteName.Scan>();
    const { isLoading, getBinDetail } = useBinDetail();
    const { permissionState, requestPermission, onError } = useCameraPermission();
    const { getCurrentLocation } = useLocationServices();
    const hasPermission = permissionState.isGranted;
    const device = useCameraDevice("back");

    const [isActive, setIsActive] = React.useState(true);
    const [isTorchOn, setIsTorchOn] = React.useState(false);
    const [isProcessingQR, setIsProcessingQR] = React.useState(false);
    const [isWalkthroughVisible, setIsWalkthroughVisible] = React.useState(false);
    const [selectedBinType, setSelectedBinType] = React.useState<BinTypeKey>("ictBulbBattery");
    const [binImage, setBinImage] = React.useState(ImageAssets.ictBulbEwasteBins);
    const [isAcknowledged, setIsAcknowledged] = React.useState(false);
    const [binId, setBinId] = React.useState<number | null>(null);
    const [binData, setBinData] = React.useState<BinDetailResponse | null>(null);
    const [hasShownLocationError, setHasShownLocationError] = React.useState(false);
    const [isShowingError, setIsShowingError] = React.useState(false);

    const bottomSheetRef = React.useRef<BottomSheetCustomRef>(null);
    const processingTimerRef = React.useRef<NodeJS.Timeout | null>(null);
    const cameraRef = React.useRef<Camera>(null);

    const framePosition = React.useMemo(() => getFramePosition(), []);
    const binInfo = React.useMemo(() => BinTypes.binInfo, []);

    const isCameraActive = React.useMemo(() => {
        return isActive && !isProcessingQR && !isLoading && !isShowingError;
    }, [isActive, isProcessingQR, isLoading, isShowingError]);

    const safeVibrate = React.useCallback(() => {
        try {
            Vibration.vibrate(100);
        } catch (error) {
            /* empty */
        }
    }, []);

    const getBinImageForType = React.useCallback((binType: BinTypeKey) => BinTypes.getBinImageForType(binType), []);

    const isPointInsideFrame = React.useCallback(
        (point: { x: number; y: number }) => {
            const padding = Math.min(fullWidth, fullHeight) * 0.1;

            return (
                point.x >= framePosition.left - padding &&
                point.x <= framePosition.left + framePosition.width + padding &&
                point.y >= framePosition.top - padding &&
                point.y <= framePosition.top + framePosition.height + padding
            );
        },
        [framePosition]
    );

    const showErrorBottomSheet = React.useCallback(
        (title: string, message: string) => {
            if (isShowingError) return;

            setIsProcessingQR(false);
            setIsActive(false);
            setIsShowingError(true);

            ShowBottomSheetObs.action({
                title,
                message,
                titleButtonConfirm: "Try Again",
                closeOnDragDown: false,
                closeOnPressMask: false,
                onConfirm: () => {
                    setIsProcessingQR(false);
                    setIsActive(true);
                    setIsShowingError(false);
                    if (title.includes("Location")) {
                        setHasShownLocationError(false);
                    }
                }
            });
        },
        [isShowingError]
    );

    const updateBinState = React.useCallback((binResponse: BinDetailResponse) => {
        setBinData(binResponse);
        setBinId(binResponse.id);

        const binTypeKey = BinTypes.determineBinType(
            binResponse.bin_type_id,
            binResponse.e_waste_bin_type_id
        ) as BinTypeKey;

        setSelectedBinType(binTypeKey);
        setBinImage(getBinImageForType(binTypeKey));
        setIsAcknowledged(false);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const fetchBinDataWithToken = React.useCallback(async (url: string) => {
        try {
            const binDetailEndpoint = EndPoint.bin.scan.replace("{qrcode_value}", url);
            const response = await getBinDetail(binDetailEndpoint);

            if (!response) {
                showErrorBottomSheet(
                    "QR Code Error",
                    "Unable to read QR code data. Please try again with a valid QR code."
                );
                return null;
            }

            const isRecyclingUrl = QRScanConstants.RECYCLING_URL_KEYWORDS.some((keyword) =>
                url.toLowerCase().includes(keyword)
            );

            const isRecyclingResponse = (resp: BinDetailResponse | RecyclingResponse): resp is RecyclingResponse => {
                return resp && "additional_point" in resp && "total_point" in resp;
            };

            if (isRecyclingUrl || isRecyclingResponse(response)) {
                navigation.navigate(RouteName.SubmittedRedeem, { result: response as RecyclingResponse });
                return response;
            }

            const binResponse = response as BinDetailResponse;
            const currentLocation = await getCurrentLocation();
            if (!currentLocation) {
                if (!hasShownLocationError) {
                    showErrorBottomSheet("Location Error", "Unable to get your current location. Please try again.");
                }
                return null;
            }

            if (binResponse.lat && binResponse.long && binResponse.map_radius) {
                const userLat = currentLocation.latitude;
                const userLon = currentLocation.longitude;
                const binLat = parseFloat(binResponse.lat);
                const binLon = parseFloat(binResponse.long);
                const binRadius = parseFloat(binResponse.map_radius);

                const distance = calculateDistance(userLat, userLon, binLat, binLon);
                const distanceInMeters = distance * 1000;

                if (distanceInMeters > binRadius) {
                    const formatDistance = (meters: number) => {
                        if (meters >= 1000) {
                            return `${(meters / 1000).toFixed(1)}km`;
                        }
                        return `${meters}m`;
                    };

                    showErrorBottomSheet(
                        "Location Restriction",
                        `You are outside the allowed scanning area. Maximum distance: ${formatDistance(binRadius)}. Currently: ${formatDistance(distanceInMeters)}`
                    );
                    return null;
                }
            } else {
                showErrorBottomSheet(
                    "Location Error",
                    "Bin location data or radius is not available. Please contact support."
                );
                return null;
            }

            updateBinState(binResponse);
            bottomSheetRef.current?.open();

            return binResponse;
        } catch (error) {
            showErrorBottomSheet(
                "Connection Error",
                "Unable to connect to server. Please check your internet connection and try again."
            );
            return null;
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const processQrCode = React.useCallback(
        (scannedCode: Code) => {
            if (isProcessingQR || isShowingError) return;

            setIsProcessingQR(true);
            setIsActive(false);
            safeVibrate();

            if (scannedCode.value) {
                fetchBinDataWithToken(scannedCode.value);
            } else {
                showErrorBottomSheet("QR Code Error", "Unable to read QR code. Please try scanning again.");
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [isProcessingQR, isShowingError]
    );

    const shouldProcessCode = React.useCallback(
        (scannedCode: Code): boolean => {
            if (isIos) {
                return true;
            }

            const { frame } = scannedCode;
            const cornerPoints = (scannedCode as any).cornerPoints as { x: number; y: number }[] | undefined;

            if (cornerPoints && cornerPoints.length > 0) {
                return cornerPoints.some((point) => isPointInsideFrame(point));
            }

            if (frame) {
                const centerX = frame.x + frame.width / 2;
                const centerY = frame.y + frame.height / 2;
                return isPointInsideFrame({ x: centerX, y: centerY });
            }

            return false;
        },
        [isPointInsideFrame]
    );

    const handleCodeScanned = React.useCallback(
        (codes: Code[]) => {
            if (codes.length > 0 && isActive && !isProcessingQR && !isLoading && !isShowingError) {
                const scannedCode = codes[0];

                if (shouldProcessCode(scannedCode)) {
                    processQrCode(scannedCode);
                }
            }
        },
        [isActive, isProcessingQR, isLoading, isShowingError, processQrCode, shouldProcessCode]
    );

    const handleCloseWalkthrough = React.useCallback(() => {
        setIsWalkthroughVisible(false);
        walkthroughService.hideWalkthrough();
    }, []);

    const handleCloseBottomSheet = React.useCallback(() => {
        setIsActive(true);
        setIsProcessingQR(false);
        setBinData(null);
    }, []);

    const handleTurnOnOffLight = React.useCallback(() => {
        setIsTorchOn((prev) => !prev);
    }, []);

    const handleSelect = React.useCallback(() => {
        setIsProcessingQR(true);
        setIsActive(false);

        if (processingTimerRef.current) {
            clearTimeout(processingTimerRef.current);
            processingTimerRef.current = null;
        }

        setTimeout(() => {
            navigation.navigate(RouteName.ScanStack, {
                screen: RouteName.TookPhoto,
                params: { binId }
            });
            bottomSheetRef.current?.close();
        }, 100);
    }, [binId, navigation]);

    // TODO: for testing
    const nextScreen = React.useCallback(
        () =>
            navigation.navigate(RouteName.ScanStack, {
                screen: RouteName.TookPhoto,
                params: { binId: 44 }
            }),
        [navigation]
    );

    useFocusEffect(
        React.useCallback(() => {
            if (!hasPermission) return;

            const initWalkthrough = async () => {
                if (params?.fromWalkthrough) {
                    setIsWalkthroughVisible(true);
                } else if (params?.fromWalkthroughBack) {
                    const timer = setTimeout(() => {
                        setIsWalkthroughVisible(true);
                    }, 300);
                    return () => clearTimeout(timer);
                }
            };
            initWalkthrough();
        }, [hasPermission, params?.fromWalkthrough, params?.fromWalkthroughBack])
    );

    React.useEffect(() => {
        const subscription = walkthroughService.walkthrough$.subscribe((event) => {
            if (event.show && event.currentStep === WalkthroughStep.SCAN) {
                setIsWalkthroughVisible(true);
            }
        });

        return () => {
            subscription.unsubscribe();
        };
    }, []);

    React.useEffect(() => {
        const checkLocationAccess = async () => {
            const currentLocation = await getCurrentLocation();
            if (!currentLocation && !hasShownLocationError) {
                setHasShownLocationError(true);
                showErrorBottomSheet(
                    "Location Required",
                    "Location access is required to scan QR codes. Please enable location services and grant permission to continue."
                );
            }
        };

        checkLocationAccess();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useFocusEffect(
        React.useCallback(() => {
            const timer = setTimeout(() => {
                setIsActive(true);
                setIsProcessingQR(false);
                setIsShowingError(false);
                setHasShownLocationError(false);
            }, 300);

            return () => {
                clearTimeout(timer);
                setIsActive(false);
            };
        }, [])
    );

    React.useEffect(() => {
        return () => {
            if (processingTimerRef.current) {
                clearTimeout(processingTimerRef.current);
            }
            setIsActive(false);
        };
    }, []);

    return {
        isLoading,
        isActive: isCameraActive,
        isTorchOn,
        isWalkthroughVisible,
        device,
        cameraRef,
        hasPermission,
        requestPermission,
        onError,
        handleCodeScanned,
        handleCloseWalkthrough,
        handleCloseBottomSheet,
        handleTurnOnOffLight,
        handleSelect,
        nextScreen,
        bottomSheetRef,
        binInfo,
        selectedBinType,
        binImage,
        isAcknowledged,
        setIsAcknowledged,
        binData,
        isProcessingQR
    };
};

export default useQRScan;
