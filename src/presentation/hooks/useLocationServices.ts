import * as Linking from "expo-linking";
import * as Location from "expo-location";
import React from "react";
import { Alert, Platform } from "react-native";

import { calculateFormattedDistance, isAndroid } from "@/shared/helper";

const LOCATION_CACHE_TIMEOUT = 30000; // 30 seconds
const LOCATION_TIMEOUT = 15000; // 15 seconds timeout for Android

export const useLocationServices = () => {
    const [isLoadingLocation, setIsLoadingLocation] = React.useState(false);
    const [userLocation, setUserLocation] = React.useState<{ longitude: number; latitude: number } | null>(null);
    const [locationError, setLocationError] = React.useState<string | null>(null);
    const lastLocationTimeRef = React.useRef<number>(0);
    const locationCacheRef = React.useRef<Location.LocationObject | null>(null);

    const openAppSettings = React.useCallback(async (): Promise<void> => {
        try {
            if (Platform.OS === "ios") {
                await Linking.openURL("app-settings:");
            } else {
                await Linking.openSettings();
            }
        } catch (error) {
            console.error("Error opening app settings:", error);
        }
    }, []);

    const showLocationAlert = React.useCallback(
        (title: string, message: string) => {
            Alert.alert(title, message, [
                { text: "Cancel", style: "cancel" },
                { text: "Open Settings", onPress: openAppSettings }
            ]);
        },
        [openAppSettings]
    );

    const getCurrentLocation = React.useCallback(async (): Promise<{ longitude: number; latitude: number } | null> => {
        const now = Date.now();

        if (locationCacheRef.current && now - lastLocationTimeRef.current < LOCATION_CACHE_TIMEOUT) {
            return {
                longitude: locationCacheRef.current.coords.longitude,
                latitude: locationCacheRef.current.coords.latitude
            };
        }

        try {
            setIsLoadingLocation(true);
            setLocationError(null);

            const { status } = await Location.getForegroundPermissionsAsync();
            if (status !== "granted") {
                const { status: newStatus } = await Location.requestForegroundPermissionsAsync();
                if (newStatus !== "granted") {
                    setLocationError("Location permission denied");
                    showLocationAlert(
                        "Location Permission Required",
                        "This app needs location access to show nearby recycling bins. Please enable location permission in app settings."
                    );
                    return null;
                }
            }

            const isLocationEnabled = await Location.hasServicesEnabledAsync();
            if (!isLocationEnabled) {
                setLocationError("Location services are disabled");
                showLocationAlert(
                    "Location Services Disabled",
                    "Location services are turned off on your device. Please enable location services to use this app effectively."
                );
                return null;
            }

            if (isAndroid) {
                try {
                    const lastKnownLocation = await Location.getLastKnownPositionAsync();
                    if (lastKnownLocation) {
                        const coords = {
                            longitude: lastKnownLocation.coords.longitude,
                            latitude: lastKnownLocation.coords.latitude
                        };

                        locationCacheRef.current = lastKnownLocation;
                        lastLocationTimeRef.current = now;

                        setUserLocation(coords);
                        return coords;
                    }
                } catch (error) {
                    /* empty */
                }
            }

            const locationOptions: Location.LocationOptions = Platform.select({
                android: {
                    accuracy: Location.Accuracy.Low,
                    timeInterval: 5000,
                    distanceInterval: 50
                },
                ios: {
                    accuracy: Location.Accuracy.Balanced,
                    timeInterval: 1000,
                    distanceInterval: 10
                },
                default: {
                    accuracy: Location.Accuracy.Balanced,
                    timeInterval: 1000,
                    distanceInterval: 10
                }
            });

            let locationPromise = Location.getCurrentPositionAsync(locationOptions);

            if (isAndroid) {
                const timeoutPromise = new Promise<never>((_, reject) => {
                    setTimeout(() => reject(new Error("Location timeout")), LOCATION_TIMEOUT);
                });

                locationPromise = Promise.race([locationPromise, timeoutPromise]);
            }

            const location = await locationPromise;

            locationCacheRef.current = location;
            lastLocationTimeRef.current = now;

            const coords = {
                longitude: location.coords.longitude,
                latitude: location.coords.latitude
            };

            setUserLocation(coords);
            return coords;
        } catch (error) {
            console.error("Location error:", error);

            if (isAndroid && error instanceof Error && error.message === "Location timeout") {
                try {
                    const fallbackLocation = await Location.getCurrentPositionAsync({
                        accuracy: Location.Accuracy.Lowest,
                        timeInterval: 10000,
                        distanceInterval: 100
                    });

                    locationCacheRef.current = fallbackLocation;
                    lastLocationTimeRef.current = Date.now();

                    const coords = {
                        longitude: fallbackLocation.coords.longitude,
                        latitude: fallbackLocation.coords.latitude
                    };

                    setUserLocation(coords);
                    return coords;
                } catch (fallbackError) {
                    console.error("Fallback location also failed:", fallbackError);
                }
            }

            const errorMessage = error instanceof Error ? error.message : "Unable to get current location";
            setLocationError(errorMessage);
            return null;
        } finally {
            setIsLoadingLocation(false);
        }
    }, [showLocationAlert]);

    React.useEffect(() => {
        getCurrentLocation();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const calculatedDistance = React.useCallback(
        (lat?: string, long?: string) => {
            if (userLocation && lat && long) {
                return calculateFormattedDistance(
                    userLocation.latitude,
                    userLocation.longitude,
                    parseFloat(lat),
                    parseFloat(long)
                );
            }
            return 0;
        },
        [userLocation]
    );

    return {
        isLoadingLocation,
        userLocation,
        locationError,
        getCurrentLocation,
        calculatedDistance
    };
};
