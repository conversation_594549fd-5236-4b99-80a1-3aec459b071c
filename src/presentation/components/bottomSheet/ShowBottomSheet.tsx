import { useNavigation } from "@react-navigation/native";
import React from "react";

import { MyButton } from "../myButton";
import { Box, HStack, Text, VStack } from "../ui";

import BinPress from "./BinPress";
import BottomSheetCustom, { BottomSheetCustomRef } from "./BottomSheetCustom";
import { usePayload } from "./ShowBottomSheet.Hook";

import { ImageAssets, RouteName, TypeBottomSheet } from "@/shared/constants";

const ShowBottomSheet = () => {
    const { payload, isOpen } = usePayload();
    const navigation = useNavigation();

    const bottomSheetRef = React.useRef<BottomSheetCustomRef>(null);

    const handleIndividual = React.useCallback(() => {
        navigation.navigate(RouteName.Auth, { screen: RouteName.Register });
        bottomSheetRef.current?.close();
    }, [navigation]);

    const handleSchools = React.useCallback(() => {
        navigation.navigate(RouteName.Auth, { screen: RouteName.WelcomeSchools });
        bottomSheetRef.current?.close();
    }, [navigation]);

    React.useEffect(() => {
        if (!isOpen || !payload) return;
        bottomSheetRef.current?.open();
    }, [isOpen, payload]);

    const handleConfirm = React.useCallback(() => {
        if (!payload) return;
        payload.onConfirm?.();
        bottomSheetRef.current?.close();
    }, [payload]);

    const handleCancel = React.useCallback(() => {
        if (!payload) return;
        payload.onCancel?.();
        bottomSheetRef.current?.close();
    }, [payload]);

    const renderContent = React.useMemo(() => {
        switch (payload?.type) {
            case TypeBottomSheet.REGISTER:
                return (
                    <>
                        <Text className="text-[22px] font-bold">Do you want to start as</Text>
                        <HStack space="md" className="w-full items-center h-full justify-center flex-1">
                            <Box className="flex-1 items-center">
                                <MyButton
                                    text="Individual"
                                    variant="secondary"
                                    onPress={handleIndividual}
                                    icon={ImageAssets.icIndividual}
                                    titleSize={13}
                                />
                            </Box>
                            <Box className="flex-1 items-center">
                                <MyButton
                                    text="Schools/Organizations"
                                    variant="secondary"
                                    onPress={handleSchools}
                                    icon={ImageAssets.icSchool}
                                    titleSize={13}
                                />
                            </Box>
                        </HStack>
                    </>
                );
            case TypeBottomSheet.BIN_PRESS:
                return <BinPress binId={payload?.binId} />;
            default:
                if (!payload) return null;
                if (payload.titleButtonCancel && payload.titleButtonConfirm) {
                    return (
                        <VStack className="w-full flex-1" space="2xl">
                            <Text className="font-bold">{payload.title}</Text>
                            <Text className="text-neutralGray">{payload.message}</Text>
                            <VStack className="w-full items-center h-full justify-center flex-1 gap-y-2">
                                <MyButton text={payload.titleButtonConfirm} onPress={handleCancel} height={44} />
                                <MyButton
                                    text={payload.titleButtonCancel}
                                    onPress={handleConfirm}
                                    height={44}
                                    variant="secondary"
                                />
                            </VStack>
                        </VStack>
                    );
                }
                if (payload.title && payload.message) {
                    return (
                        <VStack className="w-full flex-1" space="2xl">
                            <Text className="font-bold">{payload.title}</Text>
                            <Text className="text-neutralGray">{payload.message}</Text>
                            <MyButton text={payload.titleButtonConfirm} onPress={handleConfirm} />
                        </VStack>
                    );
                }
                if (payload.onConfirm) {
                    return (
                        <VStack className="w-full flex-1" space="2xl">
                            <Text className="font-bold">{payload.title}</Text>
                            <Text>{payload.message}</Text>
                            <MyButton text={payload.titleButtonConfirm} onPress={handleConfirm} />
                        </VStack>
                    );
                }
        }
    }, [payload, handleIndividual, handleSchools, handleCancel, handleConfirm]);

    const height = React.useMemo(() => {
        if (payload?.height) return payload.height;
        switch (payload?.type) {
            case TypeBottomSheet.BIN_PRESS:
                return 500;
            default:
                return 210;
        }
    }, [payload]);

    return (
        <BottomSheetCustom height={height} ref={bottomSheetRef} closeOnPressMask={payload?.closeOnPressMask}>
            <VStack className="w-full flex-1 px-5 pb-5">{renderContent}</VStack>
        </BottomSheetCustom>
    );
};

export default ShowBottomSheet;
