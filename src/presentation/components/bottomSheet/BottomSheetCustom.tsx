import React from "react";
import { StyleSheet } from "react-native";
import Animated, {
    Easing,
    Extrapolation,
    interpolate,
    runOnJS,
    useAnimatedStyle,
    useSharedValue,
    withSpring,
    withTiming
} from "react-native-reanimated";

import { Notch } from "@/presentation/components/notch";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box } from "@/presentation/components/ui";

const BOTTOM_SHEET_STYLES = {
    position: "absolute" as const,
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: "white",
    paddingBottom: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 5,
    zIndex: 2
};

const BACKDROP_STYLES = {
    position: "absolute" as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)"
};

const ANIMATION_CONFIG = {
    duration: 400,
    springDamping: 0.7,
    easing: Easing.bezier(0.25, 0.1, 0.25, 1)
};

export interface BottomSheetCustomRef {
    open: () => void;
    close: () => void;
}

interface BottomSheetCustomProps {
    height?: number;
    onClose?: () => void;
    closeOnPressMask?: boolean;
    children: React.ReactNode;
}

const BottomSheetCustom = React.forwardRef<BottomSheetCustomRef, BottomSheetCustomProps>(
    ({ height = 350, onClose, closeOnPressMask = true, children }, ref) => {
        const [isVisible, setIsVisible] = React.useState(false);
        const [isReady, setIsReady] = React.useState(false);
        const bottomSheetAnim = useSharedValue(height);
        const backdropAnim = useSharedValue(0);
        const closing = React.useRef(false);

        React.useImperativeHandle(ref, () => ({
            open: () => {
                setIsVisible(true);
                closing.current = false;
                setTimeout(() => {
                    bottomSheetAnim.value = withSpring(
                        0,
                        {
                            damping: 15,
                            stiffness: 100
                        },
                        (finished) => {
                            if (finished) runOnJS(setIsReady)(true);
                        }
                    );
                    backdropAnim.value = withTiming(1, {
                        duration: ANIMATION_CONFIG.duration,
                        easing: ANIMATION_CONFIG.easing
                    });
                }, 50);
            },
            close: () => {
                if (closing.current) return;
                closing.current = true;
                setIsReady(false);
                bottomSheetAnim.value = withSpring(height, {
                    damping: 15,
                    stiffness: 100
                });
                backdropAnim.value = withTiming(0, {
                    duration: ANIMATION_CONFIG.duration,
                    easing: ANIMATION_CONFIG.easing
                });
                setTimeout(() => {
                    setIsVisible(false);
                    closing.current = false;
                    if (onClose) onClose();
                }, ANIMATION_CONFIG.duration + 50);
            }
        }));

        const handleBackdropPress = () => {
            if (closeOnPressMask) {
                ref && typeof ref !== "function" && ref.current?.close();
            }
        };

        const bottomSheetStyle = useAnimatedStyle(() => {
            const opacity = interpolate(bottomSheetAnim.value, [0, height], [1, 0.3], Extrapolation.CLAMP);
            return {
                transform: [{ translateY: bottomSheetAnim.value }],
                opacity: isReady ? 1 : opacity
            };
        });

        const backdropStyle = useAnimatedStyle(() => {
            return {
                opacity: backdropAnim.value
            };
        });

        if (!isVisible) return null;

        return (
            <Box style={StyleSheet.absoluteFillObject} pointerEvents="box-none">
                <Animated.View style={[BACKDROP_STYLES, backdropStyle]} pointerEvents="auto">
                    <MyTouchable style={{ flex: 1 }} onPress={handleBackdropPress} activeOpacity={1} />
                </Animated.View>
                <Animated.View style={[BOTTOM_SHEET_STYLES, { height }, bottomSheetStyle]} pointerEvents="box-none">
                    <Notch />
                    {children}
                </Animated.View>
            </Box>
        );
    }
);

export default React.memo(BottomSheetCustom);
