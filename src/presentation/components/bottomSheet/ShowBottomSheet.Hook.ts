import React from "react";

import { useDeepEffect } from "@/presentation/hooks";

import { ShowBottomSheetObs } from "@/data/services/observable";
import { TypeBottomSheet } from "@/shared/constants";

export const usePayload = (): {
    isOpen: boolean;
    payload?: ShowBottomSheetObsPayload<TypeBottomSheet>;
} => {
    const [isOpen, setIsOpen] = React.useState<boolean>(false);
    const [payload, setPayload] = React.useState<ShowBottomSheetObsPayload<TypeBottomSheet>>();

    React.useEffect(() => {
        const subscription = ShowBottomSheetObs.subscribe((params: ShowBottomSheetObsPayload<TypeBottomSheet>) => {
            setPayload(params);
        });

        return () => subscription.unsubscribe();
    }, []);

    useDeepEffect(() => {
        if (!payload) return;
        setIsOpen(true);
    }, [isOpen, payload]);

    return React.useMemo(
        () => ({
            isOpen,
            payload
        }),
        [isOpen, payload]
    );
};
