import { useActionSheet } from "@expo/react-native-action-sheet";
import React from "react";

import { getColor, useBinDetail } from "@/presentation/hooks";

import { MyButton } from "@/presentation/components/myButton";
import { Box, HStack, Image, LoadingBox, ScrollView, Text } from "@/presentation/components/ui";
import { useLocationServices } from "@/presentation/hooks/useLocationServices";
import { EndPoint, ImageAssets } from "@/shared/constants";
import { handleGetDirections } from "@/shared/helper";

interface BinPressProps {
    binId?: number;
}

const BinPress: React.FC<BinPressProps> = ({ binId }) => {
    const { isLoading, getBinDetail } = useBinDetail();
    const { calculatedDistance } = useLocationServices();

    const [binInfo, setBinInfo] = React.useState<BinDetailResponse | undefined>(undefined);
    const { showActionSheetWithOptions } = useActionSheet();

    React.useEffect(() => {
        const initBinInfo = async () => {
            if (binId) {
                const binDetailEndpoint = EndPoint.bin.get.replace("{binId}", binId.toString());
                const bin = await getBinDetail(binDetailEndpoint);
                setBinInfo(bin as BinDetailResponse);
            }
        };
        initBinInfo();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [binId]);

    const distance = calculatedDistance(binInfo?.lat, binInfo?.long);

    const handleGetDirectionsPress = React.useCallback(() => {
        handleGetDirections(
            {
                address: binInfo?.address,
                googleMapsUrl: binInfo?.google_maps_url,
                appleMapsUrl: binInfo?.apple_maps_url
            },
            { showActionSheetWithOptions }
        );
    }, [showActionSheetWithOptions, binInfo]);

    return (
        <>
            <ScrollView>
                <Box>
                    <Box className="items-center justify-center">
                        <Box className="w-full h-[350px] overflow-hidden rounded-lg">
                            <Box className="relative h-[280px]">
                                <Image
                                    source={{ uri: binInfo?.type.image_url || "" }}
                                    style={{
                                        width: "100%",
                                        height: "100%"
                                    }}
                                    resizeMode="contain"
                                    alt="Recycle Bin"
                                />
                                <Box
                                    className="absolute bottom-[-40px] overflow-hidden left-0 right-0 items-center bg-grayCard h-[56px]"
                                    borderRadius={12}>
                                    <Box className="flex-1 items-center">
                                        <HStack className="items-center px-2 justify-center flex-1">
                                            <HStack className="flex-1 items-center">
                                                <Image
                                                    source={ImageAssets.icICT}
                                                    className="w-[32px] h-[32px]"
                                                    alt="ICT"
                                                />
                                                <Box className="px-2 flex-1">
                                                    <Text className="text-[13px]">{binInfo?.type.name}</Text>
                                                </Box>
                                            </HStack>
                                            <HStack className="items-center  bg-green px-2 py-2" borderRadius={8}>
                                                <Image
                                                    source={ImageAssets.tablerWalk}
                                                    className="w-[18px] h-[18px]"
                                                    alt="Walk"
                                                />
                                                <Text className="text-white text-[13px]">{distance}</Text>
                                            </HStack>
                                        </HStack>
                                    </Box>
                                </Box>
                            </Box>
                        </Box>
                    </Box>
                </Box>
            </ScrollView>
            <Box>
                <HStack className="items-start gap-x-2">
                    <Image
                        source={ImageAssets.homeOff}
                        className="w-[18px] h-[18px]"
                        tintColor={getColor("neutralGray")}
                        alt="Home"
                    />
                    <Box className="flex-1">
                        <Text> {isLoading ? "Loading..." : binInfo?.address}</Text>
                    </Box>
                </HStack>
            </Box>
            <MyButton text="Get Directions" className="mt-3" height={44} onPress={handleGetDirectionsPress} />
            <LoadingBox isLoading={isLoading} />
        </>
    );
};

export default BinPress;
