import React from "react";

import { MyTouchable } from "../touchable";
import { Box, HStack, Image, Text } from "../ui";

import { formatPoints } from "@/shared/helper";

type VoucherCardProps = {
    item: RewardResponse;
    myVoucher?: boolean;
    onPress?: (id: number) => void;
    enableRedeem?: boolean;
};

const VoucherCard: React.FC<VoucherCardProps> = ({ item, onPress, myVoucher = false, enableRedeem = true }) => {
    const handlePress = React.useCallback(() => {
        if (!enableRedeem || !onPress) return;
        onPress(item.id);
    }, [onPress, item.id, enableRedeem]);

    const renderRedeem = React.useMemo(() => {
        if (myVoucher) {
            return (
                <Box className="bg-darkBlue mt-2 py-3 px-4 rounded-full mx-1 mb-2">
                    <Text className="text-white text-lg font-semibold text-center">View Details</Text>
                </Box>
            );
        }

        if (enableRedeem) {
            return (
                <Box className="bg-darkBlue mt-2 py-3 px-4 rounded-full mx-1 mb-2">
                    <HStack className="items-center justify-center gap-x-2">
                        <Box className="w-6 h-6 rounded-full bg-yellow-400" />
                        <Text className="text-white text-lg font-medium">{formatPoints(item.price)}</Text>
                    </HStack>
                </Box>
            );
        }

        return (
            <Box className="mt-2 py-3 px-4 rounded-full mx-1 mb-2">
                <Text className="text-sm text-red">Fully redeemed this month. More soon!</Text>
            </Box>
        );
    }, [myVoucher, item.price, enableRedeem]);

    return (
        <MyTouchable className="w-full p-2" onPress={handlePress}>
            <Box className=" bg-grayCard rounded-xl overflow-hidden h-[230px]">
                <Box className="rounded-xl overflow-hidden m-3 ">
                    <Box className="h-[150px] items-center justify-center">
                        <Image
                            source={{ uri: item.image_url }}
                            className="w-full h-full"
                            alt={item.name}
                            resizeMode="contain"
                        />
                    </Box>
                </Box>

                {item.label && (
                    <Box
                        className={`${!enableRedeem ? "bg-grayRed" : "bg-lightBlue"} py-1 px-4 rounded-full self-end mx-3 -mt-[25px]`}>
                        <Text className="text-sm font-medium line-clamp-1">{item.label}</Text>
                    </Box>
                )}

                <Box className="absolute bottom-0 left-2 right-2">{renderRedeem}</Box>
            </Box>
        </MyTouchable>
    );
};

export default VoucherCard;
