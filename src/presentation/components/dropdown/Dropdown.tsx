import React from "react";
import { ColorValue, FlatList, Modal, Pressable, Text, TouchableOpacity, View } from "react-native";
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from "react-native-reanimated";

import { IconComponent } from "../ui";

import { createStyles, styles } from "./styles";

export interface DropdownItem {
    name: string;
    value: string | number;
    [key: string]: any;
}

export interface DropdownProps {
    data: DropdownItem[];
    onSelect: (selectedItem: DropdownItem, index: number) => void;
    defaultButtonText?: string;
    defaultValue?: DropdownItem;
    disabled?: boolean;
    textDropdownAlign?: "auto" | "left" | "right" | "center" | "justify" | undefined;
    borderWidth?: number | undefined;
    borderColor?: ColorValue | undefined;
    backgroundColor?: ColorValue;
    suffixIcon?: React.ReactNode;
    placeholderTextColor?: string;
    buttonTextAfterSelection?: (selectedItem: DropdownItem, index: number) => string;
    rowTextForSelection?: (item: DropdownItem, index: number) => string;
    search?: boolean;
    searchPlaceHolder?: string;
}

export type DropdownRef = {
    selectIndex: (index: number) => void;
    getSelectedIndex: () => number;
    reset: () => void;
    getCurrentValue: () => DropdownItem | null;
};

const Dropdown = React.forwardRef<DropdownRef, DropdownProps>(
    (
        {
            textDropdownAlign = "left",
            borderWidth,
            borderColor,
            backgroundColor,
            suffixIcon,
            data = [],
            onSelect,
            defaultButtonText = "Select an option",
            defaultValue,
            disabled = false,
            buttonTextAfterSelection,
            rowTextForSelection
        },
        ref
    ) => {
        const [isOpen, setIsOpen] = React.useState(false);
        const [selectedItem, setSelectedItem] = React.useState<DropdownItem | null>(defaultValue || null);
        const dropdownAnimation = useSharedValue(0);
        const dropdownRef = React.useRef<View>(null);
        const [dropdownPosition, setDropdownPosition] = React.useState({ x: 0, y: 0, width: 0, height: 0 });

        React.useImperativeHandle(ref, () => ({
            selectIndex: (index: number) => {
                if (index >= 0 && index < data.length) {
                    handleSelectItem(data[index], index);
                }
            },
            getSelectedIndex: () => {
                if (!selectedItem) return -1;
                return data.findIndex((item) => item.value === selectedItem.value);
            },
            reset: () => {
                setSelectedItem(null);
            },
            getCurrentValue: () => selectedItem
        }));

        React.useEffect(() => {
            if (defaultValue) {
                setSelectedItem(defaultValue);
            }
        }, [defaultValue]);

        const animatedStyles = useAnimatedStyle(() => {
            return {
                opacity: dropdownAnimation.value,
                transform: [{ translateY: dropdownAnimation.value * 5 }]
            };
        });

        const closeDropdown = React.useCallback(() => {
            dropdownAnimation.value = withTiming(0, { duration: 150 });
            setTimeout(() => setIsOpen(false), 150);
        }, [dropdownAnimation]);

        const toggleDropdown = React.useCallback(() => {
            if (disabled) return;

            if (!isOpen) {
                dropdownRef.current?.measure((x, y, width, height, pageX, pageY) => {
                    setDropdownPosition({ x: pageX, y: pageY + height, width, height });
                    setIsOpen(true);
                    dropdownAnimation.value = withTiming(1, { duration: 200 });
                });
            } else {
                closeDropdown();
            }
        }, [closeDropdown, disabled, dropdownAnimation, isOpen]);

        const handleSelectItem = React.useCallback(
            (item: DropdownItem, index: number) => {
                setSelectedItem(item);
                closeDropdown();
                onSelect && onSelect(item, index);
            },
            [onSelect, closeDropdown]
        );

        const renderDropdownIcon = React.useCallback(
            () => (suffixIcon ? suffixIcon : <IconComponent name="chevron-down" font="feather" size={24} />),
            [suffixIcon]
        );

        const getButtonText = React.useMemo(() => {
            if (!selectedItem) return defaultButtonText;

            if (buttonTextAfterSelection) {
                const index = data.findIndex((item) => item.value === selectedItem.value);
                return buttonTextAfterSelection(selectedItem, index);
            }

            return selectedItem.name;
        }, [buttonTextAfterSelection, data, defaultButtonText, selectedItem]);

        const getRowText = React.useCallback(
            (item: DropdownItem, index: number) => {
                if (rowTextForSelection) {
                    return rowTextForSelection(item, index);
                }
                return item.name;
            },
            [rowTextForSelection]
        );

        const renderItem = React.useCallback(
            ({ item, index }: { item: DropdownItem; index: number }) => (
                <TouchableOpacity
                    style={[styles.row, selectedItem?.value === item.value && styles.selectedRow]}
                    onPress={() => handleSelectItem(item, index)}>
                    <Text style={[styles.rowText, selectedItem?.value === item.value && styles.selectedRowText]}>
                        {getRowText(item, index)}
                    </Text>
                </TouchableOpacity>
            ),
            [selectedItem, handleSelectItem, getRowText]
        );

        return (
            <>
                <TouchableOpacity
                    ref={dropdownRef}
                    activeOpacity={disabled ? 1 : 0.7}
                    style={createStyles.button(borderWidth, borderColor, backgroundColor, disabled)}
                    onPress={toggleDropdown}
                    disabled={disabled}>
                    <Text style={createStyles.buttonText(textDropdownAlign, !!selectedItem)}>{getButtonText}</Text>
                    {renderDropdownIcon()}
                </TouchableOpacity>

                <Modal visible={isOpen} transparent animationType="none" onRequestClose={closeDropdown}>
                    <Pressable style={styles.modalOverlay} onPress={closeDropdown}>
                        <Animated.View
                            style={[
                                styles.dropdown,
                                animatedStyles,
                                {
                                    top: dropdownPosition.y,
                                    left: dropdownPosition.x,
                                    width: dropdownPosition.width,
                                    maxHeight: 250
                                }
                            ]}>
                            <FlatList
                                data={data}
                                keyExtractor={(item, index) => `dropdown-item-${index}`}
                                renderItem={renderItem}
                            />
                        </Animated.View>
                    </Pressable>
                </Modal>
            </>
        );
    }
);

export default React.memo(Dropdown);
