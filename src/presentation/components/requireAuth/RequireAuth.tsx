import { useNavigation } from "@react-navigation/native";
import React from "react";

import { MyButton } from "../myButton";
import { MyTouchable } from "../touchable";
import { HStack, Text, VStack } from "../ui";

import { ShowBottomSheetObs } from "@/data/services/observable";
import { RouteName, TypeBottomSheet } from "@/shared/constants";

const RequireAuth = () => {
    const navigation = useNavigation();
    const handleGetStarted = React.useCallback(() => {
        ShowBottomSheetObs.action({
            type: TypeBottomSheet.REGISTER
        });
    }, []);

    const handleLogin = React.useCallback(() => {
        navigation.navigate(RouteName.Auth, { screen: RouteName.Login });
    }, [navigation]);
    return (
        <VStack className="flex-1 items-center justify-center px-8" space="3xl">
            <VStack space="xl" className="items-center">
                <Text className="text-[30px] font-mono text-center">Login Required</Text>
                <Text className="text-center text-[16px] text-black/70 font-mono leading-[24px]">
                    Please create an account or login to continue with this action.
                </Text>
            </VStack>
            <VStack className="w-full">
                <MyButton text="Login" variant="outline" onPress={handleLogin} height={54} />
                <HStack className="justify-center pt-5">
                    <Text className="text-gray-600 text-base text-center leading-6 mb-8 px-2">
                        Don&apos;t have an account?
                    </Text>
                    <MyTouchable onPress={handleGetStarted}>
                        <Text className="text-blue text-[15px] font-bold">Sign Up</Text>
                    </MyTouchable>
                </HStack>
            </VStack>
        </VStack>
    );
};

export default RequireAuth;
