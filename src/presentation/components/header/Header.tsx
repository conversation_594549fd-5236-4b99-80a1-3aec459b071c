import React from "react";
import { ColorValue, ImageSourcePropType } from "react-native";

import { getColor } from "@/presentation/hooks";

import { Input } from "../input";
import { ButtonBack } from "../myButton";
import { MyTouchable } from "../touchable";
import { Box, HStack, IconComponent, Image, Text } from "../ui";

import { ImageAssets } from "@/shared/constants";

type HeaderCommonProps = {
    title?: React.ReactNode | string;
    rightComponent?: React.ReactNode | string | ImageSourcePropType;
    isShowBack?: boolean;
    tintColor?: ColorValue;
    onPress?: () => void;
    titleColor?: string;
    icBack?: ImageSourcePropType;
    onBack?: () => void;
    editable?: boolean;
};

type HeaderSearchProps = {
    type: "search" | "searchArea";
    handleFilterPress?: () => void;
    handleSearchSubmit?: () => void;
    searchQuery?: string;
    setSearchQuery?: (text: string) => void;
    placeholder?: string;
    editable?: boolean;
};

type HeaderDefaultProps = {
    type?: "default";
};

type HeaderProps = HeaderCommonProps & (HeaderSearchProps | HeaderDefaultProps);

const Header: React.FC<HeaderProps> = ({
    title,
    rightComponent,
    isShowBack = true,
    tintColor,
    onPress,
    titleColor,
    icBack,
    onBack,
    type = "default",
    editable = true,
    ...props
}) => {
    const renderRightComponent = React.useMemo(() => {
        if (
            typeof rightComponent === "number" ||
            (rightComponent && typeof rightComponent === "object" && "uri" in rightComponent)
        ) {
            return (
                <MyTouchable onPress={onPress}>
                    <Image
                        source={rightComponent}
                        className="w-6 h-6"
                        alt={typeof rightComponent === "number" ? rightComponent.toString() : "image"}
                        tintColor={tintColor}
                    />
                </MyTouchable>
            );
        }
        return rightComponent as React.ReactNode;
    }, [rightComponent, tintColor, onPress]);

    const renderTitle = React.useMemo(() => {
        if (typeof title === "string") {
            return <Text className={`text-[20px] font-bold ${titleColor ? `text-${titleColor}` : ""}`}>{title}</Text>;
        }
        return title;
    }, [title, titleColor]);

    const renderHeader = React.useMemo(() => {
        switch (type) {
            case "search":
            case "searchArea": {
                const { handleFilterPress, handleSearchSubmit, searchQuery, setSearchQuery, placeholder } =
                    props as HeaderSearchProps;

                return (
                    <HStack className="items-center justify-between">
                        <ButtonBack icBack={icBack} onBack={onBack} />

                        <Box className="flex-1 mx-3">
                            <HStack className="items-center border border-[#ECEEF1] rounded-xl pl-4">
                                <Box className="w-[24px] h-[24px]">
                                    <Image
                                        source={ImageAssets.icSearch}
                                        alt="search"
                                        className="w-full h-full"
                                        tintColor="#A0AEC0"
                                    />
                                </Box>
                                <Box className="flex-1">
                                    <Input
                                        placeholder={placeholder}
                                        value={searchQuery}
                                        onChangeText={setSearchQuery}
                                        backgroundColor="transparent"
                                        onSubmitEditing={handleSearchSubmit}
                                        returnKeyType="search"
                                        enable={editable}
                                    />
                                </Box>
                            </HStack>
                        </Box>

                        {type === "search" && (
                            <MyTouchable onPress={handleFilterPress}>
                                <Box className="w-6 h-6 justify-center items-center">
                                    <HStack className="relative">
                                        <IconComponent
                                            name="filter-outline"
                                            font="ionicons"
                                            size={24}
                                            color={getColor("textColor")}
                                        />
                                    </HStack>
                                </Box>
                            </MyTouchable>
                        )}
                    </HStack>
                );
            }

            default:
                return (
                    <HStack className="items-center justify-between">
                        <HStack className="gap-3 items-center">
                            {isShowBack && <ButtonBack icBack={icBack} onBack={onBack} />}
                            {renderTitle}
                        </HStack>
                        {renderRightComponent}
                    </HStack>
                );
        }
    }, [type, isShowBack, icBack, onBack, renderTitle, renderRightComponent, props, editable]);

    return <Box className="px-4 pb-4">{renderHeader}</Box>;
};

export default Header;
