import { LinearGradient } from "expo-linear-gradient";
import React from "react";
import { DimensionValue } from "react-native";

import { Box } from "../ui";

interface GradientDividerProps {
    horizontal?: boolean;
    className?: string;
}

const GradientDivider = ({ horizontal = false, className }: GradientDividerProps) => {
    const containerStyle = horizontal ? { width: "100%" as DimensionValue, height: 1 } : { width: 1, height: 48 };

    const gradientStyle = horizontal ? { width: "100%" as DimensionValue, height: 1 } : { width: 1, height: 48 };

    const startPoint = horizontal ? { x: 0, y: 0 } : { x: 0, y: 0 };
    const endPoint = horizontal ? { x: 1, y: 0 } : { x: 0, y: 1 };

    const containerClassName = horizontal ? "my-2" : "mx-3";

    return (
        <Box className={`${containerClassName} ${className || ""}`} style={containerStyle}>
            <LinearGradient
                colors={["rgba(224, 230, 237, 0)", "rgba(224, 230, 237, 1)", "rgba(224, 230, 237, 0)"]}
                locations={[0, 0.5, 1]}
                start={startPoint}
                end={endPoint}
                style={gradientStyle}
            />
        </Box>
    );
};

export default GradientDivider;
