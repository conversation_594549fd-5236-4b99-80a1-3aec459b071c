import React from "react";
import { Linking, Pressable, Image as RNImage } from "react-native";

import { Image } from "../ui";

type BannerProps = {
    banner?: BannerResponse;
};

const Banner: React.FC<BannerProps> = ({ banner }) => {
    const [aspectRatio, setAspectRatio] = React.useState<number>(4.06);

    React.useEffect(() => {
        if (banner?.banner?.image_url) {
            RNImage.getSize(
                banner.banner.image_url,
                (width, height) => {
                    setAspectRatio(width / height);
                },
                () => {
                    setAspectRatio(4.06);
                }
            );
        }
    }, [banner?.banner?.image_url]);

    return (
        <Pressable
            className="rounded-xl overflow-hidden self-center w-full "
            style={{ aspectRatio }}
            onPress={() => {
                if (banner?.banner?.url) {
                    Linking.openURL(banner.banner.url);
                }
            }}>
            <Image
                source={{ uri: banner?.banner?.image_url }}
                className="w-full h-full"
                alt="Banner"
                resizeMode="cover"
            />
        </Pressable>
    );
};

export default Banner;
