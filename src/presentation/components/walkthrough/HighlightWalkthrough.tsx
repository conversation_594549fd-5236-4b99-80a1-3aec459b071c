import React from "react";
import { Modal, Pressable, StyleSheet } from "react-native";
import Svg, { Defs, Mask, Rect } from "react-native-svg";

import { MyTouchable } from "../touchable";
import { Box, Text } from "../ui";

import { fullHeight, fullWidth, isAndroid, isIos } from "@/shared/helper";

export type HighlightWalkthroughProps = {
    isVisible: boolean;
    onClose?: () => void;
    onNext?: () => void;
    onBack?: () => void;
    title: string;
    content: string;
    highlightPosition?: {
        x: number;
        y: number;
        size: number;
    };
    highlightShape?: {
        width: number;
        height: number;
        cornerRadius: number;
    };
    currentStep?: number;
    totalSteps?: number;
    children?: React.ReactNode;
    trianglePosition?: "top" | "bottom";
    bottomOffset?: number;
    triangleTopRight?: number;
};

const HighlightWalkthrough: React.FC<HighlightWalkthroughProps> = ({
    isVisible,
    onClose,
    onNext,
    onBack,
    title,
    content,
    highlightPosition,
    highlightShape,
    currentStep = 1,
    totalSteps = 1,
    trianglePosition = "top",
    bottomOffset = 30,
    triangleTopRight = 0,
    children
}) => {
    if (!isVisible) {
        return <>{children}</>;
    }

    const screenHeightOffset = fullHeight * 0.055;

    const platformOffset = {
        x: 0,
        y: isAndroid ? -screenHeightOffset : 0
    };

    let centerX = 0;
    let centerY = 0;
    let rectX = 0;
    let rectY = 0;
    if (highlightPosition) {
        centerX = highlightPosition.x + highlightPosition.size / 2 + platformOffset.x;
        centerY = highlightPosition.y + highlightPosition.size / 2 + platformOffset.y;
        if (highlightShape) {
            rectX = Math.round(centerX - highlightShape.width / 2);
            rectY = Math.round(centerY - highlightShape.height / 2);
        }
    }

    return (
        <>
            <Box className="relative z-10">{children}</Box>
            <Modal transparent visible={isVisible} animationType="fade" onRequestClose={onClose}>
                <Pressable className="flex-1 items-center justify-center" onPress={onClose}>
                    <Svg height={fullHeight} width={fullWidth} style={StyleSheet.absoluteFill}>
                        <Defs>
                            <Mask id="mask" x="0" y="0" height="100%" width="100%">
                                <Rect height="100%" width="100%" fill="#fff" />
                                {highlightShape && (
                                    <Rect
                                        x={rectX}
                                        y={rectY}
                                        width={highlightShape.width}
                                        height={highlightShape.height}
                                        rx={highlightShape.cornerRadius}
                                        ry={highlightShape.cornerRadius}
                                        fill="#000"
                                    />
                                )}
                            </Mask>
                        </Defs>
                        {highlightShape && (
                            <Rect height="100%" width="100%" fill="rgba(0, 0, 0, 0.6)" mask="url(#mask)" />
                        )}
                    </Svg>
                    <Pressable
                        onPress={(e) => e.stopPropagation()}
                        style={{
                            position: "absolute",
                            bottom: highlightShape
                                ? fullHeight -
                                  rectY -
                                  highlightShape.height +
                                  (bottomOffset + (isIos ? fullHeight * 0.08 : 0))
                                : 150,
                            backgroundColor: "white",
                            borderRadius: 12,
                            padding: 20,
                            width: fullWidth * 0.85,
                            zIndex: 20
                        }}>
                        {trianglePosition === "top" && (
                            <Box
                                position="absolute"
                                top={-20}
                                alignSelf="center"
                                width={30}
                                height={20}
                                overflow="hidden"
                                transform={[{ translateX: triangleTopRight }]}>
                                <Box
                                    width={0}
                                    height={0}
                                    backgroundColor="transparent"
                                    borderStyle="solid"
                                    borderLeftWidth={15}
                                    borderRightWidth={15}
                                    borderBottomWidth={20}
                                    borderLeftColor="transparent"
                                    borderRightColor="transparent"
                                    borderBottomColor="white"
                                />
                            </Box>
                        )}
                        <Text className="text-lg font-bold">{title}</Text>
                        <Text className="text-base">{content}</Text>
                        <Box className="flex-row items-center justify-between w-full mt-5">
                            {totalSteps > 1 && (
                                <Text className="text-base text-darkGray">
                                    {currentStep}/{totalSteps}
                                </Text>
                            )}
                            <Box className="flex-row gap-5 items-center justify-end">
                                {currentStep > 1 && onBack && (
                                    <MyTouchable onPress={onBack} className="p-2">
                                        <Text className="text-base text-darkGray">Back</Text>
                                    </MyTouchable>
                                )}
                                <MyTouchable
                                    onPress={currentStep < totalSteps && onNext ? onNext : onClose}
                                    className="p-2 bg-darkBlue rounded-full px-5 py-2">
                                    <Text className="text-base text-white">
                                        {currentStep < totalSteps ? "Next" : "Start Recycling"}
                                    </Text>
                                </MyTouchable>
                            </Box>
                        </Box>
                        {trianglePosition === "bottom" && (
                            <Box
                                position="absolute"
                                bottom={-20}
                                alignSelf="center"
                                width={30}
                                height={20}
                                overflow="hidden">
                                <Box
                                    width={0}
                                    height={0}
                                    backgroundColor="transparent"
                                    borderStyle="solid"
                                    borderLeftWidth={15}
                                    borderRightWidth={15}
                                    borderBottomWidth={20}
                                    borderLeftColor="transparent"
                                    borderRightColor="transparent"
                                    borderBottomColor="white"
                                    transform={[{ rotate: "180deg" }]}
                                />
                            </Box>
                        )}
                    </Pressable>
                </Pressable>
            </Modal>
        </>
    );
};

export default HighlightWalkthrough;
