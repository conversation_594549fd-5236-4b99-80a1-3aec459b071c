import { useNavigation } from "@react-navigation/native";
import React from "react";

import HighlightWalkthrough from "./HighlightWalkthrough";

import { RouteName } from "@/shared/constants";
import { fullHeight, fullWidth } from "@/shared/helper";

export type FloatingButtonWalkthroughProps = {
    isVisible: boolean;
    onClose?: () => void;
    title: string;
    content: string;
    currentStep?: number;
    totalSteps?: number;
    children: React.ReactNode;
};

const FloatingButtonWalkthrough: React.FC<FloatingButtonWalkthroughProps> = ({
    isVisible,
    onClose,
    title,
    content,
    currentStep = 1,
    totalSteps = 1,
    children
}) => {
    const navigation = useNavigation();
    const buttonPosition = React.useMemo(
        () => ({
            x: fullWidth / 2 - 30,
            y: fullHeight - 90,
            size: 60
        }),
        []
    );

    const handleNext = React.useCallback(() => {
        onClose?.();
        navigation.navigate(RouteName.ScanStack, { screen: RouteName.Scan, params: { fromWalkthrough: true } });
    }, [navigation, onClose]);

    const handleBack = React.useCallback(() => {
        onClose?.();
    }, [onClose]);

    return (
        <HighlightWalkthrough
            isVisible={isVisible}
            onClose={onClose}
            onNext={handleNext}
            onBack={currentStep > 1 ? handleBack : undefined}
            title={title}
            content={content}
            highlightPosition={buttonPosition}
            highlightShape={{ width: 70, height: 70, cornerRadius: 20 }}
            currentStep={currentStep}
            totalSteps={totalSteps}
            trianglePosition="bottom">
            {children}
        </HighlightWalkthrough>
    );
};

export default FloatingButtonWalkthrough;
