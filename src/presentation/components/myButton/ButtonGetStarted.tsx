import React from "react";

import MyButton from "./MyButton";

import { ShowBottomSheetObs } from "@/data/services/observable";
import { TypeBottomSheet } from "@/shared/constants";

const ButtonGetStarted = () => {
    const handleGetStarted = React.useCallback(() => {
        ShowBottomSheetObs.action({
            type: TypeBottomSheet.REGISTER
        });
    }, []);

    return <MyButton text="Get Started" onPress={handleGetStarted} />;
};

export default ButtonGetStarted;
