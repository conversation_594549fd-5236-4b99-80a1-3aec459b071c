import { useNavigation } from "@react-navigation/native";
import React from "react";
import { ImageSourcePropType } from "react-native";

import { Image } from "../ui";
import Touchable from "../ui/touch";

import { ImageAssets } from "@/shared/constants";

type ButtonBackProps = {
    disabled?: boolean;
    icBack?: ImageSourcePropType;
    onBack?: () => void;
};

const ButtonBack: React.FC<ButtonBackProps> = ({ disabled = false, icBack, onBack }) => {
    const navigation = useNavigation();

    const handleBack = React.useCallback(() => {
        if (onBack) {
            onBack();
        } else {
            navigation.goBack();
        }
    }, [navigation, onBack]);

    return (
        <Touchable onPress={handleBack} disabled={disabled}>
            <Image source={icBack || ImageAssets.icBack} className="w-[24px] h-[24px]" alt="back" />
        </Touchable>
    );
};

export default ButtonBack;
