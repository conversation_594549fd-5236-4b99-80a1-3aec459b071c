import React from "react";

import { Skeleton } from "../skeleton";
import { Box, Text } from "../ui";

type AccountNameProps = {
    user?: User;
    isLoading?: boolean;
};

const AccountName: React.FC<AccountNameProps> = ({ user, isLoading }) => {
    if (isLoading) {
        return (
            <Box className="gap-y-2 flex-1">
                <Skeleton width={80} height={16} />
                <Skeleton width={120} height={16} />
            </Box>
        );
    }
    return (
        <Box flex={1}>
            <Text className="text-2xl font-bold" numberOfLines={2}>
                {(user?.first_name || "") + " " + (user?.last_name || "")}
            </Text>
            <Text className="text-lg" numberOfLines={2}>
                {!!user?.email_verified_at && user?.verified ? "Verified" : "Email unverified"}
            </Text>
        </Box>
    );
};

export default AccountName;
