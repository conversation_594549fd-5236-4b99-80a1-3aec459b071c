import React from "react";
import { Modal } from "react-native";
import WebView from "react-native-webview";

import { Header } from "../header";
import { Box } from "../ui";

import usePayload from "./WebViewModal.Hook";

const WebViewModal = () => {
    const { isOpen, payload, handleClose } = usePayload();

    const handleClosePress = React.useCallback(() => {
        handleClose();
    }, [handleClose]);

    if (!isOpen || !payload) return null;

    return (
        <Modal visible={isOpen} animationType="slide">
            <Box className="pt-safe">
                <Header title={payload.title} isShowBack={true} onBack={handleClosePress} />
            </Box>
            <Box className="flex-1">
                <WebView source={{ uri: payload.uri }} startInLoadingState javaScriptEnabled domStorageEnabled />
            </Box>
        </Modal>
    );
};

export default React.memo(WebViewModal);
