import React from "react";

import { WebViewObs } from "@/data/services/observable";

const usePayload = () => {
    const [state, setState] = React.useState<{
        isOpen: boolean;
        payload?: WebViewObsPayload;
    }>({
        isOpen: false,
        payload: undefined
    });

    React.useEffect(() => {
        const subscription = WebViewObs.subscribe((params) => {
            setState((prev) => ({
                ...prev,
                isOpen: true,
                payload: params
            }));
        });

        return () => {
            subscription.unsubscribe();
        };
    }, []);

    const handleClose = React.useCallback(() => {
        setState((prev) => ({
            ...prev,
            isOpen: false,
            payload: undefined
        }));
        state.payload?.onClose?.();
    }, [state.payload]);

    return React.useMemo(
        () => ({
            ...state,
            handleClose
        }),
        [state, handleClose]
    );
};

export default usePayload;
