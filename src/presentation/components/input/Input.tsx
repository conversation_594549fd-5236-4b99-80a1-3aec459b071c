import React from "react";
import { ActivityIndicator, Animated, TextInput, TextInputProps, TextStyle } from "react-native";
import { OtpInput, OtpInputRef } from "react-native-otp-entry";

import { getColor, useDateTimePicker } from "@/presentation/hooks";

import { Dropdown, DropdownRef, type DropdownItem } from "../dropdown";
import { MyTouchable } from "../touchable";
import { Box, HStack, IconComponent, Text, VStack } from "../ui";

import useShakeView from "./Input.Hook";

import { formatDate, fullWidth } from "@/shared/helper";

type StyleProps = Omit<TextStyle, "transform">;

type InputCommonProps = Omit<React.ComponentPropsWithoutRef<typeof TextInput>, keyof StyleProps> &
    StyleProps &
    TextInputProps & {
        prefixIcon?: React.ReactNode;
        suffixIcon?: React.ReactNode;
        onChangeFocus?: (name: string, isFocus: boolean) => void;
        onChangeValue?: (field: string, value: string, shouldValidate?: boolean | undefined) => void;
        fieldName?: string;
        enable?: boolean;
        title?: string;
        error?: string | boolean | Error | null;
        isLoading?: boolean;
        height?: number;
        testID?: string;
        required?: boolean;
        px?: number;
    };

type InputDropdownProps = {
    type?: "dropdown";
    dataDropdown?: DropdownItem[];
};

type HeaderDefaultProps = {
    type?: TypeInput;
};

export type InputProps = InputCommonProps & (InputDropdownProps | HeaderDefaultProps);

const createStyleFromProps = (props: StyleProps): TextStyle => {
    const styleKeys = Object.keys(props).filter((key) => props[key as keyof StyleProps] !== undefined);
    return Object.fromEntries(styleKeys.map((key) => [key, props[key as keyof StyleProps]])) as TextStyle;
};

const Input = React.forwardRef<TextInput | OtpInputRef | DropdownRef, InputProps>(
    (
        {
            placeholder,
            prefixIcon,
            suffixIcon,
            fieldName,
            onChangeValue,
            enable = true,
            height = 50,
            title,
            error,
            testID,
            type,
            required,
            isLoading,
            backgroundColor,
            px,
            ...props
        },
        ref
    ) => {
        const styleProps = createStyleFromProps(props as StyleProps);

        const shake = useShakeView(error as string);
        const { openDateTimePicker } = useDateTimePicker();

        const handleChangeText = React.useCallback(
            (text: string) => {
                const shouldUppercase = fieldName === "eventCode";
                const finalText = shouldUppercase ? text.toUpperCase() : text;

                if (fieldName) {
                    onChangeValue?.(fieldName, finalText);
                    return;
                }

                props.onChangeText?.(finalText);
            },
            [fieldName, onChangeValue, props]
        );

        const initValueDrop = React.useMemo(() => {
            const { dataDropdown } = props as InputDropdownProps;

            if (type !== "dropdown" || !props.value || !dataDropdown) return undefined;
            return dataDropdown?.find((item) =>
                typeof item.value === "number" ? item.value === Number(props.value) : item.value === props.value
            );
        }, [props, type]);

        const handleSelectDropdown = React.useCallback(
            (selectedItem: DropdownItem, _index: number) => {
                if (fieldName) {
                    onChangeValue?.(fieldName, selectedItem.value.toString());
                    return;
                }
                props.onChangeText?.(selectedItem.value.toString());
            },
            [fieldName, onChangeValue, props]
        );

        const onDatePicker = React.useCallback(() => {
            openDateTimePicker({
                mode: "date",
                valueDate: props.value ? new Date(props.value) : new Date(),
                onSuccess: (value) => {
                    handleChangeText(value.toISOString());
                }
            });
        }, [handleChangeText, openDateTimePicker, props.value]);

        const handleClearText = React.useCallback(() => {
            handleChangeText("");
        }, [handleChangeText]);

        const otpInputWidth = Math.max(35, Math.min(45, (fullWidth - 60) / 6));
        const otpHorizontalMargin = Math.max(2, Math.min(4, (fullWidth - otpInputWidth * 6) / 12));

        const _renderInput = React.useMemo(() => {
            switch (type) {
                case "date":
                    return (
                        <MyTouchable onPress={onDatePicker} disabled={!enable}>
                            <HStack
                                style={{ height }}
                                className={`items-center w-full rounded-2xl bg-gray ${!enable && "bg-inputDisable"} px-5 border-2 ${error ? "border-red" : "border-transparent"} `}>
                                <Text className={`${!enable || !props.value ? "text-gray2" : ""} font-semibold`}>
                                    {props.value ? formatDate(props.value) : placeholder || "DD/MM/YYYY"}
                                </Text>
                            </HStack>
                        </MyTouchable>
                    );
                case "dropdown": {
                    const { dataDropdown } = props as InputDropdownProps;
                    return (
                        <Box className={`${error ? "border-red" : "border-transparent"} border-2 rounded-2xl`}>
                            <Dropdown
                                ref={ref as React.ForwardedRef<DropdownRef>}
                                data={dataDropdown || []}
                                defaultValue={initValueDrop}
                                onSelect={handleSelectDropdown}
                                defaultButtonText={placeholder}
                                backgroundColor={getColor("gray")}
                                suffixIcon={suffixIcon}
                                disabled={!enable}
                            />
                        </Box>
                    );
                }
                case "otp":
                    return (
                        <Box className="items-center px-1 w-full gap-10 pb-10">
                            <Text>Enter OTP Code</Text>
                            <OtpInput
                                ref={ref as React.ForwardedRef<OtpInputRef>}
                                numberOfDigits={6}
                                focusColor={getColor("darkBlue")}
                                focusStickBlinkingDuration={500}
                                onFilled={handleChangeText}
                                onTextChange={(text) => {
                                    if (fieldName) {
                                        onChangeValue?.(fieldName, text, false);
                                    }
                                }}
                                theme={{
                                    containerStyle: { width: "100%" },
                                    pinCodeContainerStyle: {
                                        width: otpInputWidth,
                                        height: Math.max(50, otpInputWidth + 10),
                                        borderRadius: 12,
                                        borderWidth: 1,
                                        borderColor: getColor("gray"),
                                        backgroundColor: getColor("gray"),
                                        marginHorizontal: otpHorizontalMargin
                                    },
                                    pinCodeTextStyle: {
                                        fontSize: 18,
                                        color: "black",
                                        fontWeight: "600"
                                    }
                                }}
                                disabled={!enable}
                            />
                        </Box>
                    );
                case "phone":
                    return (
                        <HStack
                            style={{ height }}
                            className={`items-center w-full rounded-2xl  ${!enable && "bg-inputDisable"}  ${error ? "border-2 border-red" : " "} gap-2`}>
                            <Box className="rounded-2xl bg-gray h-full w-[55px]">
                                <Box className="flex-1 items-center justify-center">
                                    <Text className="text-gray2">+65</Text>
                                </Box>
                            </Box>
                            <Box className="items-center flex-1 h-full bg-gray rounded-2xl ">
                                <HStack className="items-center flex-1 h-full px-5">
                                    <TextInput
                                        testID={testID}
                                        ref={ref as React.ForwardedRef<TextInput>}
                                        {...props}
                                        className="font-semibold w-full font-body h-full"
                                        placeholder={placeholder}
                                        onChangeText={handleChangeText}
                                        editable={enable}
                                        placeholderTextColor={getColor("gray2")}
                                        style={[styleProps]}
                                        keyboardType="numeric"
                                    />
                                    <Box>
                                        {suffixIcon ||
                                            (error && (
                                                <Box>
                                                    <IconComponent
                                                        name="close-circle"
                                                        font="ionicons"
                                                        color={getColor("gray2")}
                                                        size={18}
                                                        onPress={handleClearText}
                                                    />
                                                </Box>
                                            ))}
                                    </Box>
                                </HStack>
                            </Box>
                        </HStack>
                    );
                case "email":
                    return (
                        <HStack
                            style={{ height }}
                            className={`items-center w-full rounded-2xl bg-gray ${!enable && "bg-inputDisable"} px-5  ${error ? "border-2 border-red" : " "} `}>
                            <TextInput
                                ref={ref as React.ForwardedRef<TextInput>}
                                {...props}
                                className="font-semibold w-full font-body h-full"
                                placeholder={placeholder}
                                onChangeText={handleChangeText}
                                editable={enable}
                                placeholderTextColor={getColor("gray2")}
                                style={[styleProps]}
                                autoCapitalize="none"
                            />
                        </HStack>
                    );
                default:
                    return (
                        <HStack
                            style={{ height }}
                            backgroundColor={backgroundColor || getColor("gray")}
                            className={`items-center w-full rounded-2xl  ${!enable && "bg-inputDisable"} ${px ? `px-${px}` : "px-5"}   ${error ? "border-2 border-red" : " "} `}>
                            <HStack className="items-center flex-1 h-full" space="md">
                                {prefixIcon}
                                <TextInput
                                    testID={testID}
                                    ref={ref as React.ForwardedRef<TextInput>}
                                    {...props}
                                    className="font-semibold w-full font-body h-full"
                                    placeholder={placeholder}
                                    onChangeText={handleChangeText}
                                    editable={enable}
                                    placeholderTextColor={getColor("gray2")}
                                    style={[styleProps]}
                                />
                            </HStack>
                            <Box className="pl-3">
                                {isLoading ? (
                                    <ActivityIndicator size="small" color={getColor("gray2")} />
                                ) : (
                                    suffixIcon ||
                                    (error && (
                                        <Box>
                                            <IconComponent
                                                name="close-circle"
                                                font="ionicons"
                                                color={getColor("gray2")}
                                                size={18}
                                                onPress={handleClearText}
                                            />
                                        </Box>
                                    ))
                                )}
                            </Box>
                        </HStack>
                    );
            }
        }, [
            type,
            onDatePicker,
            enable,
            height,
            error,
            props,
            placeholder,
            ref,
            initValueDrop,
            handleSelectDropdown,
            suffixIcon,
            handleChangeText,
            otpInputWidth,
            otpHorizontalMargin,
            testID,
            styleProps,
            handleClearText,
            backgroundColor,
            px,
            prefixIcon,
            isLoading,
            fieldName,
            onChangeValue
        ]);

        return (
            <VStack space="sm">
                {title && (
                    <HStack className="gap-1">
                        <Text className="text-blackLight">{title}</Text>
                        {required && <Text className="text-red">*</Text>}
                    </HStack>
                )}
                <VStack space="xs">
                    <Animated.View style={shake}>{_renderInput}</Animated.View>
                    {!isLoading && !!error && typeof error === "string" && (
                        <Box>
                            <Text testID={`${testID}-error`} className="text-red text-sm">
                                {error}
                            </Text>
                        </Box>
                    )}
                </VStack>
            </VStack>
        );
    }
);

export default Input;

declare global {
    export type TypeInput = "phone" | "otp" | "dropdown" | "date" | "email";
}
