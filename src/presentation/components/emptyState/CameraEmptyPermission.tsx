import React from "react";
import { CameraDevice } from "react-native-vision-camera";

import { Header } from "../header";
import { MyTouchable } from "../touchable";
import { Box, Container, Text } from "../ui";

import { ImageAssets } from "@/shared/constants";

type CameraEmptyPermissionProps = {
    device: CameraDevice | null | undefined;
    requestPermission: () => void;
};

const CameraEmptyPermission: React.FC<CameraEmptyPermissionProps> = ({ device, requestPermission }) => {
    return (
        <Container backgroundColor="rgba(0, 0, 0, 0.6)">
            <Box className="pt-5">
                <Header
                    title="Scan QR Code"
                    icBack={ImageAssets.icClose}
                    titleColor="white"
                    rightComponent={ImageAssets.icLight}
                />
            </Box>
            <Box className="flex-1 items-center justify-center p-5">
                <Text className="text-center text-lg text-white">
                    {!device ? "Camera not available" : "Camera permission not granted"}
                </Text>
                {!device ? null : (
                    <MyTouchable className="mt-4 bg-blue-500 px-4 py-2 rounded-md" onPress={requestPermission}>
                        <Text className="text-white font-medium">Continue</Text>
                    </MyTouchable>
                )}
            </Box>
        </Container>
    );
};

export default CameraEmptyPermission;
