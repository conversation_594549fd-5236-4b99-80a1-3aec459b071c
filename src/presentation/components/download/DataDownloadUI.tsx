import React from "react";
import { ActivityIndicator } from "react-native";

import { getColor } from "@/presentation/hooks";

import { MyTouchable } from "../touchable";
import { Box, Text, VStack } from "../ui";
import ProgressBar from "../ui/ProgressBar";

import { BinStorageStatus } from "@/data/services/binStorage";

interface DataDownloadUIProps {
    status: BinStorageStatus;
    isVisible: boolean;
    onRetry?: () => void;
}

const DataDownloadUI: React.FC<DataDownloadUIProps> = ({ status, isVisible, onRetry }) => {
    if (!isVisible) return null;

    const getStatusMessage = () => {
        switch (status.state) {
            case "downloading":
                return "Downloading bin locations...";
            case "syncing":
                return "Processing and storing data...";
            case "error":
                return status.error || "Download failed. Please check your connection.";
            case "background_refresh":
                return "Updating data...";
            default:
                return "Preparing data...";
        }
    };

    const getProgressText = () => {
        if (status.state === "downloading" && status.progress < 50) {
            return "Fetching location data from server...";
        } else if (status.state === "syncing" || status.progress >= 50) {
            const processedRecords = Math.round(((status.progress - 50) / 50) * status.totalRecords);
            return `Processing ${Math.max(0, processedRecords)} of ${status.totalRecords} locations`;
        } else if (status.totalRecords > 0) {
            return `Found ${status.totalRecords} locations`;
        }
        return "Initializing download...";
    };

    return (
        <Box className="absolute inset-0 bg-white/95 items-center justify-center z-50">
            <VStack className="items-center gap-y-6 px-8 w-full">
                {status.progress > 0 && status.progress < 100 ? (
                    <VStack className="items-center gap-y-4 w-full max-w-xs">
                        <ProgressBar
                            progress={Math.min(100, Math.max(0, status.progress))}
                            height={12}
                            backgroundColor={getColor("gray-200")}
                            colors={[getColor("lightBlue"), getColor("blue")]}
                            borderRadius={8}
                            showPercentage={true}
                            percentageTextColor={getColor("gray-500")}
                            percentageTextSize={14}
                        />
                    </VStack>
                ) : (
                    <ActivityIndicator size="large" color={getColor("green")} />
                )}

                <VStack className="items-center gap-y-2">
                    <Text className="text-lg font-semibold text-center">{getStatusMessage()}</Text>

                    <Text className="text-sm text-gray-600 text-center">{getProgressText()}</Text>

                    {status.state === "error" && (
                        <VStack className="items-center gap-y-2 mt-2">
                            <Text className="text-xs text-red-500 text-center">
                                Please check your internet connection
                            </Text>
                            {onRetry && (
                                <MyTouchable onPress={onRetry} className="bg-green-500 px-4 py-2 rounded-lg">
                                    <Text className="text-white font-medium">Retry</Text>
                                </MyTouchable>
                            )}
                        </VStack>
                    )}
                </VStack>
            </VStack>
        </Box>
    );
};

export default DataDownloadUI;
