import React from "react";
import { ImageSourcePropType } from "react-native";

import { getColor } from "@/presentation/hooks";

import { MyTouchable } from "../touchable";
import { Box, IconComponent, Image, Text } from "../ui";

import { IconName } from "@/shared/types/icon";

interface ChipProps {
    icon?: string | number | IconName | ImageSourcePropType;
    label: string;
    onPress?: () => void;
    isSelected?: boolean;
    disabled?: boolean;
    activeColor?: string;
    radius?: "sm" | "md" | "lg" | "xl" | "2xl" | "3xl" | "full";
    tintColorActive?: string;
    tintColorDisabled?: string;
}

const Chip = ({
    icon,
    label,
    onPress,
    isSelected = false,
    disabled = false,
    activeColor = "green",
    radius = "md",
    tintColorActive = "white",
    tintColorDisabled
}: ChipProps) => {
    const getBgColor = () => {
        if (isSelected) return `bg-${activeColor}`;
        return "bg-grayCard";
    };

    const getTextColor = () => {
        if (isSelected) return "text-white";
        return "text-darkGray";
    };

    const getIconColor = () => {
        if (isSelected) return getColor("white");
        return getColor("blue");
    };

    return (
        <MyTouchable
            className={`flex-row items-center justify-center rounded-${radius} px-3 h-[36px] ${getBgColor()}`}
            onPress={onPress}
            disabled={disabled}
            style={{ minWidth: "auto", flexShrink: 0 }}>
            {icon && (
                <Box className="mr-2 flex-shrink-0">
                    {typeof icon === "string" ? (
                        <IconComponent name={icon as IconName} font="feather" size={20} color={getIconColor()} />
                    ) : (
                        <Image
                            source={icon}
                            className="w-[20px] h-[20px]"
                            alt={label}
                            tintColor={isSelected ? tintColorActive : disabled ? "black" : tintColorDisabled}
                        />
                    )}
                </Box>
            )}
            <Text className={`${getTextColor()} text-sm font-medium flex-shrink-0`} numberOfLines={1}>
                {label}
            </Text>
        </MyTouchable>
    );
};

export default Chip;
