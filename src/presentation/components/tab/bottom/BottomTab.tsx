import { BottomTabBarProps } from "@react-navigation/bottom-tabs";
import { NavigationState, PartialState, Route } from "@react-navigation/native";
import React from "react";

import { Box } from "../../ui";

import TabItem from "./TabItem";

import { RootNavigator } from "@/data/services/navigation";
import { useRole } from "@/presentation/hooks/role";
import { RouteName } from "@/shared/constants";

type BottomTabProps = BottomTabBarProps;

const BottomTab: React.FC<BottomTabProps> = (props) => {
    const { state, navigation } = props;
    const { routes } = state;
    const { isIndividual } = useRole();

    const handlePress = React.useCallback(
        (
            index: number,
            routeName: keyof typeof RouteName,
            route?: Route<string> & {
                state?: NavigationState | PartialState<NavigationState> | undefined;
            }
        ) => {
            navigation.emit({
                type: "tabPress",
                target: route?.key,
                canPreventDefault: true
            });

            if (index === state.index) return;
            RootNavigator.replaceName(routeName);
        },
        [navigation, state.index]
    );

    const _renderTabItem = React.useMemo(
        () =>
            routes.map((item, index) => (
                <TabItem
                    key={index}
                    route={item}
                    onPress={handlePress}
                    index={index}
                    indexSelect={state.index}
                    isFloating={isIndividual && index === 2}
                />
            )),
        [handlePress, routes, state.index, isIndividual]
    );

    return <Box className="flex-row pb-5 pt-2 items-end justify-center px-4 bg-white">{_renderTabItem}</Box>;
};

export default BottomTab;
