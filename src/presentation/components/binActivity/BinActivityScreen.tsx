import React from "react";

import { Header } from "../header";
import { MyTouchable } from "../touchable";
import { Box, Container, HStack, IconComponent, ScrollView, Text, VStack } from "../ui";

/**
 * Interface for bin activity data
 */
interface BinActivity {
    id: number;
    type: string;
    name: string;
    activeBins: number;
    totalPoints: string;
    icon: string;
    backgroundColor: string;
    iconColor: string;
}

// Mock data for bin activities - replace with actual data from your store/API
const binActivities: BinActivity[] = [
    {
        id: 1,
        type: "3in1",
        name: "3-in-1 bin",
        activeBins: 5,
        totalPoints: "10.01",
        icon: "layers",
        backgroundColor: "#C9E7FF",
        iconColor: "#009DD3"
    },
    {
        id: 2,
        type: "recycling",
        name: "Recycling bin",
        activeBins: 17,
        totalPoints: "1001.01",
        icon: "refresh-cw",
        backgroundColor: "#ECF6EA",
        iconColor: "#44A12B"
    },
    {
        id: 3,
        type: "batteries-bulb",
        name: "Batteries & Bulbs",
        activeBins: 17,
        totalPoints: "1001.01",
        icon: "zap",
        backgroundColor: "#FFF3CD",
        iconColor: "#F59E0B"
    },
    {
        id: 4,
        type: "batteries",
        name: "Batteries",
        activeBins: 17,
        totalPoints: "1001.01",
        icon: "battery",
        backgroundColor: "#E0E7FF",
        iconColor: "#6366F1"
    },
    {
        id: 5,
        type: "rvm",
        name: "RVM",
        activeBins: 1,
        totalPoints: "1001.01",
        icon: "monitor",
        backgroundColor: "#FEE2E2",
        iconColor: "#EF4444"
    }
];

interface BinActivityCardProps {
    item: BinActivity;
    onPress?: () => void;
}

/**
 * Individual bin activity card component
 * Displays bin information including icon, name, active bins count, and total CO₂ points
 */
const BinActivityCard: React.FC<BinActivityCardProps> = ({ item, onPress }) => {
    return (
        <MyTouchable onPress={onPress}>
            <Box className="bg-[#FBFBFB] rounded-xl p-3 mb-4 mx-4">
                <HStack className="items-center gap-3">
                    {/* Icon Container */}
                    <Box
                        className="w-[54px] h-[54px] rounded-lg items-center justify-center border-2 border-white"
                        style={{
                            backgroundColor: item.backgroundColor,
                            shadowColor: "#000",
                            shadowOffset: { width: 0, height: 0 },
                            shadowOpacity: 0.1,
                            shadowRadius: 10,
                            elevation: 5
                        }}>
                        <IconComponent name={item.icon as any} font="feather" size={24} color={item.iconColor} />
                    </Box>

                    {/* Content */}
                    <Box className="flex-1">
                        <HStack className="items-center justify-between">
                            {/* Left Content */}
                            <VStack className="gap-1 flex-1 mr-4">
                                <Text className="text-[#183362] text-base font-medium">{item.name}</Text>
                                <HStack className="items-center gap-1">
                                    <Box className="w-2 h-2 rounded-full bg-[#44A12B]" />
                                    <Text className="text-[#4A5568] text-[13px] font-semibold">
                                        {item.activeBins} active bin{item.activeBins !== 1 ? "s" : ""}
                                    </Text>
                                </HStack>
                            </VStack>

                            {/* Right Content */}
                            <VStack className="items-end gap-1">
                                <Text className="text-[#009DD3] text-xl font-bold">{item.totalPoints}</Text>
                                <Text className="text-[#A0AEC0] text-[13px] font-semibold">Total CO₂ points</Text>
                            </VStack>
                        </HStack>
                    </Box>

                    {/* Chevron */}
                    <Box className="ml-2">
                        <IconComponent name="chevron-right" font="feather" size={20} color="#183362" />
                    </Box>
                </HStack>
            </Box>
        </MyTouchable>
    );
};

/**
 * Bin Activity Screen Component
 *
 * Displays a list of different bin types with their activity statistics.
 * Each bin shows:
 * - Icon with colored background
 * - Bin name and type
 * - Number of active bins
 * - Total CO₂ points earned
 *
 * Based on Figma design: Alba Step Up Mobile App - Bin Activity / See All
 *
 * @returns JSX.Element
 */
const BinActivityScreen: React.FC = () => {
    const handleBinPress = React.useCallback((_item: BinActivity) => {
        // Handle navigation to bin detail screen
        // You can navigate to a specific bin detail screen here
        // Example: navigation.navigate('BinDetail', { binId: _item.id, binType: _item.type });
    }, []);

    return (
        <Container>
            <Header title="Bin Activity" />
            <ScrollView className="flex-1">
                <VStack className="py-4">
                    {binActivities.map((item) => (
                        <BinActivityCard key={item.id} item={item} onPress={() => handleBinPress(item)} />
                    ))}
                </VStack>
            </ScrollView>
        </Container>
    );
};

export default BinActivityScreen;
