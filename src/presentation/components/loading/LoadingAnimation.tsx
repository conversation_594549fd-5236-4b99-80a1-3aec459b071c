import React from "react";
import Animated, {
    Easing,
    interpolate,
    useAnimatedStyle,
    useSharedValue,
    withRepeat,
    withSequence,
    withTiming
} from "react-native-reanimated";

import { Box, Text } from "@/presentation/components/ui";

interface LoadingAnimationProps {
    isFilterChanging?: boolean;
    title?: string;
    subtitle?: string;
}

const LoadingAnimation = React.memo<LoadingAnimationProps>(({ isFilterChanging = false, title, subtitle }) => {
    const dot1Opacity = useSharedValue(0.3);
    const dot2Opacity = useSharedValue(0.3);
    const dot3Opacity = useSharedValue(0.3);
    const scale = useSharedValue(1);

    React.useEffect(() => {
        dot1Opacity.value = withRepeat(
            withSequence(
                withTiming(1, { duration: 1000, easing: Easing.bezier(0.4, 0, 0.2, 1) }),
                withTiming(0.3, { duration: 1000, easing: Easing.bezier(0.4, 0, 0.2, 1) })
            ),
            -1,
            true
        );

        dot2Opacity.value = withRepeat(
            withSequence(
                withTiming(0.3, { duration: 200 }),
                withTiming(1, { duration: 1000, easing: Easing.bezier(0.4, 0, 0.2, 1) }),
                withTiming(0.3, { duration: 1000, easing: Easing.bezier(0.4, 0, 0.2, 1) })
            ),
            -1,
            true
        );

        dot3Opacity.value = withRepeat(
            withSequence(
                withTiming(0.3, { duration: 400 }),
                withTiming(1, { duration: 1000, easing: Easing.bezier(0.4, 0, 0.2, 1) }),
                withTiming(0.3, { duration: 1000, easing: Easing.bezier(0.4, 0, 0.2, 1) })
            ),
            -1,
            true
        );

        scale.value = withRepeat(
            withSequence(
                withTiming(1.02, { duration: 2000, easing: Easing.bezier(0.4, 0, 0.2, 1) }),
                withTiming(0.98, { duration: 2000, easing: Easing.bezier(0.4, 0, 0.2, 1) })
            ),
            -1,
            true
        );
    }, [dot1Opacity, dot2Opacity, dot3Opacity, scale]);

    const dot1Style = useAnimatedStyle(() => ({
        opacity: dot1Opacity.value,
        transform: [{ scale: interpolate(dot1Opacity.value, [0.3, 1], [0.8, 1.1]) }]
    }));

    const dot2Style = useAnimatedStyle(() => ({
        opacity: dot2Opacity.value,
        transform: [{ scale: interpolate(dot2Opacity.value, [0.3, 1], [0.8, 1.1]) }]
    }));

    const dot3Style = useAnimatedStyle(() => ({
        opacity: dot3Opacity.value,
        transform: [{ scale: interpolate(dot3Opacity.value, [0.3, 1], [0.8, 1.1]) }]
    }));

    const containerStyle = useAnimatedStyle(() => ({
        transform: [{ scale: scale.value }]
    }));

    const defaultTitle = title || (isFilterChanging ? "Updating Map" : "Loading Map");
    const defaultSubtitle = subtitle || (isFilterChanging ? "Applying your filter..." : "Preparing your view...");

    return (
        <Box className="flex-1 items-center justify-center bg-white">
            <Animated.View style={containerStyle}>
                <Box className="items-center space-y-8">
                    <Box className="flex-row space-x-3">
                        <Animated.View style={dot1Style}>
                            <Box className="w-4 h-4 bg-blue rounded-full" />
                        </Animated.View>
                        <Animated.View style={dot2Style}>
                            <Box className="w-4 h-4 bg-green rounded-full" />
                        </Animated.View>
                        <Animated.View style={dot3Style}>
                            <Box className="w-4 h-4 bg-blue rounded-full" />
                        </Animated.View>
                    </Box>

                    <Box className="items-center">
                        <Text className="text-lg font-semibold text-blue text-center">{defaultTitle}</Text>
                        <Text className="text-sm text-darkGray text-center mt-1">{defaultSubtitle}</Text>
                    </Box>
                </Box>
            </Animated.View>
        </Box>
    );
});

LoadingAnimation.displayName = "LoadingAnimation";

export default LoadingAnimation;
