import React from "react";

import { getColor } from "@/presentation/hooks";

import { Box, HStack, Image, Text, VStack } from "../ui";

import { ImageAssets } from "@/shared/constants";

type RedeemToSectionProps = {
    rewardPoints: string;
    rewardName: string;
    rewardLabel: string;
};

const RedeemToSection: React.FC<RedeemToSectionProps> = ({ rewardPoints, rewardName, rewardLabel }) => (
    <Box className="mx-5 bg-gray rounded-xl p-5 gap-y-2">
        <Text className="text-neutralGray text-base">Redeem to</Text>
        <HStack className="justify-between">
            <VStack>
                <HStack className="gap-x-2">
                    <Box className="mt-2">
                        <Image
                            source={ImageAssets.icVoucher}
                            className="h-[14px] w-[20px]"
                            resizeMode="cover"
                            alt="voucher"
                            tintColor={getColor("blue")}
                        />
                    </Box>
                    <VStack>
                        <Text className="text-lg font-bold text-darkBlue">{rewardName}</Text>
                        <Text className="text-darkBlue">{rewardLabel}</Text>
                    </VStack>
                </HStack>
            </VStack>
            <Text className="text-darkBlue font-bold">{rewardPoints}</Text>
        </HStack>
    </Box>
);

export default RedeemToSection;
