import React from "react";

import { getColor, useLocationServices } from "@/presentation/hooks";

import { MyTouchable } from "../touchable";
import { Box, HStack, IconComponent, Image, Text, VStack } from "../ui";

import { ShowPopupRequiredObs } from "@/data/services/observable";
import { useGuest } from "@/presentation/hooks/user";
import { ImageAssets } from "@/shared/constants";
import { formatDate } from "@/shared/helper";

type EventItem = {
    item: EventResponse;
    onPress: (id: number) => void;
};

const EventItem: React.FC<EventItem> = ({ item, onPress }) => {
    const { calculatedDistance } = useLocationServices();
    const { isGuestMode } = useGuest();

    const distance = calculatedDistance(item.lat, item.long);

    const handlePress = React.useCallback(() => {
        if (isGuestMode()) {
            return ShowPopupRequiredObs.action();
        }
        onPress(item.id);
    }, [onPress, item.id, isGuestMode]);

    return (
        <MyTouchable className="flex-row p-2 bg-white rounded-xl mb-2" onPress={handlePress}>
            <HStack className="gap-2 flex-1">
                <Box className="w-[60px] h-[76px] bg-grayCard rounded-lg items-center justify-between p-1">
                    <Box className="w-full items-center bg-white rounded-t-sm py-[2px]">
                        <Text className="text-gray-500 text-xs font-semibold">
                            {formatDate(item.date_start, "EEE").toUpperCase()}
                        </Text>
                    </Box>
                    <Text className="text-blue text-2xl font-bold">{formatDate(item.date_start, "dd")}</Text>
                    <Text className="text-darkGray text-xs font-semibold">
                        {formatDate(item.date_start, "MMM").toUpperCase()}
                    </Text>
                </Box>

                <VStack className="flex-1">
                    <HStack className="items-center gap-1">
                        <IconComponent name="clock" font="feather" size={16} color={getColor("darkBlue")} />
                        <Text className="text-darkBlue ml-1">{item.time_start_formatted}</Text>
                        {item.is_ongoing && <Text className="text-green text-[10px]">Ongoing Event</Text>}
                        <HStack className="items-center ml-auto">
                            <Box className="w-4 h-4 justify-center items-center mr-1">
                                <Image
                                    source={ImageAssets.tablerWalk}
                                    className="w-[18px] h-[18px]"
                                    alt="Walk"
                                    tintColor={getColor("green")}
                                />
                            </Box>
                            <Text className="text-green text-[16px]">{distance}</Text>
                        </HStack>
                    </HStack>

                    <Box className="flex-1">
                        <Text className="text-grayText font-semibold mt-2 text-[15px]">{item.address}</Text>
                    </Box>
                </VStack>
            </HStack>
        </MyTouchable>
    );
};

export default EventItem;
