import React from "react";

import { Box, HStack, Image, Text, VStack } from "../ui";

import { ImageAssets } from "@/shared/constants";
import { formatRelativeDate } from "@/shared/helper";

type ActivityHistoryItemProps = {
    item: EventLeaderboardHistoryItem;
};

const ActivityHistoryItem: React.FC<ActivityHistoryItemProps> = ({ item }) => {
    return (
        <Box key={item.id} className="bg-background-light rounded-xl p-4">
            <HStack alignItems="center" justifyContent="space-between">
                <HStack className="gap-2">
                    <Box>
                        <Image source={ImageAssets.icActivity} className="w-[24px] h-[24px]" alt="activity" />
                    </Box>
                    <VStack className="gap-1">
                        <Text className="text-darkGray text-[16px]">{item.bin?.type?.name}</Text>
                        <Text className="text-neutralGray text-[14px]">
                            {formatRelativeDate(item.created_at)} | Bin #{item.bin_id}
                        </Text>
                    </VStack>
                </HStack>
                <VStack alignItems="flex-end" className="gap-1">
                    <Text className="text-neutralGray font-bold text-[16px]">{item.bin?.point}pts</Text>
                </VStack>
            </HStack>
        </Box>
    );
};

export default ActivityHistoryItem;
