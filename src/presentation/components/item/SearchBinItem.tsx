import React from "react";

import { getColor, useLocationServices } from "@/presentation/hooks";

import { MyTouchable } from "../touchable";
import { Box, HStack, IconComponent, Image, Text, VStack } from "../ui";

import { ImageAssets } from "@/shared/constants";

type SearchBinItemProps = {
    item: BinResponse;
    handleNavigatePress: (item: BinResponse) => void;
};

const SearchBinItem: React.FC<SearchBinItemProps> = ({ item, handleNavigatePress }) => {
    const { calculatedDistance } = useLocationServices();

    const distance = calculatedDistance(item.lat, item.long);

    const onPress = React.useCallback(() => {
        handleNavigatePress(item);
    }, [handleNavigatePress, item]);

    return (
        <HStack className="bg-white rounded-xl p-2 mt-3 gap-2">
            <Box className="w-[56px] h-[56px] rounded-lg overflow-hidden bg-lightBlue justify-center items-center border-2 border-white shadow-md">
                <Image source={item.type.image_url} alt={item.type.name} className="w-[32px] h-[32px]" />
            </Box>
            <VStack className="flex-1 ml-2">
                <HStack className="items-center gap-2">
                    <Box className="w-4 h-4">
                        <Image
                            source={ImageAssets.tablerWalk}
                            alt="distance"
                            className="w-full h-full"
                            tintColor={getColor("green")}
                        />
                    </Box>
                    <Text className="text-[16px] text-green">{distance}</Text>
                </HStack>
                <HStack className="gap-2">
                    <Box>
                        <IconComponent name="map-pin" font="feather" size={16} color={getColor("neutralGray")} />
                    </Box>
                    <Box className="flex-1">
                        <Text className="text-[15px] text-blackLight flex-1">{item.address}</Text>
                    </Box>
                </HStack>
            </VStack>
            <MyTouchable onPress={onPress}>
                <Box className="w-[36px] h-[36px] rounded-full bg-lightBlue justify-center items-center">
                    <Box className="w-[19px] h-[19px]">
                        <Image source={ImageAssets.icNavigation} alt="navigation" className="w-full h-full" />
                    </Box>
                </Box>
            </MyTouchable>
        </HStack>
    );
};

export default SearchBinItem;
