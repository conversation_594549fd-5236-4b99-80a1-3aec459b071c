import React from "react";
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from "react-native-reanimated";

import { getColor } from "@/presentation/hooks";

import { MyTouchable } from "../touchable";
import { Box, HStack, IconComponent, Image, Text } from "../ui";

import EventItem from "./EventItem";

import { ImageAssets } from "@/shared/constants";
import { GroupedItem } from "@/shared/helper";

type CashForTrashItemProps = {
    item: GroupedItem<EventResponse>;
    setCashForTrashLocations: React.Dispatch<React.SetStateAction<GroupedItem<EventResponse>[]>>;
    getEventDetail: (eventId: number) => void;
};

const CashForTrashItem: React.FC<CashForTrashItemProps> = ({ item, setCashForTrashLocations, getEventDetail }) => {
    const heightAnimation = useSharedValue(0);
    const rotateAnimation = useSharedValue(0);

    React.useEffect(() => {
        rotateAnimation.value = withTiming(item.isExpanded ? 180 : 0, { duration: 300 });
        heightAnimation.value = withTiming(item.isExpanded ? 1 : 0, { duration: 300 });
    }, [item.isExpanded, heightAnimation, rotateAnimation]);

    const toggleLocationExpand = React.useCallback(
        (locationId: string) => {
            setCashForTrashLocations((prevLocations) =>
                prevLocations.map((location) =>
                    location.id === locationId ? { ...location, isExpanded: !location.isExpanded } : location
                )
            );
        },
        [setCashForTrashLocations]
    );

    const renderEventItem = React.useCallback(
        ({ item: event }: { item: EventResponse }) => {
            return <EventItem item={event} onPress={getEventDetail} />;
        },
        [getEventDetail]
    );

    const animatedIconStyle = useAnimatedStyle(() => {
        return {
            transform: [{ rotate: `${rotateAnimation.value}deg` }]
        };
    });

    const animatedContentStyle = useAnimatedStyle(() => {
        return {
            opacity: heightAnimation.value,
            maxHeight: heightAnimation.value * 1000,
            overflow: "hidden"
        };
    });

    return (
        <Box className="bg-grayCard rounded-xl mb-4">
            <MyTouchable
                onPress={() => toggleLocationExpand(item.id)}
                className="flex-row justify-between items-center px-4 py-4">
                <HStack className="items-center gap-4">
                    <Box className="w-[46px] h-[45px] justify-center items-center">
                        <Image
                            source={ImageAssets.icCrashForTrash}
                            className="w-[46px] h-[45px]"
                            resizeMode="contain"
                            alt="Recycle Bin"
                        />
                    </Box>
                    <Text className="text-gray-600 font-bold text-base">{item.name}</Text>
                </HStack>
                <Animated.View style={animatedIconStyle}>
                    <IconComponent name="chevron-down" font="feather" size={20} color={getColor("blue")} />
                </Animated.View>
            </MyTouchable>

            <Animated.View style={animatedContentStyle}>
                <Box className="px-2 pb-2">
                    {item.items.map((event) => (
                        <React.Fragment key={event.id}>{renderEventItem({ item: event })}</React.Fragment>
                    ))}
                </Box>
            </Animated.View>
        </Box>
    );
};

export default CashForTrashItem;
