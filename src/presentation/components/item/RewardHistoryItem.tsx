import React from "react";

import { Box, Image, Text, VStack } from "../ui";

import { ImageAssets } from "@/shared/constants";

type RewardHistoryItemProps = {
    item: PointHistoryResponse;
};

const RewardHistoryItem = ({ item }: RewardHistoryItemProps) => {
    return (
        <VStack className="gap-4">
            <Text className="text-neutralGray text-[16px] font-bold">{item.month_year}</Text>
            {item.transactions.map((transaction, index) => (
                <Box
                    key={`${transaction.date}_${index}`}
                    className="flex-row justify-between items-center p-4 bg-background-light rounded-lg">
                    <Box className="flex-row items-start gap-2">
                        <Image source={ImageAssets.icPoint} className="w-[24px] h-[24px]" alt="Point" />
                        <Box>
                            <Text className="font-medium text-base">
                                {transaction.type === "earned" ? "Earn points" : "Points redeemed"}
                            </Text>
                            <Text className="text-neutralGray text-sm">{transaction.date}</Text>
                        </Box>
                    </Box>
                    <Text
                        className={`font-bold text-base ${transaction.type === "earned" ? "text-darkBlue" : "text-red"}`}>
                        {transaction.points.toLocaleString()}
                    </Text>
                </Box>
            ))}
        </VStack>
    );
};

export default RewardHistoryItem;
