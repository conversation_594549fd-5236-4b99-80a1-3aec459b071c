import React from "react";
import { ImageSourcePropType } from "react-native";

import { Box, Image, Text } from "../ui";

import { ImageAssets } from "@/shared/constants";
import { fullWidth } from "@/shared/helper";

type WasteType = {
    id: number;
    name: string;
    pivot: {
        bin_type_id: number;
        waste_type_id: number;
    };
};

type AcceptedItemsGridProps = {
    items?: Array<{
        name: string;
        icon: ImageSourcePropType;
    }>;
    wasteTypes?: WasteType[];
};

const AcceptedItemsGrid: React.FC<AcceptedItemsGridProps> = ({ items, wasteTypes }) => {
    const containerPadding = 32;
    const itemSpacing = 8;
    const numColumns = 3;
    const availableWidth = fullWidth - containerPadding;
    const itemWidth = (availableWidth - itemSpacing * (numColumns - 1)) / numColumns;

    const displayItems = wasteTypes
        ? wasteTypes.map((waste) => ({
              name: waste.name,
              icon: ImageAssets.badgeCheck
          }))
        : items || [];

    const rows = [];
    for (let i = 0; i < displayItems.length; i += numColumns) {
        rows.push(displayItems.slice(i, i + numColumns));
    }

    return (
        <Box className="mb-4">
            <Text className="text-base font-bold mb-3">Accepted Items:</Text>
            {rows.map((row, rowIndex) => (
                <Box key={`row-${rowIndex}`} className="flex-row justify-between mb-4">
                    {row.map((item, colIndex) => (
                        <Box key={`item-${rowIndex}-${colIndex}`} style={{ width: itemWidth }} className="items-center">
                            <Box className="w-full h-24 items-center justify-between bg-grayCard p-3" borderRadius={12}>
                                <Box className="w-10 h-10 items-center justify-center rounded-full">
                                    <Image
                                        source={item.icon}
                                        style={{ width: 24, height: 24 }}
                                        resizeMode="contain"
                                        alt={item.name}
                                    />
                                </Box>
                                <Box className="w-full h-8 justify-center">
                                    <Text className="text-xs text-center" numberOfLines={2}>
                                        {item.name}
                                    </Text>
                                </Box>
                            </Box>
                        </Box>
                    ))}

                    {row.length < numColumns &&
                        Array(numColumns - row.length)
                            .fill(0)
                            .map((_, index) => <Box key={`empty-${rowIndex}-${index}`} style={{ width: itemWidth }} />)}
                </Box>
            ))}
        </Box>
    );
};

export default AcceptedItemsGrid;
