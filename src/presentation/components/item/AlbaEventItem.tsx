import { useNavigation } from "@react-navigation/native";
import React from "react";

import { MyButton } from "../myButton";
import { Box, Image, Text, VStack } from "../ui";

import { ImageAssets, RouteName } from "@/shared/constants";

type AlbaEventItem = {
    item: EventResponse;
    handleJoin: (id: number) => void;
    isLoading?: boolean;
    isAnyItemLoading?: boolean;
};

const AlbaEventItem: React.FC<AlbaEventItem> = ({ item, handleJoin, isLoading, isAnyItemLoading }) => {
    const navigation = useNavigation();

    const handlePressJoin = React.useCallback(() => {
        if (isAnyItemLoading) return;
        handleJoin(item.id);
    }, [handleJoin, item.id, isAnyItemLoading]);

    const handlePressViewDetail = React.useCallback(() => {
        if (isAnyItemLoading) return;
        navigation.navigate(RouteName.SpecialEvent, { eventId: item.id });
    }, [isAnyItemLoading, navigation, item.id]);

    const renderSection = React.useMemo(() => {
        if (item.joined) {
            return (
                <Box className="flex-1 items-center justify-center">
                    <Box className="w-1/2">
                        <MyButton
                            text="View Detail"
                            onPress={handlePressViewDetail}
                            variant="secondary"
                            isLoading={isLoading}
                        />
                    </Box>
                </Box>
            );
        }
        return (
            <Box className="flex-1 items-center justify-center">
                <Box className="w-1/2">
                    <MyButton text="Join Event" onPress={handlePressJoin} variant="primary" isLoading={isLoading} />
                </Box>
            </Box>
        );
    }, [item.joined, handlePressJoin, isLoading, handlePressViewDetail]);

    return (
        <VStack className="gap-5 p-5 rounded-2xl bg-white border-2 border-lightGray">
            <Image
                source={item?.image_url ? { uri: item?.image_url } : ImageAssets.icJoinEvent}
                className="w-full h-[150px]"
                alt="Join Event"
                resizeMode="contain"
            />
            <Text className="text-[16px] font-[700]">{item?.name || ""}</Text>

            <Text className="text-darkGray text-[16px]">{item?.description || ""}</Text>

            {renderSection}
        </VStack>
    );
};

export default AlbaEventItem;
