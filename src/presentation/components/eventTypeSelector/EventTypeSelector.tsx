import React from "react";
import { ImageSourcePropType, ScrollView } from "react-native";

import { getColor } from "@/presentation/hooks";

import { Chip } from "@/presentation/components/chip";
import { Skeleton } from "@/presentation/components/skeleton";
import { Box } from "@/presentation/components/ui";
import { EventTypes } from "@/shared/constants";
import { EventTypeK } from "@/shared/constants/EventTypes";
import { IconName } from "@/shared/types/icon";

type EventTypeSelectorProps = {
    selectedEventType: number;
    eventTypes: Array<{ id: EventTypeK; name: string }>;
    isLoading: boolean;
    onEventTypeChange: (type: { id: EventTypeK; name: string }) => void;
    getEventIcon: (eventTypeId: number) => string | number | IconName | ImageSourcePropType;
};

const EventTypeSelector: React.FC<EventTypeSelectorProps> = ({
    selectedEventType,
    eventTypes,
    isLoading,
    onEventTypeChange,
    getEventIcon
}) => {
    const renderSkeletonChips = React.useMemo(() => {
        const skeletonWidths = [70, 110, 90];

        return (
            <>
                {Array.from({ length: 3 }).map((_, index) => (
                    <Box
                        key={index}
                        className="flex-row items-center justify-center px-3 h-[36px] rounded-full bg-grayCard"
                        style={{ flexShrink: 0 }}>
                        <Box className="mr-2 flex-shrink-0">
                            <Skeleton width={20} height={20} borderRadius={10} />
                        </Box>
                        <Skeleton width={skeletonWidths[index]} height={16} borderRadius={4} />
                    </Box>
                ))}
            </>
        );
    }, []);

    return (
        <Box className="min-h-[60px]">
            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                    gap: 8,
                    flexGrow: 1
                }}
                style={{ flexGrow: 0 }}>
                {isLoading
                    ? renderSkeletonChips
                    : eventTypes.map((eventType) => (
                          <Box key={eventType.id.toString()}>
                              <Chip
                                  label={eventType.name}
                                  icon={getEventIcon(eventType.id)}
                                  isSelected={selectedEventType === eventType.id}
                                  onPress={() => onEventTypeChange(eventType)}
                                  activeColor="darkBlue"
                                  radius="full"
                                  tintColorDisabled={
                                      eventType.id === EventTypes.PRIVATE_EVENT ? "black" : getColor("darkBlue")
                                  }
                              />
                          </Box>
                      ))}
            </ScrollView>
        </Box>
    );
};

export default EventTypeSelector;
