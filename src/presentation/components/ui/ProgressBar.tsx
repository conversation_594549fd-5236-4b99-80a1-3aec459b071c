import { LinearGradient } from "expo-linear-gradient";
import React from "react";

import { getColor } from "@/presentation/hooks";

import { Box, Text } from "./index";

interface ProgressBarProps {
    progress: number; // 0-100
    height?: number;
    backgroundColor?: string;
    colors?: [string, string];
    borderRadius?: number;
    showPercentage?: boolean;
    percentageTextColor?: string;
    percentageTextSize?: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
    progress,
    height = 12,
    backgroundColor = "#e5e7eb", // gray-200
    colors = [getColor("lightBlue"), getColor("blue")],
    borderRadius = 8,
    showPercentage = false,
    percentageTextColor = "#6b7280", // gray-500
    percentageTextSize = 14
}) => {
    const clampedProgress = Math.min(100, Math.max(0, progress));

    return (
        <Box className="w-full">
            <Box
                className="rounded-full overflow-hidden"
                style={{
                    backgroundColor,
                    height,
                    borderRadius
                }}>
                <LinearGradient
                    colors={colors}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={{
                        width: `${clampedProgress}%`,
                        height: "100%",
                        borderRadius
                    }}
                />
            </Box>

            {showPercentage && (
                <Box className="mt-2">
                    <Text
                        className="text-center font-medium"
                        style={{
                            color: percentageTextColor,
                            fontSize: percentageTextSize
                        }}>
                        {Math.round(clampedProgress)}% Complete
                    </Text>
                </Box>
            )}
        </Box>
    );
};

export default ProgressBar;
