import React, { useEffect, useState } from "react";
import { Text, TouchableOpacity, View } from "react-native";

import { getColor } from "@/presentation/hooks";

import ProgressBar from "./ProgressBar";

const ProgressBarDemo: React.FC = () => {
    const [progress, setProgress] = useState(0);

    useEffect(() => {
        const interval = setInterval(() => {
            setProgress((prev) => {
                if (prev >= 100) {
                    clearInterval(interval);
                    return 100;
                }
                return prev + 10;
            });
        }, 1000);

        return () => clearInterval(interval);
    }, []);

    const resetProgress = () => {
        setProgress(0);
    };

    return (
        <View
            style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "#f5f5f5",
                padding: 20
            }}>
            <Text style={{ fontSize: 24, fontWeight: "bold", marginBottom: 30, textAlign: "center" }}>
                Progress Bar Demo
            </Text>

            <View style={{ width: "100%", gap: 30 }}>
                {/* Default ProgressBar */}
                <View>
                    <Text style={{ fontSize: 16, fontWeight: "bold", marginBottom: 10 }}>Default ProgressBar</Text>
                    <ProgressBar progress={progress} />
                </View>

                {/* Custom ProgressBar */}
                <View>
                    <Text style={{ fontSize: 16, fontWeight: "bold", marginBottom: 10 }}>Custom ProgressBar</Text>
                    <ProgressBar
                        progress={progress}
                        height={16}
                        backgroundColor={getColor("gray-300")}
                        colors={[getColor("green"), getColor("blue")]}
                        borderRadius={12}
                        showPercentage={true}
                        percentageTextColor={getColor("gray-700")}
                        percentageTextSize={16}
                    />
                </View>

                {/* Reward Card Style */}
                <View>
                    <Text style={{ fontSize: 16, fontWeight: "bold", marginBottom: 10 }}>Reward Card Style</Text>
                    <ProgressBar
                        progress={progress}
                        height={8}
                        backgroundColor="#18336266"
                        colors={[getColor("lightBlue"), getColor("blue")]}
                        borderRadius={8}
                        showPercentage={false}
                    />
                </View>

                {/* Download UI Style */}
                <View>
                    <Text style={{ fontSize: 16, fontWeight: "bold", marginBottom: 10 }}>Download UI Style</Text>
                    <ProgressBar
                        progress={progress}
                        height={12}
                        backgroundColor={getColor("gray-200")}
                        colors={[getColor("lightBlue"), getColor("blue")]}
                        borderRadius={8}
                        showPercentage={true}
                        percentageTextColor={getColor("gray-500")}
                        percentageTextSize={14}
                    />
                </View>
            </View>

            <Text style={{ marginTop: 30, fontSize: 16, textAlign: "center" }}>Progress: {progress}%</Text>

            <TouchableOpacity
                onPress={resetProgress}
                style={{
                    marginTop: 20,
                    backgroundColor: getColor("green"),
                    paddingHorizontal: 20,
                    paddingVertical: 10,
                    borderRadius: 8
                }}>
                <Text style={{ color: "white", fontWeight: "bold" }}>Reset</Text>
            </TouchableOpacity>
        </View>
    );
};

export default ProgressBarDemo;
