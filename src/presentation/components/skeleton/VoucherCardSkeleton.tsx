import React from "react";

import { Box, HStack } from "../ui";

import Skeleton from "./Skeleton";

const VoucherCardSkeleton = () => {
    return (
        <Box className="w-full p-2">
            <Box className="bg-grayCard rounded-xl overflow-hidden">
                <Box className="rounded-xl overflow-hidden m-3">
                    <Box className="h-[150px] items-center justify-center">
                        <Skeleton width="100%" height="100%" borderRadius={12} />
                    </Box>
                </Box>

                <Box className="self-end mx-3 -mt-[25px] mb-2">
                    <Skeleton width={80} height={24} borderRadius={12} />
                </Box>

                <Box className="mt-2 py-3 px-4 rounded-full mx-1 mb-2">
                    <HStack className="items-center justify-center gap-x-2">
                        <Skeleton width={24} height={24} borderRadius={12} />
                        <Skeleton width={60} height={20} />
                    </HStack>
                </Box>
            </Box>
        </Box>
    );
};

export default VoucherCardSkeleton;
