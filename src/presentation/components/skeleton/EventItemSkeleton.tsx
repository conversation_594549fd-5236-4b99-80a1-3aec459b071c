import React from "react";

import { Box, HStack, VStack } from "../ui";

import Skeleton from "./Skeleton";

const EventItemSkeleton = () => {
    return (
        <Box className="flex-row p-2 bg-white rounded-xl mb-2">
            <HStack className="gap-2 flex-1">
                <Box className="w-[60px] h-[76px] bg-grayCard rounded-lg items-center justify-between p-1">
                    <Skeleton width="100%" height={16} className="rounded-t-sm" />
                    <Skeleton width={24} height={24} className="my-1" />
                    <Skeleton width={32} height={12} />
                </Box>

                <VStack className="flex-1">
                    <HStack className="items-center gap-1">
                        <Skeleton width={16} height={16} borderRadius={8} />
                        <Skeleton width={60} height={16} className="ml-1" />
                        <HStack className="items-center ml-auto">
                            <Skeleton width={18} height={18} borderRadius={9} className="mr-1" />
                            <Skeleton width={40} height={16} />
                        </HStack>
                    </HStack>

                    <Box className="flex-1 mt-2">
                        <Skeleton width="90%" height={16} className="mb-1" />
                        <Skeleton width="70%" height={16} />
                    </Box>
                </VStack>
            </HStack>
        </Box>
    );
};

export default EventItemSkeleton;
