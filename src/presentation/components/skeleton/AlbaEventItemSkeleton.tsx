import React from "react";

import { Box, VStack } from "../ui";

import Skeleton from "./Skeleton";

const AlbaEventItemSkeleton = () => {
    return (
        <VStack className="gap-5 p-5 rounded-2xl bg-white border-2 border-lightGray">
            {/* Image skeleton */}
            <Skeleton width="100%" height={150} borderRadius={8} />

            {/* Title skeleton */}
            <Skeleton width="80%" height={20} borderRadius={4} />

            {/* Description skeleton - multiple lines */}
            <VStack className="gap-2">
                <Skeleton width="100%" height={16} borderRadius={4} />
                <Skeleton width="90%" height={16} borderRadius={4} />
                <Skeleton width="75%" height={16} borderRadius={4} />
            </VStack>

            {/* Warning text skeleton */}
            <Skeleton width="95%" height={14} borderRadius={4} />

            {/* Button skeleton */}
            <Box className="flex-1 items-center justify-center">
                <Box className="w-1/2">
                    <Skeleton width="100%" height={44} borderRadius={22} />
                </Box>
            </Box>
        </VStack>
    );
};

export default AlbaEventItemSkeleton;
