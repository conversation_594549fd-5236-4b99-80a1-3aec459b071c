import React from "react";

import { Box, HStack, VStack } from "../ui";

import Skeleton from "./Skeleton";

const ActivityHistoryItemSkeleton = () => {
    return (
        <Box className="bg-background-light rounded-xl p-4">
            <HStack alignItems="center" justifyContent="space-between">
                <HStack className="gap-2">
                    <Box>
                        <Skeleton width={24} height={24} borderRadius={4} />
                    </Box>
                    <VStack className="gap-1">
                        <Skeleton width={120} height={16} borderRadius={4} />
                        <Skeleton width={140} height={14} borderRadius={4} />
                    </VStack>
                </HStack>
                <VStack alignItems="flex-end" className="gap-1">
                    <Skeleton width={60} height={16} borderRadius={4} />
                </VStack>
            </HStack>
        </Box>
    );
};

export default ActivityHistoryItemSkeleton;
