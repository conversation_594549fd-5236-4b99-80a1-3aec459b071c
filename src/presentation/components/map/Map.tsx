import { Asset } from "expo-asset";
import * as FileSystem from "expo-file-system";
import React from "react";
import { ActivityIndicator, View } from "react-native";
import WebView, { WebViewMessageEvent } from "react-native-webview";

import { getColor } from "@/presentation/hooks";

import { MyTouchable } from "../touchable";
import { Box, Image } from "../ui";

import {
    DEFAULT_LATITUDE,
    DEFAULT_LONGITUDE,
    DEFAULT_ZOOM_LEVEL,
    SINGAPORE_BOUNDS,
    SINGAPORE_MAX_ZOOM,
    SINGAPORE_MIN_ZOOM
} from "./constants";
import htmlSourceUnified from "./maplibre-webview.html";
import { mapStateManager } from "./MapStateManager";
import { MapComponentProps, MapComponentRef, MapMarker } from "./types";

import { ImageAssets } from "@/shared/constants";
import { isAndroid } from "@/shared/helper";

interface WebViewMessage {
    type: string;
    [key: string]: any;
}

let htmlContentCache: string | null = null;
let htmlLoadingPromise: Promise<string> | null = null;

const loadHtmlContent = async (): Promise<string> => {
    if (htmlContentCache) {
        return htmlContentCache;
    }

    if (htmlLoadingPromise) {
        return htmlLoadingPromise;
    }

    htmlLoadingPromise = (async () => {
        const htmlAsset = Asset.fromModule(htmlSourceUnified as unknown as number);
        await htmlAsset.downloadAsync();
        const htmlText = await FileSystem.readAsStringAsync(htmlAsset.localUri || htmlAsset.uri, {
            encoding: FileSystem.EncodingType.UTF8
        });
        htmlContentCache = htmlText;
        return htmlText;
    })();

    return htmlLoadingPromise;
};

const MapComponent = React.forwardRef<MapComponentRef, MapComponentProps>(
    (
        {
            markers = [],
            onMarkerPress,
            onMarkersLoadingChange,
            userLocation,
            isLoadingLocation,
            initialRegion = {
                longitude: DEFAULT_LONGITUDE,
                latitude: DEFAULT_LATITUDE,
                zoomLevel: DEFAULT_ZOOM_LEVEL
            },
            showDefaultLocationButton = true
        },
        ref
    ) => {
        React.useImperativeHandle(ref, () => ({
            handleMyLocationPress: handleMyLocationPress
        }));

        const webViewRef = React.useRef<WebView>(null);
        const [isMapReady, setIsMapReady] = React.useState(false);
        const [webViewError, setWebViewError] = React.useState<string | null>(null);
        const [htmlContent, setHtmlContent] = React.useState<string | null>(null);
        const markersRef = React.useRef<MapMarker[]>([]);
        const hasSetUserLocationRef = React.useRef<boolean>(false);

        const safeInitialRegion = React.useMemo(
            () => ({
                longitude:
                    typeof initialRegion?.longitude === "number" && isFinite(initialRegion.longitude)
                        ? initialRegion.longitude
                        : DEFAULT_LONGITUDE,
                latitude:
                    typeof initialRegion?.latitude === "number" && isFinite(initialRegion.latitude)
                        ? initialRegion.latitude
                        : DEFAULT_LATITUDE,
                zoomLevel:
                    typeof initialRegion?.zoomLevel === "number" && isFinite(initialRegion.zoomLevel)
                        ? Math.max(SINGAPORE_MIN_ZOOM, Math.min(SINGAPORE_MAX_ZOOM, initialRegion.zoomLevel))
                        : DEFAULT_ZOOM_LEVEL
            }),
            [initialRegion?.longitude, initialRegion?.latitude, initialRegion?.zoomLevel]
        );

        const sendMessageToWebView = React.useCallback(
            (message: WebViewMessage) => {
                // console.warn("=== SENDING MESSAGE TO WEBVIEW ===");
                // console.warn("Message type:", message.type);
                // console.warn("WebView ref exists:", !!webViewRef.current);
                // console.warn("Map ready:", isMapReady);

                if (webViewRef.current && isMapReady) {
                    const jsonMessage = JSON.stringify(message);
                    console.warn("Message size:", jsonMessage.length, "characters");
                    console.warn("Message preview:", jsonMessage.substring(0, 100) + "...");

                    try {
                        webViewRef.current.postMessage(jsonMessage);
                        console.warn("Message sent successfully");
                    } catch (error) {
                        console.error("Error sending message:", error);
                    }
                } else {
                    console.warn("NOT sending message - webViewRef:", !!webViewRef.current, "isMapReady:", isMapReady);
                }
            },
            [isMapReady]
        );

        React.useEffect(() => {
            let isMounted = true;

            const initHtmlContent = async () => {
                try {
                    const htmlText = await loadHtmlContent();
                    if (isMounted) {
                        setHtmlContent(htmlText);
                    }
                } catch (e) {
                    console.error("Failed to load assets:", e);
                    if (isMounted) {
                        setWebViewError("Failed to load map content");
                    }
                }
            };

            initHtmlContent();

            return () => {
                isMounted = false;
            };
        }, []);

        function resolveImageUri(input: unknown): string | undefined {
            try {
                if (!input) return undefined;
                if (typeof input === "string") return input;
                if (typeof input === "number") return Asset.fromModule(input).uri;
                if (typeof input === "object" && (input as { uri?: unknown }).uri) {
                    const maybeUri = (input as { uri?: unknown }).uri;
                    return typeof maybeUri === "string" ? maybeUri : undefined;
                }
            } catch {
                // ignore
            }
            return undefined;
        }

        const enrichMarkersForWebView = React.useCallback((list: MapMarker[]) => {
            return list.map((m) => ({
                ...m,
                iconImageUrl: resolveImageUri((m as any).iconImage)
            }));
        }, []);

        const handleWebViewMessage = React.useCallback(
            (event: WebViewMessageEvent) => {
                try {
                    const message: WebViewMessage = JSON.parse(event.nativeEvent.data);

                    switch (message.type) {
                        case "mapReady": {
                            setIsMapReady(true);

                            const existingState = mapStateManager.getState();

                            if (existingState?.isInitialized) {
                                sendMessageToWebView({
                                    type: "setCenter",
                                    longitude: existingState.center.longitude,
                                    latitude: existingState.center.latitude,
                                    zoom: existingState.zoom
                                });
                            } else {
                                // Initial load - use initial region
                                sendMessageToWebView({
                                    type: "setCenter",
                                    longitude: safeInitialRegion.longitude,
                                    latitude: safeInitialRegion.latitude,
                                    zoom: safeInitialRegion.zoomLevel
                                });

                                mapStateManager.setState({
                                    center: {
                                        longitude: safeInitialRegion.longitude,
                                        latitude: safeInitialRegion.latitude
                                    },
                                    zoom: safeInitialRegion.zoomLevel
                                });
                                mapStateManager.markInitialized();
                            }

                            if (userLocation) {
                                sendMessageToWebView({
                                    type: "setUserLocation",
                                    longitude: userLocation.longitude,
                                    latitude: userLocation.latitude
                                });

                                if (!existingState?.userLocationSet && !hasSetUserLocationRef.current) {
                                    setTimeout(() => {
                                        sendMessageToWebView({
                                            type: "flyToLocation",
                                            longitude: userLocation.longitude,
                                            latitude: userLocation.latitude
                                        });
                                        mapStateManager.markUserLocationSet();
                                        hasSetUserLocationRef.current = true;
                                    }, 1000);
                                }
                            }

                            if (markersRef.current.length > 0) {
                                sendMessageToWebView({
                                    type: "setMarkers",
                                    markers: enrichMarkersForWebView(markersRef.current) as unknown as any[]
                                });
                            }
                            break;
                        }

                        case "markerPress":
                            if (onMarkerPress && message.marker) {
                                onMarkerPress(message.marker as MapMarker);
                            }
                            break;

                        case "webviewDebugLog":
                            console.warn("[WebView Debug]", message.message);
                            break;

                        case "mapViewChanged":
                            if (message.center && message.zoom) {
                                mapStateManager.setState({
                                    center: {
                                        longitude: message.center.longitude,
                                        latitude: message.center.latitude
                                    },
                                    zoom: message.zoom
                                });
                            }
                            break;

                        case "error":
                            console.error("WebView map error:", message.error);
                            setWebViewError(message.error);
                            break;
                    }
                } catch (error) {
                    console.error("Error parsing WebView message:", error);
                }
            },
            [onMarkerPress, sendMessageToWebView, safeInitialRegion, userLocation, enrichMarkersForWebView]
        );

        React.useEffect(() => {
            // console.warn("=== REACT NATIVE MARKERS EFFECT ===");
            // console.warn("Total markers passed to component:", markers.length);
            // console.warn("isMapReady:", isMapReady);
            // console.warn("Sample markers:", markers.slice(0, 2));

            const validMarkers = markers.filter(
                (marker) =>
                    marker?.coordinate?.length === 2 &&
                    typeof marker.coordinate[0] === "number" &&
                    typeof marker.coordinate[1] === "number" &&
                    isFinite(marker.coordinate[0]) &&
                    isFinite(marker.coordinate[1]) &&
                    marker.coordinate[1] >= SINGAPORE_BOUNDS.south &&
                    marker.coordinate[1] <= SINGAPORE_BOUNDS.north &&
                    marker.coordinate[0] >= SINGAPORE_BOUNDS.west &&
                    marker.coordinate[0] <= SINGAPORE_BOUNDS.east
            );

            // console.warn("Valid markers after filtering:", validMarkers.length);
            // console.warn("Sample valid markers:", validMarkers.slice(0, 2));

            // console.warn("Singapore bounds:", {
            //     west: SINGAPORE_BOUNDS.west,
            //     east: SINGAPORE_BOUNDS.east,
            //     south: SINGAPORE_BOUNDS.south,
            //     north: SINGAPORE_BOUNDS.north
            // });

            // if (markers.length > 0 && validMarkers.length === 0) {
            //     console.warn("All markers filtered out! Checking first marker:");
            //     const marker = markers[0];
            //     if (marker?.coordinate) {
            //         const [lng, lat] = marker.coordinate;
            //         console.warn("Marker coords:", lng, lat);
            //         console.warn("Coord checks:", {
            //             hasCoordinate: marker.coordinate?.length === 2,
            //             lngIsNumber: typeof lng === "number",
            //             latIsNumber: typeof lat === "number",
            //             lngFinite: isFinite(lng),
            //             latFinite: isFinite(lat),
            //             latInBounds: lat >= SINGAPORE_BOUNDS.south && lat <= SINGAPORE_BOUNDS.north,
            //             lngInBounds: lng >= SINGAPORE_BOUNDS.west && lng <= SINGAPORE_BOUNDS.east
            //         });
            //     }
            // }

            markersRef.current = validMarkers;

            if (isMapReady && validMarkers.length > 0) {
                console.warn("Sending markers to WebView:", validMarkers.length);

                const enrichedMarkers = enrichMarkersForWebView(validMarkers);

                if (isAndroid) {
                    const BATCH_SIZE = 50;
                    const batches: any[][] = [];
                    for (let i = 0; i < enrichedMarkers.length; i += BATCH_SIZE) {
                        batches.push(enrichedMarkers.slice(i, i + BATCH_SIZE) as unknown as any[]);
                    }
                    console.warn(
                        `Splitting ${validMarkers.length} markers into ${batches.length} batches of ${BATCH_SIZE}`
                    );
                    batches.forEach((batch, index) => {
                        setTimeout(
                            () => {
                                console.warn(
                                    `Sending batch ${index + 1}/${batches.length} with ${batch.length} markers`
                                );
                                sendMessageToWebView({
                                    type: "addMarkersBatch",
                                    markers: batch,
                                    batchIndex: index,
                                    totalBatches: batches.length,
                                    isLastBatch: index === batches.length - 1
                                });
                            },
                            500 + index * 200
                        );
                    });
                } else {
                    sendMessageToWebView({
                        type: "setMarkers",
                        markers: enrichedMarkers as unknown as any[]
                    });
                }
            } else {
                console.warn(
                    "NOT sending markers - isMapReady:",
                    isMapReady,
                    "validMarkers.length:",
                    validMarkers.length
                );
            }

            if (onMarkersLoadingChange) {
                onMarkersLoadingChange(!isMapReady);
            }
        }, [markers, isMapReady, sendMessageToWebView, onMarkersLoadingChange, enrichMarkersForWebView]);

        React.useEffect(() => {
            if (isMapReady && userLocation) {
                sendMessageToWebView({
                    type: "setUserLocation",
                    longitude: userLocation.longitude,
                    latitude: userLocation.latitude
                });

                // Auto-zoom to user location only on first load
                const existingState = mapStateManager.getState();
                if (!existingState?.userLocationSet && !hasSetUserLocationRef.current) {
                    setTimeout(() => {
                        sendMessageToWebView({
                            type: "flyToLocation",
                            longitude: userLocation.longitude,
                            latitude: userLocation.latitude
                        });
                        mapStateManager.markUserLocationSet();
                        hasSetUserLocationRef.current = true;
                    }, 1000);
                }
            }
        }, [userLocation, isMapReady, sendMessageToWebView]);

        const handleMyLocationPress = React.useCallback(() => {
            if (!userLocation || !isMapReady) return;
            sendMessageToWebView({
                type: "flyToLocation",
                longitude: userLocation.longitude,
                latitude: userLocation.latitude
            });
        }, [userLocation, isMapReady, sendMessageToWebView]);

        const handleWebViewError = React.useCallback((syntheticEvent: any) => {
            const { nativeEvent } = syntheticEvent;
            console.error("WebView error:", nativeEvent);
            setWebViewError(`WebView error: ${nativeEvent.description || "Unknown error"}`);
        }, []);

        if (webViewError) {
            return (
                <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                    <MyTouchable
                        onPress={() => {
                            setWebViewError(null);
                            setIsMapReady(false);
                        }}>
                        Retry Map Loading
                    </MyTouchable>
                </View>
            );
        }

        return (
            <Box className="flex-1 w-full h-full">
                <WebView
                    ref={webViewRef}
                    source={htmlContent ? { html: htmlContent } : undefined}
                    originWhitelist={["*"]}
                    style={{ flex: 1 }}
                    onMessage={handleWebViewMessage}
                    onError={handleWebViewError}
                    javaScriptEnabled={true}
                    domStorageEnabled={true}
                    allowsInlineMediaPlayback={true}
                    mediaPlaybackRequiresUserAction={false}
                    allowsBackForwardNavigationGestures={false}
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    mixedContentMode="compatibility"
                    allowFileAccessFromFileURLs={true}
                    allowUniversalAccessFromFileURLs={true}
                    startInLoadingState
                />

                {showDefaultLocationButton && (
                    <MyTouchable
                        className="absolute right-10 bottom-10 bg-white p-2 rounded-full shadow-md"
                        onPress={handleMyLocationPress}
                        disabled={isLoadingLocation || !isMapReady}>
                        {isLoadingLocation ? (
                            <ActivityIndicator size="small" color={getColor("green")} />
                        ) : (
                            <Image source={ImageAssets.icLocation} className="w-[16px] h-[16px]" alt="location" />
                        )}
                    </MyTouchable>
                )}
            </Box>
        );
    }
);

const MemoizedMapComponent = React.memo(MapComponent);

export default MemoizedMapComponent;
