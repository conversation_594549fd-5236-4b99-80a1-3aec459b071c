<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>MapLibre GL JS with Clustering</title>
        <script src="https://unpkg.com/maplibre-gl@latest/dist/maplibre-gl.js"></script>
        <script src="https://unpkg.com/supercluster@8.0.1/dist/supercluster.min.js"></script>
        <link href="https://unpkg.com/maplibre-gl@latest/dist/maplibre-gl.css" rel="stylesheet" />
        <style>
            body {
                margin: 0;
                padding: 0;
                font-family: "Arial", sans-serif;
            }
            #map {
                position: absolute;
                top: 0;
                bottom: 0;
                width: 100%;
            }
            .marker-pin {
                background: #4285f4;
                border: 2px solid #ffffff;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                cursor: pointer;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                transition: transform 0.2s;
            }
            .marker-pin:hover {
                transform: scale(1.1);
            }
            .cluster-marker {
                background: #ff6b6b;
                border: 3px solid #ffffff;
                border-radius: 50%;
                color: white;
                font-weight: bold;
                font-size: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
                transition: transform 0.2s;
            }
            .cluster-marker:hover {
                transform: scale(1.1);
            }
            .cluster-small {
                width: 30px;
                height: 30px;
            }
            .cluster-medium {
                width: 40px;
                height: 40px;
                background: #ff8c42;
            }
            .cluster-large {
                width: 50px;
                height: 50px;
                background: #ff3030;
            }
            [data-user-location="true"] {
                background: #ff0000 !important;
                border: 2px solid #ffffff !important;
                box-shadow: 0 0 0 4px rgba(255, 0, 0, 0.3) !important;
                border-radius: 50% !important;
                z-index: 2147483647 !important;
            }
        </style>
    </head>
    <body>
        <div id="map"></div>

        <script>
            class MapLibreWebView {
                constructor() {
                    this.map = null;
                    this.supercluster = null;
                    this.markers = [];
                    this.currentMarkers = new Map();
                    this.isInitialized = false;
                    this.isAndroid = this.detectAndroid();

                    this.SINGAPORE_BOUNDS = {
                        north: 1.48,
                        south: 1.16,
                        east: 104.05,
                        west: 103.58
                    };

                    this.SINGAPORE_MIN_ZOOM = 10;
                    this.SINGAPORE_MAX_ZOOM = 20;
                    this.CLUSTER_ZOOM_THRESHOLD = 15;

                    this.init();
                }

                elevateOnPress(element, baseZ = 100) {
                    const elevatedZ = 2147483000;
                    const onPointerDown = (e) => {
                        try {
                            e.stopPropagation();
                            element.style.zIndex = String(elevatedZ);
                        } catch {}
                    };
                    const onPointerUp = (e) => {
                        try {
                            e.stopPropagation();
                            element.style.zIndex = String(baseZ);
                        } catch {}
                    };
                    element.addEventListener("pointerdown", onPointerDown, { passive: false });
                    element.addEventListener("pointerup", onPointerUp, { passive: false });
                    element.addEventListener("touchstart", onPointerDown, { passive: false });
                    element.addEventListener("touchend", onPointerUp, { passive: false });
                    element.addEventListener("mousedown", onPointerDown, { passive: false });
                    element.addEventListener("mouseup", onPointerUp, { passive: false });
                }

                detectAndroid() {
                    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
                    const isAndroidUA = /android/i.test(userAgent);
                    return !!isAndroidUA;
                }

                log(message, ...args) {
                    try {
                        console.log(message, ...args);
                        if (window.ReactNativeWebView && (message.includes("===") || message.includes("batch"))) {
                            const timestamp = new Date().toISOString();
                            window.ReactNativeWebView.postMessage(
                                JSON.stringify({ type: "webviewDebugLog", message: `[${timestamp}] ${message}` })
                            );
                        }
                    } catch {}
                }

                addMarkersBatch(batchMarkers, batchIndex, totalBatches, isLastBatch) {
                    this.log(`=== ADD MARKERS BATCH ${batchIndex + 1}/${totalBatches} ===`);

                    if (!this.isInitialized || !Array.isArray(batchMarkers)) return;

                    if (batchIndex === 0) {
                        this.markers = [];
                        this.clearMarkers();
                    }

                    const validBatchMarkers = batchMarkers.filter(
                        (marker) =>
                            marker &&
                            marker.coordinate &&
                            Array.isArray(marker.coordinate) &&
                            marker.coordinate.length === 2 &&
                            typeof marker.coordinate[0] === "number" &&
                            typeof marker.coordinate[1] === "number" &&
                            isFinite(marker.coordinate[0]) &&
                            isFinite(marker.coordinate[1])
                    );

                    this.markers = this.markers.concat(validBatchMarkers);

                    if (isLastBatch) {
                        const SuperclusterGlobal = window.Supercluster || window.supercluster;
                        if (!SuperclusterGlobal) return;

                        const superclusterOptions = {
                            radius: 120,
                            maxZoom: this.CLUSTER_ZOOM_THRESHOLD - 1,
                            minZoom: 0,
                            minPoints: 2
                        };

                        try {
                            this.supercluster = new SuperclusterGlobal(superclusterOptions);
                        } catch (e) {
                            try {
                                this.supercluster = SuperclusterGlobal(superclusterOptions);
                            } catch (e2) {
                                this.log("Failed to initialize Supercluster:", e2.message);
                                return;
                            }
                        }

                        const points = this.markers.map((marker) => ({
                            type: "Feature",
                            properties: {
                                id: marker.id,
                                title: marker.title,
                                description: marker.description,
                                category: marker.category,
                                iconImage: marker.iconImage,
                                iconImageUrl: marker.iconImageUrl,
                                backgroundImageUrl: marker.backgroundImageUrl
                            },
                            geometry: {
                                type: "Point",
                                coordinates: marker.coordinate
                            }
                        }));

                        this.supercluster.load(points);
                        this.updateClusters();
                    }
                }

                init() {
                    try {
                        if (typeof maplibregl !== "undefined") {
                            try {
                                maplibregl.workerUrl =
                                    "https://unpkg.com/maplibre-gl@latest/dist/maplibre-gl-csp-worker.js";
                                maplibregl.workerCount = 1;
                            } catch (e) {}
                        }

                        this.map = new maplibregl.Map({
                            container: "map",
                            attributionControl: false,
                            style: {
                                version: 8,
                                sources: {
                                    cartodb: {
                                        type: "raster",
                                        tiles: [
                                            "https://a.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}.png",
                                            "https://b.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}.png",
                                            "https://c.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}.png",
                                            "https://d.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}.png"
                                        ],
                                        tileSize: 256
                                    }
                                },
                                layers: [
                                    {
                                        id: "cartodb",
                                        type: "raster",
                                        source: "cartodb",
                                        paint: {
                                            "raster-fade-duration": 100
                                        }
                                    }
                                ]
                            },
                            center: [103.8198, 1.3521],
                            zoom: 16,
                            minZoom: this.SINGAPORE_MIN_ZOOM,
                            maxZoom: this.SINGAPORE_MAX_ZOOM,
                            maxBounds: [
                                [this.SINGAPORE_BOUNDS.west, this.SINGAPORE_BOUNDS.south],
                                [this.SINGAPORE_BOUNDS.east, this.SINGAPORE_BOUNDS.north]
                            ]
                        });

                        this.map.on("load", () => {
                            this.isInitialized = true;
                            this.setupEventListeners();
                            this.postMessage({ type: "mapReady" });
                        });

                        this.map.on("moveend", () => {
                            this.updateClusters();
                            if (this.isAndroid) this.updateAllAndroidMarkers();
                            this.notifyMapViewChanged();
                        });

                        this.map.on("zoomend", () => {
                            this.updateClusters();
                            if (this.isAndroid) this.updateAllAndroidMarkers();
                            this.notifyMapViewChanged();
                        });

                        this.map.on("move", () => {
                            if (this.isAndroid) this.updateAllAndroidMarkers();
                        });
                    } catch (error) {
                        console.error("Error initializing map:", error);
                        this.postMessage({ type: "error", error: error.message });
                    }
                }

                setupEventListeners() {
                    const handleMessage = (event) => {
                        const data = event && event.data ? event.data : event;
                        this.handleMessage(data);
                    };

                    if (window.ReactNativeWebView) {
                        window.addEventListener("message", handleMessage);
                        document.addEventListener("message", handleMessage);
                    } else {
                        window.addEventListener("message", handleMessage);
                    }
                }

                handleMessage(data) {
                    try {
                        const message = typeof data === "string" ? JSON.parse(data) : data;
                        switch (message.type) {
                            case "setMarkers":
                                this.setMarkers(message.markers);
                                break;
                            case "clearMarkers":
                                this.markers = [];
                                this.clearMarkers();
                                break;
                            case "addMarkersBatch":
                                if (this.isAndroid) {
                                    this.addMarkersBatch(
                                        message.markers,
                                        message.batchIndex,
                                        message.totalBatches,
                                        message.isLastBatch
                                    );
                                } else {
                                    this.log("Ignoring addMarkersBatch on iOS");
                                }
                                break;
                            case "setCenter":
                                this.setCenter(message.longitude, message.latitude, message.zoom);
                                break;
                            case "setUserLocation":
                                this.setUserLocation(message.longitude, message.latitude);
                                break;
                            case "flyToLocation":
                                this.flyToLocation(message.longitude, message.latitude);
                                break;
                            default:
                                this.log("Unknown message type:", message.type);
                        }
                    } catch (error) {
                        console.error("Error handling message:", error);
                    }
                }

                setMarkers(markers) {
                    if (!this.isInitialized || !Array.isArray(markers)) return;

                    this.markers = markers.filter(
                        (marker) =>
                            marker &&
                            marker.coordinate &&
                            Array.isArray(marker.coordinate) &&
                            marker.coordinate.length === 2 &&
                            typeof marker.coordinate[0] === "number" &&
                            typeof marker.coordinate[1] === "number" &&
                            isFinite(marker.coordinate[0]) &&
                            isFinite(marker.coordinate[1])
                    );

                    const SuperclusterGlobal = window.Supercluster || window.supercluster;
                    if (!SuperclusterGlobal) {
                        console.error("Supercluster library not available on window.");
                        this.postMessage({ type: "error", error: "Supercluster not loaded" });
                        return;
                    }

                    const superclusterOptions = {
                        radius: 120,
                        maxZoom: this.CLUSTER_ZOOM_THRESHOLD - 1,
                        minZoom: 0,
                        minPoints: 2
                    };

                    try {
                        this.supercluster = new SuperclusterGlobal(superclusterOptions);
                    } catch (e) {
                        try {
                            this.supercluster = SuperclusterGlobal(superclusterOptions);
                        } catch (e2) {
                            console.error("Failed to initialize Supercluster:", e2);
                            this.postMessage({ type: "error", error: "Supercluster init failed" });
                            return;
                        }
                    }

                    const points = this.markers.map((marker) => ({
                        type: "Feature",
                        properties: {
                            id: marker.id,
                            title: marker.title,
                            description: marker.description,
                            category: marker.category,
                            iconImage: marker.iconImage,
                            iconImageUrl: marker.iconImageUrl,
                            backgroundImageUrl: marker.backgroundImageUrl
                        },
                        geometry: {
                            type: "Point",
                            coordinates: marker.coordinate
                        }
                    }));

                    this.supercluster.load(points);
                    this.updateClusters();
                }

                updateClusters() {
                    if (!this.supercluster || !this.isInitialized) return;

                    this.clearMarkers();

                    const bounds = this.map.getBounds();
                    const rawZoom = this.map.getZoom();
                    const zoom = Math.max(0, Math.floor(rawZoom));

                    if (zoom >= this.CLUSTER_ZOOM_THRESHOLD) {
                        const visibleMarkers = [];
                        for (const marker of this.markers) {
                            const [lng, lat] = marker.coordinate;
                            if (
                                lng >= bounds.getWest() &&
                                lng <= bounds.getEast() &&
                                lat >= bounds.getSouth() &&
                                lat <= bounds.getNorth()
                            ) {
                                const feature = {
                                    type: "Feature",
                                    properties: {
                                        id: marker.id,
                                        title: marker.title,
                                        description: marker.description,
                                        category: marker.category,
                                        iconImage: marker.iconImage,
                                        iconImageUrl: marker.iconImageUrl,
                                        backgroundImageUrl: marker.backgroundImageUrl
                                    },
                                    geometry: { type: "Point", coordinates: [lng, lat] }
                                };
                                visibleMarkers.push({ feature, lng, lat });
                            }
                        }

                        const groups = new Map();
                        for (const item of visibleMarkers) {
                            const key = `${item.lng.toFixed(7)},${item.lat.toFixed(7)}`;
                            if (!groups.has(key)) groups.set(key, []);
                            groups.get(key).push(item);
                        }

                        groups.forEach((items) => {
                            const count = items.length;
                            if (count === 1) {
                                const { feature, lng, lat } = items[0];
                                this.createIndividualMarker(feature, lng, lat, [0, 0]);
                                return;
                            }

                            const baseRadius = 18;
                            const radius = Math.min(40, baseRadius + Math.max(0, count - 2) * 3);
                            for (let i = 0; i < count; i++) {
                                const angle = (2 * Math.PI * i) / count;
                                const offsetX = Math.round(radius * Math.cos(angle));
                                const offsetY = Math.round(radius * Math.sin(angle));
                                const { feature, lng, lat } = items[i];
                                this.createIndividualMarker(feature, lng, lat, [offsetX, offsetY]);
                            }
                        });

                        return;
                    }

                    const clusters = this.supercluster.getClusters(
                        [bounds.getWest(), bounds.getSouth(), bounds.getEast(), bounds.getNorth()],
                        zoom
                    );

                    if (window.ReactNativeWebView) {
                        try {
                            window.ReactNativeWebView.postMessage(
                                JSON.stringify({ type: "clustersUpdated", zoom, clusters: clusters.length })
                            );
                        } catch {}
                    }

                    clusters.forEach((cluster) => {
                        const [longitude, latitude] = cluster.geometry.coordinates;

                        if (cluster.properties.cluster) {
                            this.createClusterMarker(cluster, longitude, latitude);
                        } else {
                            this.createIndividualMarker(cluster, longitude, latitude);
                        }
                    });
                }

                createClusterMarker(cluster, longitude, latitude) {
                    const count = cluster.properties.point_count;
                    const clusterId = cluster.properties.cluster_id;

                    if (this.isAndroid) {
                        this.createAndroidClusterMarker(cluster, longitude, latitude);
                        return;
                    }

                    const el = document.createElement("div");
                    el.style.width = "50px";
                    el.style.height = "50px";
                    el.style.minWidth = "50px";
                    el.style.minHeight = "50px";
                    el.style.borderRadius = "100px";
                    el.style.border = "3px solid rgba(255,255,255,0.9)";
                    el.style.display = "flex";
                    el.style.alignItems = "center";
                    el.style.justifyContent = "center";
                    el.style.backgroundImage = "linear-gradient(135deg, #0B3C5D 0%, #2E86DE 100%)";
                    el.style.boxShadow = "0 3px 4.65px rgba(0,0,0,0.3)";
                    el.style.cursor = "pointer";
                    el.style.userSelect = "none";
                    el.style.zIndex = "1000";

                    const text = document.createElement("span");
                    text.textContent = count > 999 ? "999+" : String(count);
                    text.style.color = "#FFFFFF";
                    text.style.fontWeight = "700";
                    text.style.fontSize = "16px";
                    text.style.textShadow = "1px 1px 2px rgba(0,0,0,0.25)";
                    text.style.lineHeight = "1";
                    text.style.display = "block";
                    el.appendChild(text);

                    el.addEventListener("click", () => {
                        const expansionZoom = this.supercluster.getClusterExpansionZoom(clusterId);
                        this.map.flyTo({
                            center: [longitude, latitude],
                            zoom: Math.min(expansionZoom, this.SINGAPORE_MAX_ZOOM)
                        });
                    });

                    const marker = new maplibregl.Marker({ element: el, anchor: "center" })
                        .setLngLat([longitude, latitude])
                        .addTo(this.map);

                    this.currentMarkers.set("cluster-" + clusterId, marker);
                }

                createAndroidClusterMarker(cluster, longitude, latitude) {
                    const count = cluster.properties.point_count;
                    const clusterId = cluster.properties.cluster_id;

                    const el = document.createElement("div");
                    el.id = "cluster-" + clusterId;
                    el.textContent = count > 999 ? "999+" : String(count);
                    el.style.position = "absolute";
                    el.style.width = "50px";
                    el.style.height = "50px";
                    el.style.borderRadius = "100px";
                    el.style.backgroundImage = "linear-gradient(135deg, #0B3C5D 0%, #2E86DE 100%)";
                    el.style.border = "3px solid white";
                    el.style.color = "white";
                    el.style.fontSize = "14px";
                    el.style.fontWeight = "bold";
                    el.style.display = "flex";
                    el.style.alignItems = "center";
                    el.style.justifyContent = "center";
                    el.style.zIndex = "999999";
                    el.style.opacity = "1";
                    el.style.visibility = "visible";
                    el.style.pointerEvents = "auto";
                    el.style.transform = "translate(-50%, -50%)";

                    const mapContainer = this.map.getContainer();
                    if (mapContainer) {
                        mapContainer.appendChild(el);
                        try {
                            const point = this.map.project([longitude, latitude]);
                            el.style.left = point.x + "px";
                            el.style.top = point.y + "px";
                        } catch (error) {
                            el.style.left = "50%";
                            el.style.top = "50%";
                        }
                    }

                    el.addEventListener("click", (e) => {
                        e.stopPropagation();
                        const expansionZoom = this.supercluster.getClusterExpansionZoom(clusterId);
                        this.map.flyTo({
                            center: [longitude, latitude],
                            zoom: Math.min(expansionZoom, this.SINGAPORE_MAX_ZOOM)
                        });
                    });

                    this.currentMarkers.set("cluster-" + clusterId, {
                        element: el,
                        lngLat: [longitude, latitude],
                        remove: () => {
                            if (el.parentNode) el.parentNode.removeChild(el);
                        }
                    });
                }

                createIndividualMarker(feature, longitude, latitude, offsetPx = [0, 0]) {
                    const properties = feature.properties;

                    if (this.isAndroid) {
                        this.createAndroidIndividualMarker(feature, longitude, latitude, offsetPx);
                        return;
                    }

                    const container = document.createElement("div");
                    container.style.width = "40px";
                    container.style.height = "40px";
                    container.style.display = "flex";
                    container.style.alignItems = "center";
                    container.style.justifyContent = "center";
                    container.style.zIndex = "100";
                    container.style.cursor = "pointer";
                    container.style.userSelect = "none";
                    container.style.pointerEvents = "auto";
                    container.style.touchAction = "manipulation";
                    container.style.borderRadius = "100px";
                    container.style.border = "5px solid rgba(255,255,255,0.9)";
                    container.style.backgroundColor = "#E0E6ED";

                    const iconUrl =
                        (properties.iconImageUrl && typeof properties.iconImageUrl === "string"
                            ? properties.iconImageUrl
                            : null) ||
                        (properties.iconImage && typeof properties.iconImage === "string"
                            ? properties.iconImage
                            : null) ||
                        (properties.iconImage && typeof properties.iconImage === "object" && properties.iconImage.uri
                            ? properties.iconImage.uri
                            : null);

                    if (iconUrl) {
                        const img = document.createElement("img");
                        img.src = iconUrl;
                        img.alt = "marker icon";
                        img.style.width = "24px";
                        img.style.height = "24px";
                        img.style.objectFit = "contain";
                        img.style.display = "block";
                        container.appendChild(img);
                    }

                    this.elevateOnPress(container, 100);
                    container.addEventListener("click", () => {
                        this.postMessage({
                            type: "markerPress",
                            marker: {
                                id: properties.id,
                                coordinate: [longitude, latitude],
                                title: properties.title,
                                description: properties.description,
                                category: properties.category,
                                iconImage: properties.iconImage
                            }
                        });
                    });

                    const marker = new maplibregl.Marker({ element: container, anchor: "center", offset: offsetPx })
                        .setLngLat([longitude, latitude])
                        .addTo(this.map);

                    this.currentMarkers.set("marker-" + properties.id, marker);
                }

                createAndroidIndividualMarker(feature, longitude, latitude, offsetPx = [0, 0]) {
                    const properties = feature.properties;

                    const container = document.createElement("div");
                    container.className = "android-individual-marker";
                    container.style.position = "absolute";
                    container.style.width = "40px";
                    container.style.height = "40px";
                    container.style.display = "flex";
                    container.style.alignItems = "center";
                    container.style.justifyContent = "center";
                    container.style.zIndex = "9999";
                    container.style.pointerEvents = "auto";
                    container.style.touchAction = "manipulation";
                    container.style.transform = `translate(calc(-50% + ${offsetPx[0]}px), calc(-50% + ${offsetPx[1]}px))`;
                    container.style.borderRadius = "100px";
                    container.style.border = "5px solid rgba(255,255,255,0.9)";
                    container.style.backgroundColor = "#E0E6ED";

                    const iconUrl = properties.iconImageUrl || properties.iconImage;
                    if (iconUrl) {
                        const img = document.createElement("img");
                        img.src = iconUrl;
                        img.alt = "marker icon";
                        img.style.width = "24px";
                        img.style.height = "24px";
                        img.style.objectFit = "contain";
                        container.appendChild(img);
                    }

                    this.elevateOnPress(container, 9999);
                    container.addEventListener("click", () => {
                        this.postMessage({
                            type: "markerPress",
                            marker: {
                                id: properties.id,
                                coordinate: [longitude, latitude],
                                title: properties.title,
                                description: properties.description,
                                category: properties.category,
                                iconImage: properties.iconImage
                            }
                        });
                    });

                    const mapContainer = this.map.getContainer();
                    mapContainer.appendChild(container);
                    this.updateAndroidMarkerPosition(container, longitude, latitude);

                    this.currentMarkers.set("marker-" + properties.id, {
                        element: container,
                        lngLat: [longitude, latitude],
                        remove: () => {
                            if (container.parentNode) container.parentNode.removeChild(container);
                        }
                    });
                }

                updateAndroidMarkerPosition(element, longitude, latitude) {
                    try {
                        const point = this.map.project([longitude, latitude]);
                        element.style.left = point.x + "px";
                        element.style.top = point.y + "px";
                    } catch (error) {
                        console.error("Error positioning Android marker:", error);
                    }
                }

                updateAllAndroidMarkers() {
                    if (!this.isAndroid) return;
                    this.currentMarkers.forEach((marker) => {
                        if (marker.element && marker.lngLat) {
                            this.updateAndroidMarkerPosition(marker.element, marker.lngLat[0], marker.lngLat[1]);
                        }
                    });
                }

                clearMarkers() {
                    this.currentMarkers.forEach((marker) => marker.remove && marker.remove());
                    this.currentMarkers.clear();
                }

                setCenter(longitude, latitude, zoom = null) {
                    if (!this.isInitialized) return;
                    this.map.setCenter([longitude, latitude]);
                    if (zoom !== null) {
                        this.map.setZoom(Math.max(this.SINGAPORE_MIN_ZOOM, Math.min(this.SINGAPORE_MAX_ZOOM, zoom)));
                    }
                }

                setUserLocation(longitude, latitude) {
                    if (!this.isInitialized) return;
                    if (this.userLocationMarker) {
                        this.userLocationMarker.remove();
                    }

                    const el = document.createElement("div");
                    el.setAttribute("data-user-location", "true");
                    el.style.position = "relative";
                    el.style.width = "14px";
                    el.style.height = "14px";
                    el.style.backgroundColor = "#2E86DE";
                    el.style.border = "2px solid #FFFFFF";
                    el.style.borderRadius = "50%";
                    el.style.boxShadow = "0 0 0 4px rgba(255, 0, 0, 0.3)";
                    el.style.zIndex = "2147483647";
                    el.style.pointerEvents = "none";

                    this.userLocationMarker = new maplibregl.Marker({ element: el, anchor: "center" })
                        .setLngLat([longitude, latitude])
                        .addTo(this.map);

                    this.postMessage({ type: "userLocationUpdated", longitude, latitude });
                }

                flyToLocation(longitude, latitude) {
                    if (!this.isInitialized) return;
                    this.map.flyTo({ center: [longitude, latitude], zoom: 16, duration: 800 });
                }

                notifyMapViewChanged() {
                    if (!this.isInitialized || !this.map) return;
                    const center = this.map.getCenter();
                    const zoom = this.map.getZoom();
                    this.postMessage({
                        type: "mapViewChanged",
                        center: { longitude: center.lng, latitude: center.lat },
                        zoom: zoom
                    });
                }

                postMessage(message) {
                    if (window.ReactNativeWebView) {
                        window.ReactNativeWebView.postMessage(JSON.stringify(message));
                    }
                }
            }

            if (document.readyState === "loading") {
                document.addEventListener("DOMContentLoaded", () => {
                    new MapLibreWebView();
                });
            } else {
                new MapLibreWebView();
            }
        </script>
    </body>
</html>
