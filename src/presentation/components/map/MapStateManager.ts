interface MapState {
    center: { longitude: number; latitude: number };
    zoom: number;
    isInitialized: boolean;
    userLocationSet: boolean;
}

interface MapStateManager {
    getState(): MapState | null;
    setState(state: Partial<MapState>): void;
    markInitialized(): void;
    markUserLocationSet(): void;
    reset(): void;
}

let globalMapState: MapState | null = null;

export const mapStateManager: MapStateManager = {
    getState: () => globalMapState,

    setState: (newState: Partial<MapState>) => {
        if (!globalMapState) {
            globalMapState = {
                center: { longitude: 103.8518, latitude: 1.2966 }, // Default Singapore
                zoom: 11,
                isInitialized: false,
                userLocationSet: false
            };
        }
        globalMapState = { ...globalMapState, ...newState };
    },

    markInitialized: () => {
        if (!globalMapState) {
            mapStateManager.setState({});
        }
        globalMapState!.isInitialized = true;
    },

    markUserLocationSet: () => {
        if (!globalMapState) {
            mapStateManager.setState({});
        }
        globalMapState!.userLocationSet = true;
    },

    reset: () => {
        globalMapState = null;
    }
};
