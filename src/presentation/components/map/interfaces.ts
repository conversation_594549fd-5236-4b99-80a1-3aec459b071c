import React from "react";
import { PointFeature, ClusterFeature as SuperClusterFeature } from "supercluster";

export interface ViewportBounds {
    north: number;
    south: number;
    east: number;
    west: number;
}

export interface MarkerCache {
    [key: string]: React.ReactElement;
}

export interface PerformanceMetrics {
    renderTime: number;
    markerCount: number;
    clusterCount: number;
    memoryUsage: number;
}

export interface MarkerProperties {
    markerId?: string;
    title?: string;
    description?: string;
    iconImage?: number | { uri: string };
    category?: string;
    originalIndex?: number;
    cluster?: boolean;
    cluster_id?: number;
    id?: string | number;
    point_count?: number;
    count?: number;
    point_count_abbreviated?: string;
}

export type ClusterOrMarkerFeature = PointFeature<MarkerProperties> | SuperClusterFeature<MarkerProperties>;

export function isClusterFeature(feature: ClusterOrMarkerFeature): feature is SuperClusterFeature<MarkerProperties> {
    return "cluster" in feature.properties && feature.properties.cluster === true;
}
