export type MapMarker = {
    id: string;
    coordinate: [number, number];
    title?: string;
    description?: string;
    iconImage?: { uri: string | undefined } | undefined;
    category?: string;
};

export interface MapComponentProps {
    markers?: MapMarker[];
    initialRegion?: {
        longitude: number;
        latitude: number;
        zoomLevel: number;
    };
    onMarkerPress?: (marker: MapMarker) => void;
    userLocation?: {
        longitude: number;
        latitude: number;
    } | null;
    isLoadingLocation?: boolean;
    onMarkersLoadingChange?: (isLoading: boolean) => void;
    showDefaultLocationButton?: boolean;
}

export interface MapComponentRef {
    handleMyLocationPress: () => void;
}
