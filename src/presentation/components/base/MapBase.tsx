import { MapView, MapViewRef } from "@maplibre/maplibre-react-native";
import React from "react";
import { ViewProps } from "react-native";

import { fullHeight, fullWidth } from "@/shared/helper";
import { cleanupMapLibreErrorHandling, initializeMapLibreErrorHandling } from "@/shared/helper/maplibreErrorHandler";

type MapBaseProps = {
    style?: ViewProps["style"];
    onDidFinishRenderingMapFully?: () => void;
    onRegionWillChange?: () => void;
    onRegionDidChange?: () => void;
    children: React.ReactNode;
};

const MapBase = React.forwardRef<MapViewRef, MapBaseProps>(
    (
        {
            style = { width: fullWidth, height: fullHeight },
            onDidFinishRenderingMapFully,
            onRegionWillChange,
            onRegionDidChange,
            children
        },
        ref
    ) => {
        const mapStyle = React.useMemo(
            () => ({
                version: 8,
                zoom: 14,
                minzoom: 13,
                maxzoom: 20,
                sources: {
                    cartodb: {
                        type: "raster",
                        tiles: [
                            "https://a.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}.png",
                            "https://b.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}.png",
                            "https://c.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}.png",
                            "https://d.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}.png"
                        ],
                        tileSize: 256,
                        attribution: "© OpenStreetMap contributors © CARTO",
                        maxZoom: 20
                    }
                },
                layers: [
                    {
                        id: "cartodb",
                        type: "raster",
                        source: "cartodb",
                        paint: {
                            "raster-fade-duration": 100
                        }
                    }
                ]
            }),
            []
        );

        React.useEffect(() => {
            initializeMapLibreErrorHandling();
            return () => {
                cleanupMapLibreErrorHandling();
            };
        }, []);

        return (
            <MapView
                ref={ref as React.RefObject<MapViewRef>}
                style={style}
                attributionEnabled={false}
                mapStyle={mapStyle}
                logoEnabled={false}
                onDidFinishRenderingMapFully={onDidFinishRenderingMapFully}
                onRegionWillChange={onRegionWillChange}
                onRegionDidChange={onRegionDidChange}>
                {children}
            </MapView>
        );
    }
);

export default MapBase;
