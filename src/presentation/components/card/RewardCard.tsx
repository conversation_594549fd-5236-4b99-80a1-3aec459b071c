import { useNavigation } from "@react-navigation/native";
import React from "react";

import { getColor } from "@/presentation/hooks";

import { BoxCard } from "../boxCard";
import { RewardCardProgressSkeleton } from "../skeleton";
import { Box, HStack, IconComponent, Image, Text } from "../ui";
import ProgressBar from "../ui/ProgressBar";

import { usePoint, usePointGoal } from "@/presentation/hooks/point";
import { ImageAssets, RouteName } from "@/shared/constants";
import { formatPoints } from "@/shared/helper";

const RewardCard = () => {
    const navigation = useNavigation();
    const { point } = usePoint();
    const { pointGoal, isLoading, isWithinDailyLimit } = usePointGoal();

    const handleMyVoucher = React.useCallback(() => {
        navigation.navigate(RouteName.MyVoucher);
    }, [navigation]);

    // const handleInfoPress = React.useCallback(() => {
    //     ShowBottomSheetObs.action({
    //         title: "CO₂ Points",
    //         titleButtonConfirm: "OKay"
    //     });
    // }, []);

    const progressPercentage =
        ((Number(pointGoal?.today_reward) ?? 0) / (Number(pointGoal?.daily_point_goal) ?? 1)) * 100;

    return (
        <BoxCard>
            <Box className="px-5">
                <Box className="mb-4">
                    <HStack className="items-center justify-between py-4">
                        <Image
                            source={ImageAssets.co2Points}
                            className="h-[24px]"
                            resizeMode="contain"
                            alt="CO₂ Points"
                            tintColor="white"
                        />
                        <HStack className="items-center gap-2">
                            <Image source={ImageAssets.icPoint} className="w-[24px] h-[24px]" alt="Point" />
                            <Text className="text-[24px] font-bold text-white">{point}</Text>
                        </HStack>
                    </HStack>

                    {isLoading ? (
                        <RewardCardProgressSkeleton />
                    ) : (
                        <Box>
                            <Box className="mb-2">
                                <ProgressBar
                                    progress={progressPercentage}
                                    height={8}
                                    backgroundColor="#18336266"
                                    colors={[getColor("lightBlue"), getColor("blue")]}
                                    borderRadius={8}
                                    showPercentage={false}
                                />
                            </Box>

                            <HStack className="items-center justify-between">
                                <Text className="text-white text-[13px]">Today&apos;s Points Goal</Text>
                                <Text className="text-white text-[13px] font-bold">
                                    {formatPoints(Number(pointGoal?.today_reward))}/
                                    {formatPoints(Number(pointGoal?.daily_point_goal))} pts
                                </Text>
                            </HStack>
                            {isWithinDailyLimit && (
                                <Text className="text-white text-[10px] font-bold">
                                    🎉 Daily Max Reached – You&apos;re a Recycling Hero!
                                </Text>
                            )}
                        </Box>
                    )}
                </Box>
                <Image
                    source={ImageAssets.lineWhite}
                    className="h-[2px] w-full"
                    resizeMode="cover"
                    alt="Line"
                    tintColor="white"
                />
                <HStack className="items-center justify-between py-4" onPress={handleMyVoucher}>
                    <HStack className="items-center gap-2">
                        <Image
                            source={ImageAssets.icVoucher}
                            className="h-[14px] w-[20px]"
                            resizeMode="cover"
                            alt="voucher"
                            tintColor="white"
                        />
                        <Text className="text-white font-bold">My Voucher</Text>
                    </HStack>
                    <IconComponent name="chevron-right" font="feather" size={20} color="white" />
                </HStack>
            </Box>
        </BoxCard>
    );
};

export default RewardCard;
