import React from "react";
import Svg, { Defs, G, Path, Rect } from "react-native-svg";

import { getColor } from "@/presentation/hooks";

import { MyTouchable } from "../touchable";
import { Box, HStack, Text } from "../ui";

import { environment } from "@/data/services/environment";
import { WebViewObs } from "@/data/services/observable";
import { EndPoint } from "@/shared/constants";

type CheckBoxProps = {
    onChange?: (value: boolean) => void;
    disabled?: boolean;
    value?: boolean;
    title?: string;
};

const CheckBox: React.FC<CheckBoxProps> = ({ onChange, disabled, value = false, title }) => {
    const handleCheck = React.useCallback(() => {
        onChange?.(!value);
    }, [value, onChange]);

    const _renderCheck = React.useMemo(() => {
        if (value) {
            return (
                <Svg width={24} height={24} fill="none">
                    <Rect width={24} height={24} fill={getColor("green")} rx={4} />
                    <Path
                        fill="#fff"
                        d="M19.629 6.871c.316.352.316.879 0 1.195l-9.281 9.282c-.352.351-.88.351-1.196 0l-4.78-4.782a.77.77 0 0 1 0-1.16.77.77 0 0 1 1.16 0l4.183 4.184 8.719-8.719a.828.828 0 0 1 1.16 0h.035Z"
                    />
                </Svg>
            );
        }

        return (
            <Svg width={24} height={24} fill="none">
                <G filter="url(#a)">
                    <Rect width={24} height={24} fill="#fff" rx={4} />
                    <Rect width={22} height={22} x={1} y={1} stroke="#bbbbbb" strokeWidth={2} rx={3} />
                </G>
                <Defs />
            </Svg>
        );
    }, [value]);

    const handleTermsPress = React.useCallback(() => {
        WebViewObs.action({ title: "Terms & Conditions", uri: `${environment.url}${EndPoint.policies.terms}` });
    }, []);

    const handlePrivacyPress = React.useCallback(() => {
        WebViewObs.action({ title: "Privacy Policy", uri: `${environment.url}${EndPoint.policies.privacy}` });
    }, []);

    return (
        <MyTouchable onPress={handleCheck} disabled={disabled}>
            <HStack space="md" className="items-center">
                <Box className="pt-1">{_renderCheck}</Box>
                <Box className="flex-1">
                    {title ? (
                        <Text>{title}</Text>
                    ) : (
                        <Text>
                            By signing up, you agree to our{" "}
                            <Text onPress={handleTermsPress} className="text-blue">
                                Terms & Conditions
                            </Text>{" "}
                            and{" "}
                            <Text onPress={handlePrivacyPress} className="text-blue">
                                Privacy Policy
                            </Text>
                        </Text>
                    )}
                </Box>
            </HStack>
        </MyTouchable>
    );
};

export default CheckBox;
