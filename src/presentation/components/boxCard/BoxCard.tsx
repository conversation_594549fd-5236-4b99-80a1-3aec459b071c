import React from "react";
import { ImageBackground } from "react-native";

import { Box } from "../ui";

import { ImageAssets } from "@/shared/constants";

type BoxCardProps = {
    children: React.ReactNode;
    mx?: number;
};

const BoxCard: React.FC<BoxCardProps> = ({ children, mx = 5 }) => {
    return (
        <Box className={`mx-${mx} mb-6`}>
            <Box className="rounded-xl overflow-hidden">
                <ImageBackground source={ImageAssets.accentBackground} resizeMode="cover" alt="Accent Background">
                    {children}
                </ImageBackground>
            </Box>
        </Box>
    );
};

export default BoxCard;
