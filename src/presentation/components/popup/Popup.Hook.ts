import React from "react";

import { useDeepEffect } from "@/presentation/hooks";

import { TypePopup } from "./Popup";

import { ShowPopupRequiredObs } from "@/data/services/observable";

export const usePayload = (): {
    isOpen: boolean;
    payload?: ShowPopupRequiredObsPayload<TypePopup>;
    handleClose: () => void;
} => {
    const [isOpen, setIsOpen] = React.useState<boolean>(false);
    const [payload, setPayload] = React.useState<ShowPopupRequiredObsPayload<TypePopup>>();

    const handleClose = React.useCallback(() => {
        setIsOpen(false);
        setPayload(undefined);
    }, []);

    React.useEffect(() => {
        const subscription = ShowPopupRequiredObs.subscribe((params: ShowPopupRequiredObsPayload<TypePopup> | null) => {
            setPayload(params!);
        });

        return () => subscription.unsubscribe();
    }, [handleClose]);

    useDeepEffect(() => {
        if (!payload) return;
        setIsOpen(true);
    }, [isOpen, payload]);

    return React.useMemo(
        () => ({
            isOpen,
            payload,
            handleClose
        }),
        [isOpen, payload, handleClose]
    );
};
