import React from "react";
import { Linking } from "react-native";

import { getColor } from "@/presentation/hooks";

import { MyButton } from "../myButton";
import { Box, IconComponent, Image } from "../ui";

type AdvertisePopupProps = {
    onClose: () => void;
    banner: BannerResponse;
};

const AdvertisePopup: React.FC<AdvertisePopupProps> = ({ onClose, banner }) => {
    const handleShopNowPress = () => {
        if (banner.banner?.url) {
            Linking.openURL(banner.banner.url);
        }
        onClose();
    };

    return (
        <Box className="bg-white rounded-[27px] items-center max-w-[258px]">
            <Box className="w-[258px] h-[301px] rounded-[27px] overflow-visible">
                <Box className="p-2 rounded-2xl overflow-visible">
                    <Image
                        source={{ uri: banner.banner?.image_url }}
                        className="w-full h-full rounded-[27px]"
                        alt="Recycle Now"
                        resizeMode="stretch"
                    />
                </Box>
                {banner.banner?.url && (
                    <Box className="absolute bottom-[-30px] left-2 right-2 p-4 shadow-lg flex-row items-center justify-center">
                        <Box className="w-1/2">
                            <MyButton
                                text="Recycle Now"
                                onPress={handleShopNowPress}
                                variant="outline"
                                titleSize={15}
                                height={44}
                            />
                        </Box>
                    </Box>
                )}
            </Box>
            <Box className="absolute bottom-0 right-[-10px] top-[-10px]">
                <Box className="bg-buttonSecondaryColor rounded-full p-2">
                    <IconComponent
                        name="close"
                        font="ant-design"
                        size={24}
                        color={getColor("blue")}
                        onPress={onClose}
                    />
                </Box>
            </Box>
        </Box>
    );
};

export default AdvertisePopup;
