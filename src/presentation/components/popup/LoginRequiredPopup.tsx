import { useNavigation } from "@react-navigation/native";
import React from "react";

import { getColor } from "@/presentation/hooks";

import { MyButton } from "../myButton";
import { MyTouchable } from "../touchable";
import { Box, HStack, IconComponent, Text, VStack } from "../ui";

import { ShowBottomSheetObs } from "@/data/services/observable";
import { RouteName, TypeBottomSheet } from "@/shared/constants";

type LoginRequiredPopupProps = {
    onClose: () => void;
};

const LoginRequiredPopup: React.FC<LoginRequiredPopupProps> = ({ onClose }) => {
    const navigation = useNavigation();

    const handleGetStarted = React.useCallback(() => {
        onClose();
        ShowBottomSheetObs.action({
            type: TypeBottomSheet.REGISTER
        });
    }, [onClose]);

    const handleLogin = React.useCallback(() => {
        onClose();
        navigation.navigate(RouteName.Auth, { screen: RouteName.Login });
    }, [navigation, onClose]);

    return (
        <Box className="bg-white rounded-3xl mx-4 shadow-lg pt-10">
            <VStack className="items-center px-8 py-10">
                <Text className="text-darkBlue text-2xl font-bold text-center mb-3">Login Required</Text>

                <Text className="text-gray-600 text-base text-center leading-6 mb-8 px-2">
                    Please create an account or login to continue with this action.
                </Text>

                <MyButton text="Login" variant="outline" onPress={handleLogin} height={54} />
                <HStack className="justify-center pt-5">
                    <Text className="text-gray-600 text-base text-center leading-6 mb-8 px-2">
                        Don&apos;t have an account?
                    </Text>
                    <MyTouchable onPress={handleGetStarted}>
                        <Text className="text-blue text-[15px] font-bold">Sign Up</Text>
                    </MyTouchable>
                </HStack>
            </VStack>
            <Box className="absolute bottom-0 right-5 top-5">
                <IconComponent name="close" font="ant-design" size={24} color={getColor("blue")} onPress={onClose} />
            </Box>
        </Box>
    );
};

export default LoginRequiredPopup;
