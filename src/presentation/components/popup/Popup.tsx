import React from "react";
import { Pressable } from "react-native";

import AdvertisePopup from "./AdvertisePopup";
import LoginRequiredPopup from "./LoginRequiredPopup";
import { usePayload } from "./Popup.Hook";

export enum TypePopup {
    ADVERTISE_POPUP = "advertise-popup"
}

const Popup = () => {
    const { payload, isOpen, handleClose } = usePayload();

    const renderContent = React.useMemo(() => {
        if (!payload) return;
        switch (payload?.type) {
            case TypePopup.ADVERTISE_POPUP:
                return <AdvertisePopup onClose={handleClose} banner={payload.banner!} />;
            default:
                return <LoginRequiredPopup onClose={handleClose} />;
        }
    }, [payload, handleClose]);

    if (!isOpen || !payload) return;

    return (
        <Pressable
            className="absolute inset-0 bg-black/50 justify-center items-center"
            style={{ zIndex: 1000 }}
            onPress={handleClose}>
            <Pressable onPress={(e) => e.stopPropagation()}>{renderContent}</Pressable>
        </Pressable>
    );
};

export default Popup;
