import React from "react";
import { ImageSourcePropType } from "react-native";

import BottomSheetCustom, { BottomSheetCustomRef } from "@/presentation/components/bottomSheet/BottomSheetCustom";
import { CheckBox } from "@/presentation/components/checkBox";
import { AcceptedItemsGrid } from "@/presentation/components/item";
import { MyButton } from "@/presentation/components/myButton";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Image, ScrollView, Text } from "@/presentation/components/ui";
import { BinInfoType } from "@/presentation/hooks/scan/useQRScan";
import { BinTypeKey } from "@/shared/constants/BinTypes";

type ResultScanProps = {
    binInfo?: BinInfoType;
    selectedBinType?: BinTypeKey;
    binImage?: string | ImageSourcePropType | undefined;
    isAcknowledged: boolean;
    handleCloseBottomSheet: () => void;
    handleSelect: () => void;
    onCheckChange: (value: boolean) => void;
    binData?: BinDetailResponse;
};

const ResultScan = React.forwardRef<BottomSheetCustomRef, ResultScanProps>(
    (
        {
            binInfo,
            selectedBinType,
            binImage,
            isAcknowledged,
            handleCloseBottomSheet,
            handleSelect,
            onCheckChange,
            binData
        },
        ref
    ) => {
        const binName = binData?.type?.name || (selectedBinType && binInfo?.[selectedBinType]?.name) || "Bin";
        const imageSource = binData?.type?.image_url ? { uri: binData.type.image_url } : binImage;
        const wasteTypes = binData?.type?.waste_types;

        return (
            <BottomSheetCustom ref={ref} height={700} onClose={handleCloseBottomSheet}>
                <ScrollView>
                    <Box className="p-4">
                        <Text className="text-gray-500 text-base mb-2">You scanned the</Text>
                        <Text className="text-xl font-bold mb-4">{binName}</Text>

                        <Box className="items-center justify-center">
                            <Box className="w-full h-[300px] overflow-hidden rounded-lg">
                                <Image
                                    source={imageSource}
                                    style={{
                                        width: "100%",
                                        height: "100%"
                                    }}
                                    resizeMode="contain"
                                    alt="bin"
                                />
                            </Box>
                        </Box>

                        {wasteTypes ? (
                            <AcceptedItemsGrid wasteTypes={wasteTypes} />
                        ) : (
                            selectedBinType &&
                            binInfo && <AcceptedItemsGrid items={binInfo[selectedBinType].acceptedItems} />
                        )}
                    </Box>
                </ScrollView>

                <Box className="px-4">
                    <Box className="mt-5">
                        <MyTouchable onPress={() => onCheckChange(!isAcknowledged)}>
                            <CheckBox
                                onChange={onCheckChange}
                                value={isAcknowledged}
                                title="I acknowledge that the Recycled Items are correct and final"
                            />
                        </MyTouchable>
                    </Box>
                    <MyButton text="Select" className="mt-3 mb-3" disabled={!isAcknowledged} onPress={handleSelect} />
                </Box>
            </BottomSheetCustom>
        );
    }
);

export default ResultScan;
