import React from "react";

import ResultScan from "./ResultScan";

import { CameraEmptyPermission } from "@/presentation/components/emptyState";
import { QRFrameOverlay } from "@/presentation/components/frame";
import { Header } from "@/presentation/components/header";
import { QRCamera } from "@/presentation/components/qrCamera";
import { Box, Container, IconComponent } from "@/presentation/components/ui";
import QRFrameWalkthrough from "@/presentation/components/walkthrough/QRFrameWalkthrough";
import { useQRScan } from "@/presentation/hooks/scan";
import { ImageAssets } from "@/shared/constants";

const ScanScreen = () => {
    const {
        isLoading,
        isActive,
        isTorchOn,
        isWalkthroughVisible,
        device,
        cameraRef,
        hasPermission,
        requestPermission,
        onError,
        handleCodeScanned,
        handleCloseWalkthrough,
        handleCloseBottomSheet,
        handleTurnOnOffLight,
        handleSelect,
        nextScreen,
        bottomSheetRef,
        binInfo,
        selectedBinType,
        binImage,
        isAcknowledged,
        setIsAcknowledged,
        binData
    } = useQRScan();

    if (!device || !hasPermission) {
        return <CameraEmptyPermission device={device} requestPermission={requestPermission} />;
    }

    return (
        <>
            <Container isLoading={isLoading}>
                <Box className="flex-1">
                    <QRCamera
                        ref={cameraRef}
                        isActive={isActive}
                        isTorchOn={isTorchOn}
                        onError={onError}
                        onCodeScanned={handleCodeScanned}
                    />

                    <Box className="absolute top-5 left-0 right-0 z-10">
                        <Header
                            title="Scan QR Code"
                            icBack={ImageAssets.icClose}
                            titleColor="white"
                            rightComponent={ImageAssets.icLight}
                            onPress={handleTurnOnOffLight}
                        />
                    </Box>

                    <QRFrameWalkthrough
                        isVisible={isWalkthroughVisible}
                        onClose={handleCloseWalkthrough}
                        currentStep={2}
                        totalSteps={4}>
                        <QRFrameOverlay />
                    </QRFrameWalkthrough>
                </Box>

                {/* TODO: for testing */}
                {__DEV__ && (
                    <Box className="absolute bottom-10 left-0 right-0 items-center justify-center z-10">
                        <IconComponent name="arrow-right" size={24} color="white" onPress={nextScreen} />
                    </Box>
                )}
            </Container>

            <ResultScan
                ref={bottomSheetRef}
                binInfo={binInfo}
                selectedBinType={selectedBinType}
                binImage={binImage}
                isAcknowledged={isAcknowledged}
                handleCloseBottomSheet={handleCloseBottomSheet}
                handleSelect={handleSelect}
                onCheckChange={setIsAcknowledged}
                binData={binData || undefined}
            />
        </>
    );
};

export default ScanScreen;
