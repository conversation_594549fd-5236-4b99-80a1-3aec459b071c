import { useNavigation } from "@react-navigation/native";
import React from "react";
import { Keyboard } from "react-native";
import { object, string } from "yup";

import { useUserStore } from "@/app/store";

import { useUpdateUserQueries } from "@/data/queries";

import { useAddress, useForm } from "@/presentation/hooks";

import { ShowBottomSheetObs } from "@/data/services/observable";
import { Header } from "@/presentation/components/header";
import { Input } from "@/presentation/components/input";
import { MyButton } from "@/presentation/components/myButton";
import { Box, Container, ScrollView, Text } from "@/presentation/components/ui";
import { usePoint } from "@/presentation/hooks/point";
import RouteName from "@/shared/constants/RouteName";
import { compareValue, formatDate } from "@/shared/helper";
import {
    addressSchema,
    dobSchema,
    firstNameSchema,
    lastNameSchema,
    nicknameSchema,
    postalCodeSchema
} from "@/shared/validations";

// Constants
const FIELD_CONFIG = {
    firstName: { title: "First Name", placeholder: "First Name", required: true },
    lastName: { title: "Last Name", placeholder: "Last Name", required: true },
    nickname: { title: "Nickname", placeholder: "Nickname", required: false },
    email: { title: "Registered Email Address", placeholder: "Registered Email Address", required: false },
    address: { title: "Address", placeholder: "Address", required: false },
    dateOfBirth: { title: "Date of Birth", placeholder: "Date of Birth", required: false },
    postalCode: { title: "Postal Code", placeholder: "Postal Code", required: false }
} as const;

const DISABLED_FIELDS = ["email", "address"] as const;

const EditAccountScreen: React.FC = () => {
    const { user } = useUserStore();
    const { point } = usePoint();
    const { updateUser, isLoading: isLoadingUpdateUser } = useUpdateUserQueries();
    const { getAddressByPostalCode, isLoading: isLoadingAddress } = useAddress();

    const navigation = useNavigation();

    const initialValues = React.useMemo(
        () => ({
            firstName: user?.first_name || "",
            lastName: user?.last_name || "",
            nickname: user?.display_name || "",
            email: user?.email || "",
            address: user?.address || "",
            dateOfBirth: user?.dob ? formatDate(user?.dob, "yyyy-MM-dd") : "",
            postalCode: user?.postal_code || ""
        }),
        [user]
    );

    const {
        getInputProps,
        handleSubmit,
        formik,
        values: valuesForm,
        errors
    } = useForm({
        enableReinitialize: true,
        initialValues,
        validationSchema: object().shape({
            firstName: firstNameSchema,
            lastName: lastNameSchema,
            nickname: nicknameSchema,
            email: string().optional(),
            address: addressSchema,
            dateOfBirth: dobSchema,
            postalCode: postalCodeSchema
        }),
        onSubmit: async (values) => {
            await updateUser({
                first_name: values.firstName,
                last_name: values.lastName,
                display_name: values.nickname,
                address: values.address,
                dob: values.dateOfBirth,
                postal_code: values.postalCode
            });
            Keyboard.dismiss();
        }
    });

    const handleConfirmDeleteAccount = React.useCallback(() => {
        navigation.navigate(RouteName.Auth, { screen: RouteName.Verification });
    }, [navigation]);

    const handleDeleteAccount = React.useCallback(() => {
        ShowBottomSheetObs.action({
            title: "Are you sure you want to delete the account?",
            message: `All of the ${point} CO₂ Points will be gone`,
            titleButtonConfirm: "Cancel",
            titleButtonCancel: "Delete Account",
            onConfirm: handleConfirmDeleteAccount,
            onCancel: () => {}
        });
    }, [handleConfirmDeleteAccount, point]);

    const handleChangeValue = React.useCallback(
        (field: string, value: string) => {
            if (field === "dateOfBirth") {
                return formik.setFieldValue(field, formatDate(value, "yyyy-MM-dd"));
            }
            formik.setFieldValue(field, value);
        },
        [formik]
    );

    const getFieldProps = React.useCallback(
        (fieldKey: keyof typeof initialValues) => {
            const isDisabled = DISABLED_FIELDS.includes(fieldKey as "email" | "address");

            if (isDisabled) {
                if (fieldKey === "email") {
                    return { value: user?.email || "", enable: false };
                }
                if (fieldKey === "address") {
                    const props = getInputProps("address");
                    return { ...props, enable: false };
                }
            }

            if (fieldKey === "email") {
                return { value: user?.email || "", enable: false };
            }

            return getInputProps(
                fieldKey as "firstName" | "lastName" | "nickname" | "address" | "dateOfBirth" | "postalCode"
            );
        },
        [getInputProps, user?.email]
    );

    const isChanged = React.useMemo(() => compareValue(initialValues, valuesForm), [initialValues, valuesForm]);

    React.useEffect(() => {
        const fetchAddress = async () => {
            if (isChanged) return;

            if (
                valuesForm?.postalCode?.length === 6 &&
                valuesForm?.postalCode !== initialValues?.postalCode &&
                !errors.postalCode
            ) {
                const addressResponse = await getAddressByPostalCode(valuesForm.postalCode);
                formik.setFieldValue("address", addressResponse?.location?.ADDRESS || "");
            } else if (valuesForm?.postalCode?.length === 0 && valuesForm?.postalCode !== initialValues?.postalCode) {
                formik.setFieldValue("address", "");
            }
        };

        fetchAddress();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [valuesForm.postalCode, isChanged, initialValues?.postalCode, errors?.postalCode]);

    const renderFormField = React.useCallback(
        (fieldKey: keyof typeof initialValues) => {
            const config = FIELD_CONFIG[fieldKey];
            const isDisabled = DISABLED_FIELDS.includes(fieldKey as "email" | "address");

            return (
                <Box className="mb-5">
                    <Input
                        title={config.title}
                        required={config.required}
                        placeholder={config.placeholder}
                        {...getFieldProps(fieldKey)}
                        type={fieldKey === "dateOfBirth" ? "date" : undefined}
                        onChangeValue={isDisabled ? undefined : handleChangeValue}
                        keyboardType={
                            fieldKey === "postalCode" ? "numeric" : fieldKey === "email" ? "email-address" : undefined
                        }
                        isLoading={fieldKey === "postalCode" && isLoadingAddress}
                        maxLength={fieldKey === "postalCode" ? 6 : undefined}
                    />
                </Box>
            );
        },
        [getFieldProps, handleChangeValue, isLoadingAddress]
    );

    return (
        <Container isLoading={isLoadingUpdateUser}>
            <Header
                title="Edit Account"
                rightComponent={
                    <Text className="text-darkBlue font-medium" onPress={handleDeleteAccount}>
                        Delete Account
                    </Text>
                }
            />
            <ScrollView className="flex-1 px-5 pt-4">
                {renderFormField("firstName")}
                {renderFormField("lastName")}
                {renderFormField("email")}
                {renderFormField("address")}
                {renderFormField("dateOfBirth")}
                {renderFormField("postalCode")}
                {renderFormField("nickname")}
            </ScrollView>
            <Box className="p-4">
                <MyButton text="Save Changes" onPress={handleSubmit} disabled={isChanged} />
            </Box>
        </Container>
    );
};

export default EditAccountScreen;
