import { useNavigation } from "@react-navigation/native";
import { ListRenderItemInfo } from "@shopify/flash-list";
import React from "react";

import RewardCard from "../../components/card/RewardCard";
import { Box, Container, Text } from "../../components/ui";

import { Banner } from "@/presentation/components/advertisement";
import { Header } from "@/presentation/components/header";
import { ListView } from "@/presentation/components/listView";
import { RequireAuth } from "@/presentation/components/requireAuth";
import { VoucherCardSkeleton } from "@/presentation/components/skeleton";
import { VoucherCard } from "@/presentation/components/voucherCard";
import { useBanner } from "@/presentation/hooks/banner";
import { useRewards, useRewardsDetail } from "@/presentation/hooks/rewards";
import { useGuest } from "@/presentation/hooks/user";
import { ImageAssets, RouteName } from "@/shared/constants";

const RewardsScreen = () => {
    const navigation = useNavigation();
    const { rewards, isLoading, isLoadingMore, fetchNextPage, refetch } = useRewards();
    const { handleFetchRewardDetail, isLoading: isLoadingRewardDetail } = useRewardsDetail();
    const { isGuestMode } = useGuest();
    const { bannerReward, getBannerReward } = useBanner();

    React.useEffect(() => {
        getBannerReward();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const onPressVoucher = React.useCallback(
        (id: number) => {
            handleFetchRewardDetail(id);
        },
        [handleFetchRewardDetail]
    );

    const renderVoucherItem = React.useCallback(
        ({ item }: ListRenderItemInfo<RewardResponse>) => (
            <VoucherCard item={item} onPress={onPressVoucher} enableRedeem={item.remaining_vouchers > 0} />
        ),
        [onPressVoucher]
    );

    const onHistoryPress = React.useCallback(() => {
        navigation.navigate(RouteName.RewardsHistory);
    }, [navigation]);

    const renderContent = React.useMemo(() => {
        if (isGuestMode()) {
            return <RequireAuth />;
        }
        return (
            <Box className="flex-1">
                <RewardCard />

                <Box className="px-3 flex-1">
                    <ListView
                        keyList="id"
                        listHeaderComponent={
                            <Box className="px-4">
                                <Text className="mb-4 text-[16px]">
                                    Redeem your points for vouchers and enjoy great savings on your next purchase!
                                </Text>
                                {bannerReward?.banner && bannerReward.banner?.status && (
                                    <Banner banner={bannerReward} />
                                )}
                            </Box>
                        }
                        data={rewards}
                        renderItem={renderVoucherItem}
                        numColumns={2}
                        onPressLoadMore={fetchNextPage}
                        isLoadingMore={isLoadingMore}
                        isLoading={isLoading}
                        skeletonComponent={VoucherCardSkeleton}
                        onPullToRefresh={refetch}
                    />
                </Box>
            </Box>
        );
    }, [bannerReward, fetchNextPage, isGuestMode, isLoading, isLoadingMore, renderVoucherItem, rewards, refetch]);

    return (
        <Container isLoading={isLoadingRewardDetail}>
            <Header
                title="Rewards"
                isShowBack={false}
                rightComponent={ImageAssets.activityOff}
                tintColor="black"
                onPress={onHistoryPress}
            />

            {renderContent}
        </Container>
    );
};

export default RewardsScreen;
