import { useNavigation } from "@react-navigation/native";
import { ListRenderItemInfo } from "@shopify/flash-list";
import React from "react";

import { EmptyState } from "@/presentation/components/emptyState";
import { Header } from "@/presentation/components/header";
import { RewardHistoryItem } from "@/presentation/components/item";
import { ListView } from "@/presentation/components/listView";
import { RequireAuth } from "@/presentation/components/requireAuth";
import { Box, Container } from "@/presentation/components/ui";
import { usePointHistory } from "@/presentation/hooks/point";
import { useGuest } from "@/presentation/hooks/user";
import { RouteName } from "@/shared/constants";

const RewardsHistoryScreen = () => {
    const { pointHistory, isLoading } = usePointHistory();
    const { isGuestMode } = useGuest();
    const navigation = useNavigation();

    const onStartRecycling = React.useCallback(() => {
        navigation.navigate(RouteName.ScanStack, {
            screen: RouteName.Scan
        });
    }, [navigation]);

    const renderEmptyState = React.useMemo(() => {
        return (
            <EmptyState
                title="You haven't earned CO₂ Points yet"
                description="Let's earn it by start recycling your goods"
                buttonText="Start recycling"
                onButtonPress={onStartRecycling}
            />
        );
    }, [onStartRecycling]);

    const renderItem = React.useCallback(({ item }: ListRenderItemInfo<PointHistoryResponse>) => {
        return <RewardHistoryItem item={item} />;
    }, []);

    const renderContent = React.useMemo(() => {
        if (isGuestMode()) {
            return <RequireAuth />;
        }
        return (
            <Box className="flex-1 px-4">
                <ListView
                    data={pointHistory}
                    renderItem={renderItem}
                    keyList="month_year"
                    isLoading={isLoading}
                    emptyComponent={renderEmptyState}
                />
            </Box>
        );
    }, [isGuestMode, isLoading, pointHistory, renderEmptyState, renderItem]);

    return (
        <Container isLoading={isLoading}>
            <Header title="Rewards History" />

            {renderContent}
        </Container>
    );
};

export default RewardsHistoryScreen;
