import { useNavigation } from "@react-navigation/native";
import React from "react";
import Carousel from "react-native-reanimated-carousel";

import { ButtonGetStarted } from "@/presentation/components/myButton";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, HStack, Image, ScrollView, Text, VStack } from "@/presentation/components/ui";
import { useGuest } from "@/presentation/hooks/user";
import { ImageAssets, RouteName } from "@/shared/constants";
import { fullWidth } from "@/shared/helper";

const bannerData = [
    {
        source: ImageAssets.bannerOne,
        alt: "banner one",
        title: "Hey there",
        description: "Welcome to Step Up — your fun way to find ALBA bins and start recycling!"
    },
    {
        source: ImageAssets.bannerTwo,
        alt: "banner two",
        title: "Recycle Right",
        description: "Find nearby ALBA bins for batteries, electronics, plastics, and more — all in one app."
    },
    {
        source: ImageAssets.bannerThree,
        alt: "banner three",
        title: "<PERSON>arn <PERSON>, Redeem Rewards",
        description: "Recycle, earn CO₂ points, and unlock rewards with our partners."
    }
];

const WelcomeScreen = () => {
    const navigation = useNavigation();
    const { setGuestMode } = useGuest();

    const [currentIndex, setCurrentIndex] = React.useState(0);

    const renderBannerItem = React.useCallback(({ item }: { item: (typeof bannerData)[number] }) => {
        return (
            <VStack className="gap-5">
                <Image source={item.source} className="w-full h-[400px]" alt={item.alt} />
                <Box className="px-5 gap-3">
                    <Text className="text-[29px] font-bold text-darkBlue">{item.title}</Text>
                    <Text className="text-lg text-darkBlue">{item.description}</Text>
                </Box>
            </VStack>
        );
    }, []);

    const handleCurrentIndexChange = React.useCallback((index: number) => {
        setCurrentIndex(index % bannerData.length);
    }, []);

    const onLogin = React.useCallback(() => {
        navigation.navigate(RouteName.Auth, { screen: RouteName.Login });
    }, [navigation]);

    const onGuest = React.useCallback(() => {
        setGuestMode(true);
        navigation.replace(RouteName.Bottom);
    }, [navigation, setGuestMode]);

    return (
        <Container>
            <ScrollView>
                <Carousel
                    loop={false}
                    width={fullWidth}
                    height={550}
                    autoPlay={false}
                    data={bannerData}
                    scrollAnimationDuration={1000}
                    renderItem={renderBannerItem}
                    onScrollEnd={handleCurrentIndexChange}
                    modeConfig={{
                        parallaxScrollingScale: 0.9,
                        parallaxScrollingOffset: 50
                    }}
                    defaultIndex={currentIndex}
                />
                <HStack className="justify-center gap-2 mt-5">
                    {bannerData.map((_, index) => (
                        <Box
                            key={index}
                            className={`w-[8px] h-[8px] rounded-full ${currentIndex === index ? "bg-darkBlue" : "bg-darkBlue opacity-50"}`}
                        />
                    ))}
                </HStack>
                <Box className="px-5 mt-10 items-center gap-5">
                    <ButtonGetStarted />
                    <MyTouchable onPress={onLogin}>
                        <Text className="text-blue text-[15px] font-bold">I already have an account</Text>
                    </MyTouchable>
                    <MyTouchable onPress={onGuest}>
                        <Text className="text-blue text-[15px] font-bold">Continue as guest</Text>
                    </MyTouchable>
                </Box>
            </ScrollView>
        </Container>
    );
};

export default WelcomeScreen;
