import React from "react";
import { <PERSON><PERSON>View } from "react-native";

import { Head<PERSON> } from "@/presentation/components/header";
import { MyButton } from "@/presentation/components/myButton";
import { Box, Container, HStack, Image, Text, VStack } from "@/presentation/components/ui";
import { ImageAssets } from "@/shared/constants";

interface Contributor {
    id: string;
    rank: number;
    name: string;
    isUser: boolean;
    co2Saved: number;
}

const JoinEventDetailScreen = () => {
    // Mock data for the event details
    const eventData = {
        totalCO2Saved: "871.37",
        eventName: "Trash to Treasure 2025",
        endDate: "18 July 2025 • 24:00:00",
        location: "302 Tiong Bahru Road, Tiong Bahru Plaza, #03-19",
        about: "Let's know the details of it here",
        userImpact: "0.2",
        userRank: "#128973",
        totalContributors: "223565"
    };

    // Mock data for top contributors
    const topContributors: Contributor[] = [
        { id: "1", rank: 1, name: "Anonymous", isUser: false, co2Saved: 1050 },
        { id: "2", rank: 2, name: "Anonymous", isUser: false, co2Saved: 780 },
        { id: "3", rank: 3, name: "Anonymous", isUser: false, co2Saved: 679 },
        { id: "4", rank: 4, name: "37856x", isUser: true, co2Saved: 0.2 }
    ];

    const renderContributor = (contributor: Contributor) => {
        return (
            <HStack
                key={contributor.id}
                className={`py-3 px-4 rounded-lg items-center justify-between ${contributor.isUser ? "bg-green-50" : ""}`}>
                <HStack className="items-center gap-3">
                    <Box
                        className={`w-12 h-12 rounded-full items-center justify-center ${contributor.isUser ? "bg-green-500" : "bg-gray-200"}`}>
                        <Text className="text-white font-bold">AN</Text>
                        {contributor.rank <= 3 && (
                            <Box className="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-amber-400 items-center justify-center">
                                <Text className="text-white font-bold text-xs">{contributor.rank}</Text>
                            </Box>
                        )}
                    </Box>
                    <Text className="text-darkGray text-lg">
                        {contributor.name} {contributor.isUser && "(You)"}
                    </Text>
                </HStack>
                <Text className="text-darkGray font-bold">
                    {contributor.co2Saved} kg CO<Text className="text-xs">2</Text>
                </Text>
            </HStack>
        );
    };

    return (
        <Container>
            <Header title="Your Private Event" />

            <ScrollView showsVerticalScrollIndicator={false}>
                <Box className="bg-darkBlue pt-4 pb-6 items-center">
                    <Box className="w-24 h-24 bg-gray-200 rounded-full mb-4" />
                    <Text className="text-white text-5xl font-bold">
                        {eventData.totalCO2Saved}
                        <Text className="text-xl font-normal">kg</Text>
                    </Text>
                    <Text className="text-blue-200 text-base">
                        Total CO<Text className="text-xs">2</Text> emission saved on this event
                    </Text>
                    <Text className="text-white text-2xl font-bold mt-4">{eventData.eventName}</Text>
                </Box>

                <Box className="bg-darkBlue px-5 pb-5">
                    <Box className="bg-blue-800 rounded-lg p-4">
                        <VStack className="gap-6">
                            <VStack>
                                <Text className="text-blue-200 text-sm">ENDED IN</Text>
                                <HStack className="items-center gap-2">
                                    <Image
                                        source={ImageAssets.icCalendar}
                                        className="w-5 h-5 tint-white"
                                        alt="calendar"
                                    />
                                    <Text className="text-white">{eventData.endDate}</Text>
                                </HStack>
                            </VStack>

                            <VStack>
                                <Text className="text-blue-200 text-sm">LOCATION</Text>
                                <HStack className="items-center gap-2">
                                    <Image
                                        source={ImageAssets.icMapHouseElectron}
                                        className="w-5 h-5 tint-white"
                                        alt="location"
                                    />
                                    <Text className="text-white">{eventData.location}</Text>
                                </HStack>
                            </VStack>

                            <VStack>
                                <Text className="text-blue-200 text-sm">ABOUT</Text>
                                <HStack className="items-center justify-between">
                                    <HStack className="items-center gap-2">
                                        <Image source={ImageAssets.icInfo} className="w-5 h-5 tint-white" alt="info" />
                                        <Text className="text-white">{eventData.about}</Text>
                                    </HStack>
                                    <Text className="text-white text-xl">›</Text>
                                </HStack>
                            </VStack>
                        </VStack>
                    </Box>
                </Box>

                <Box className="p-5">
                    <Text className="text-darkGray text-lg mb-4">Your impact so far</Text>
                    <Box className="bg-green-50 p-4 rounded-lg">
                        <HStack className="items-center">
                            <Image source={ImageAssets.trophy} className="w-12 h-12 mr-4" alt="trophy" />
                            <VStack>
                                <Text className="text-darkBlue text-3xl font-bold">{eventData.userImpact} kg</Text>
                                <Text className="text-darkGray">
                                    Your total CO<Text className="text-xs">2</Text> emission saved
                                </Text>
                            </VStack>
                        </HStack>
                    </Box>
                </Box>

                <Box className="px-5 pb-24">
                    <Text className="text-darkGray text-lg mb-2">Let&apos;s climb the leaderboard!</Text>
                    <HStack className="items-center mb-6">
                        <Image source={ImageAssets.crown} className="w-6 h-6 mr-2" alt="crown" />
                        <Text className="text-darkGray font-bold">{eventData.userRank}</Text>
                        <Text className="text-darkGray ml-2">of {eventData.totalContributors} contributors</Text>
                    </HStack>

                    <Text className="text-darkGray text-lg font-bold mb-4">Top Contributors</Text>
                    <VStack className="gap-2">{topContributors.map(renderContributor)}</VStack>
                </Box>
            </ScrollView>

            <Box className="absolute bottom-0 left-0 right-0 p-5 bg-white">
                <MyButton text="Start Recycling" onPress={() => {}} variant="secondary" />
            </Box>
        </Container>
    );
};

export default JoinEventDetailScreen;
