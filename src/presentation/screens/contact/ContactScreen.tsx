import { ImagePickerAsset } from "expo-image-picker";
import React from "react";
import { ActivityIndicator, Alert } from "react-native";
import { object, string } from "yup";

import { useAddress, useForm, usePickerImage } from "@/presentation/hooks";

import { DropdownRef } from "@/presentation/components/dropdown";
import { Header } from "@/presentation/components/header";
import { Input } from "@/presentation/components/input";
import { MyButton } from "@/presentation/components/myButton";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, Image, ScrollView, Text } from "@/presentation/components/ui";
import IconComponent from "@/presentation/components/ui/icon";
import { useContact } from "@/presentation/hooks/contact";
import {
    emailSchema,
    messageSchema,
    nameSchema,
    natureOfQuerySchema,
    phoneNumberSchema,
    postalCodeSchema
} from "@/shared/validations";

const natureOfQueryData = [
    {
        name: "Ewaste collection (Only for NUS)",
        value: "Ewaste collection (Only for NUS)"
    },
    {
        name: "Confidential Paper Collection (Only for NUS)",
        value: "Confidential Paper Collection (Only for NUS)"
    }
];

const ContactScreen = () => {
    const [images, setImages] = React.useState<ImagePickerAsset[]>([]);
    const natureOfQueryRef = React.useRef<DropdownRef>(null);

    const { getAddressByPostalCode, isLoading: isLoadingAddress } = useAddress();
    const { pickerImageWithValidation, isLoadingPicker, error } = usePickerImage(true);
    const { sendContact, isLoading } = useContact();

    const handleAddImage = async () => {
        try {
            const result = await pickerImageWithValidation("photo", {
                maxImages: 5 - (images?.length || 0),
                allowedTypes: ["jpg", "jpeg", "png", "heic"],
                maxWidth: 2000,
                maxHeight: 2000,
                quality: 0.8,
                maxFileSize: 10 * 1024 * 1024
            });

            if (result && result.length > 0) {
                const newImageUris = result.map((asset) => asset).filter((uri) => uri !== undefined);
                setImages([...(images || []), ...newImageUris]);
            }
        } catch (err) {
            /* empty */
        }
    };

    const handleRemoveImage = (index: number) => {
        const newImages = [...(images || [])];
        newImages.splice(index, 1);
        setImages(newImages);
    };

    const { getInputProps, handleSubmit, values, errors, touched, setFieldValue, resetForm } = useForm({
        initialValues: {
            natureOfQuery: "",
            name: "",
            email: "",
            phone: "",
            address: "",
            postalCode: "",
            message: ""
        },
        validationSchema: object().shape({
            natureOfQuery: natureOfQuerySchema,
            name: nameSchema,
            email: emailSchema,
            phone: phoneNumberSchema,
            address: string().optional(),
            postalCode: postalCodeSchema,
            message: messageSchema
        }),
        onSubmit: async (_values) => {
            const result = await sendContact({
                nature_of_query: values.natureOfQuery,
                name: values.name,
                email: values.email,
                phone: values.phone,
                address: values.address,
                postal_code: values.postalCode,
                message: values.message,
                attachments: images || []
            });

            if (result?.ok) {
                Alert.alert(
                    "Success",
                    "Your message has been sent successfully",
                    [
                        {
                            text: "OK",
                            onPress: () => {
                                resetForm();
                                natureOfQueryRef.current?.reset();
                                setImages([]);
                            }
                        }
                    ],
                    { cancelable: false }
                );
            }
        }
    });

    React.useEffect(() => {
        const fetchAddress = async () => {
            if (values?.postalCode?.length === 6 && !errors.postalCode) {
                const addressResponse = await getAddressByPostalCode(values.postalCode);
                setFieldValue("address", addressResponse?.location?.ADDRESS || "");
            } else {
                setFieldValue("address", "");
            }
        };

        fetchAddress();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [values?.postalCode, errors?.postalCode, touched?.postalCode]);

    return (
        <Container isLoading={isLoading}>
            <Header title="Contact Us" />
            <ScrollView className="px-4 pt-5">
                <Text>Have questions? We&apos;re just a message away</Text>

                {error && (
                    <Box className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        <Text className="text-red-700">{error}</Text>
                    </Box>
                )}

                <Box className="gap-4 py-4">
                    <Input
                        title="Nature of query"
                        placeholder="Select nature of query"
                        type="dropdown"
                        required
                        dataDropdown={natureOfQueryData}
                        ref={natureOfQueryRef}
                        {...getInputProps("natureOfQuery")}
                    />
                    <Input title="Name" placeholder="Name" required {...getInputProps("name")} />
                    <Input title="Email" placeholder="Email" required type="email" {...getInputProps("email")} />
                    <Input
                        title="Phone number"
                        placeholder="Phone number"
                        required
                        type="phone"
                        {...getInputProps("phone")}
                    />
                    <Input
                        title="Postal Code"
                        placeholder="Postal Code"
                        required
                        {...getInputProps("postalCode")}
                        keyboardType="numeric"
                        isLoading={isLoadingAddress}
                        maxLength={6}
                    />
                    {values?.address && !isLoadingAddress && (
                        <Input
                            title="Address"
                            placeholder="Address"
                            {...getInputProps("address")}
                            editable={false}
                            isLoading={isLoadingAddress}
                        />
                    )}
                    <Input
                        title="Message"
                        placeholder="Message"
                        required
                        multiline
                        height={100}
                        paddingTop={10}
                        {...getInputProps("message")}
                    />
                    <Box>
                        <Text className="mb-2">Attachments (Max. 5)</Text>

                        <Box className="flex-row py-2 w-full gap-3 flex-wrap">
                            {images?.map((imageUri, index) => (
                                <Box
                                    key={`image-${index}`}
                                    className="h-[56px] w-[56px] overflow-visible items-center justify-center relative">
                                    <Image
                                        className="w-full h-full rounded-lg "
                                        source={{ uri: imageUri.uri }}
                                        alt={`Selected image ${index + 1}`}
                                        resizeMode="cover"
                                    />
                                    <MyTouchable
                                        onPress={() => handleRemoveImage(index)}
                                        className="absolute top-[-8px] right-[-8px] bg-red rounded-full w-5 h-5 items-center justify-center">
                                        <IconComponent name="cross" size={12} color="white" />
                                    </MyTouchable>
                                </Box>
                            ))}
                            {images?.length < 5 && (
                                <MyTouchable
                                    onPress={handleAddImage}
                                    disabled={isLoadingPicker}
                                    className="bg-lightBlue rounded-[5px]">
                                    <Box className="h-[56px] w-[56px] items-center justify-center bg-gray-50">
                                        {isLoadingPicker ? (
                                            <ActivityIndicator size="small" color="#888888" />
                                        ) : (
                                            <IconComponent name="plus" size={24} color="#888888" />
                                        )}
                                    </Box>
                                </MyTouchable>
                            )}
                        </Box>
                    </Box>
                </Box>
            </ScrollView>
            <Box className="p-4">
                <MyButton text="Submit" onPress={handleSubmit} />
            </Box>
        </Container>
    );
};

export default ContactScreen;
