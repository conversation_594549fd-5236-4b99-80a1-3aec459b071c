import { ListRenderItemInfo } from "@shopify/flash-list";
import React from "react";

import { EmptyState } from "@/presentation/components/emptyState";
import { Header } from "@/presentation/components/header";
import { ListView } from "@/presentation/components/listView";
import { VoucherCardSkeleton } from "@/presentation/components/skeleton";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, HStack, Text } from "@/presentation/components/ui";
import { VoucherCard } from "@/presentation/components/voucherCard";
import { useMyReward, useMyRewardDetail } from "@/presentation/hooks/user";
import { RewardStatusType } from "@/shared/constants";

const MyVoucherScreen = () => {
    const { myReward, handleSetStatus, tabStatus, isLoading } = useMyReward();
    const { getRewardDetail, isLoading: isLoadingRewardDetail } = useMyRewardDetail();

    const renderTabButton = React.useCallback(
        (tab: RewardStatusType, label: string) => {
            const isActive = tabStatus === tab;
            return (
                <MyTouchable
                    onPress={() => handleSetStatus(tab)}
                    className={`px-4 py-1.5 rounded-full ${isActive ? "bg-lightBlue" : "border border-gray"}`}>
                    <Text
                        className={`text-base text-center ${isActive ? "text-darkBlue" : "text-darkBlue opacity-50"}`}>
                        {label}
                    </Text>
                </MyTouchable>
            );
        },
        [handleSetStatus, tabStatus]
    );

    const onPressVoucher = React.useCallback(
        (id: number) => {
            getRewardDetail(id);
        },
        [getRewardDetail]
    );

    const renderVoucherItem = React.useCallback(
        ({ item }: ListRenderItemInfo<RewardResponse>) => (
            <VoucherCard item={item} onPress={onPressVoucher} myVoucher />
        ),
        [onPressVoucher]
    );

    const renderEmptyState = React.useMemo(
        () => (
            <EmptyState
                title="You haven't redeemed any voucher"
                description="Browse available vouchers in Rewards."
                buttonText="Redeem my points"
            />
        ),
        []
    );

    return (
        <Container isLoading={isLoadingRewardDetail}>
            <Header title="My Voucher" />

            <Box className="mx-3 flex-1">
                <Box className="mb-4">
                    <HStack
                        className="justify-start items-center gap-3 px-4 py-2"
                        style={{
                            gap: 12,
                            padding: 8,
                            paddingHorizontal: 16,
                            alignItems: "center"
                        }}>
                        {renderTabButton(RewardStatusType.active, "Available")}
                        {renderTabButton(RewardStatusType.expired, "Expired")}
                    </HStack>
                </Box>

                <ListView
                    keyList="id"
                    data={myReward}
                    renderItem={renderVoucherItem}
                    numColumns={2}
                    emptyComponent={renderEmptyState}
                    isLoading={isLoading}
                    skeletonComponent={VoucherCardSkeleton}
                />
            </Box>
        </Container>
    );
};

export default MyVoucherScreen;
