import React from "react";

import { getColor, useRouteParams } from "@/presentation/hooks";

import { AmountSection } from "@/presentation/components/amount";
import { Header } from "@/presentation/components/header";
import { MyButton } from "@/presentation/components/myButton";
import { RedeemToSection } from "@/presentation/components/redeem";
import { Box, Container, IconComponent, ScrollView, Text } from "@/presentation/components/ui";
import { usePoint } from "@/presentation/hooks/point";
import { useRedeem } from "@/presentation/hooks/rewards";
import { RouteName } from "@/shared/constants";

const RedeemPointScreen = () => {
    const params = useRouteParams<typeof RouteName.RedeemPoint>();

    const { point, canRedeem } = usePoint();

    const { handleRedeem, isLoading } = useRedeem();

    const handleRedeemPoints = React.useCallback(() => {
        if (!params?.reward?.id || !canRedeem(Number(params?.reward?.price))) return;
        handleRedeem(params?.reward?.id);
    }, [params?.reward?.id, params?.reward?.price, canRedeem, handleRedeem]);

    return (
        <Container isLoading={isLoading}>
            <Header title="Redeem Points" />

            <ScrollView className="flex-1">
                <Box>
                    <Box className="gap-y-3">
                        <AmountSection
                            points={params?.reward?.price.toString() || "0"}
                            balance={point?.toString() || "0"}
                        />
                        <RedeemToSection
                            rewardPoints={params?.reward?.price.toString() || "0"}
                            rewardName={params?.reward?.name || ""}
                            rewardLabel={params?.reward?.label || ""}
                        />
                        <Box className="absolute top-5 right-10 bottom-0 flex items-center justify-center">
                            <Box className="w-[40px] h-[40px] bg-white rounded-full items-center justify-center">
                                <IconComponent name="arrow-down" font="feather" color={getColor("blue")} size={24} />
                            </Box>
                        </Box>
                    </Box>

                    <Box className="mx-5 mt-5">
                        <Text className="text-neutralGray text-[16px]">
                            Convert your CO₂ Points to {params?.reward?.name} points and select from a wide range of
                            services, including transport, on demand food delivery, consumer services goods, and etc.
                        </Text>
                    </Box>
                </Box>
            </ScrollView>

            <Box className="px-5 py-4">
                <MyButton text="Redeem Points" onPress={handleRedeemPoints} variant="primary" />
            </Box>
        </Container>
    );
};

export default RedeemPointScreen;
