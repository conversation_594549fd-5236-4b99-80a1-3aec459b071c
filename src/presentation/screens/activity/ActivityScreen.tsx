import { useNavigation } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import React from "react";
import { Image, ImageBackground } from "react-native";

import { ShowPopupRequiredObs } from "@/data/services/observable";
import { Header } from "@/presentation/components/header";
import { Box, Container, HStack, ScrollView, Text } from "@/presentation/components/ui";
import { useActivity } from "@/presentation/hooks/activity";
import { useGuest } from "@/presentation/hooks/user";
import { ImageAssets, RouteName } from "@/shared/constants";
import { formatPoints } from "@/shared/helper";

const ActivityScreen = () => {
    const { leaderboard, isLoading } = useActivity();
    const navigation = useNavigation();
    const { isGuestMode } = useGuest();

    const onActivityHistory = React.useCallback(() => {
        if (isGuestMode()) {
            return ShowPopupRequiredObs.action();
        }
        navigation.navigate(RouteName.ActivityHistory);
    }, [navigation, isGuestMode]);

    const myImpact = leaderboard?.my_impact?.total_points || 0;
    const myRank = leaderboard?.leaderboard_position?.my_rank;
    const totalContributors = leaderboard?.leaderboard_position?.total_contributors || 0;
    const topContributors = leaderboard?.top_contributors || [];
    const myRankData = leaderboard?.my_rank_data;

    return (
        <Container isLoading={isLoading}>
            <ImageBackground source={ImageAssets.bgActivity} className="w-full h-full">
                <Box className="pt-4">
                    <Header
                        title="Activity"
                        titleColor="white"
                        isShowBack={false}
                        rightComponent={ImageAssets.activityOff}
                        tintColor="white"
                        onPress={onActivityHistory}
                    />
                </Box>
                <ScrollView className="px-3">
                    <Box className="mt-4 mb-6">
                        <Box>
                            <Box className="bg-white/20 rounded-t-[12px] px-5 py-3 backdrop-blur-md self-start">
                                <Text className="text-white text-[13px] font-bold">Your recycling wins so far</Text>
                            </Box>
                            <Box className="bg-white/30 rounded-tr-[12px] rounded-b-[12px] p-5 backdrop-blur-md">
                                <Box className="absolute top-0 left-0 right-0 h-[1px]">
                                    <LinearGradient
                                        colors={["transparent", "rgba(255,255,255,0.6)", "transparent"]}
                                        start={{ x: 0, y: 0.5 }}
                                        end={{ x: 1, y: 0.5 }}
                                        style={{ height: 1, width: "100%" }}
                                    />
                                </Box>
                                <Box className="flex-row items-center">
                                    <Image source={ImageAssets.trophy} className="w-16 h-16 mr-4" alt="trophy" />
                                    <Box className="gap-1">
                                        <Text className="text-white text-[24px] font-bold">
                                            {formatPoints(myImpact)} CO
                                            <Text className="text-[9px] align-bottom">2</Text> Points
                                        </Text>
                                        <Text className="text-white text-[13px]">
                                            Your total CO<Text className="text-[9px] align-bottom">2</Text> emission
                                            saved
                                        </Text>
                                    </Box>
                                </Box>
                                <Box className="absolute bottom-0 left-0 right-0 h-[1px]">
                                    <LinearGradient
                                        colors={["transparent", "rgba(255,255,255,0.6)", "transparent"]}
                                        start={{ x: 0, y: 0.5 }}
                                        end={{ x: 1, y: 0.5 }}
                                        style={{ height: 1, width: "100%" }}
                                    />
                                </Box>
                            </Box>
                            <Text className="text-white text-[11px] font-bold">
                                * Reward points are calculated from August 1. Points from the old app are not included
                            </Text>
                        </Box>

                        <Box className="mt-6 gap-3">
                            <HStack className="justify-between items-center">
                                <HStack className="items-center gap-2">
                                    <Box className="bg-white/30 rounded-full p-1 backdrop-blur-md self-start">
                                        <Image source={ImageAssets.crown} className="w-[18px] h-[18px]" alt="crown" />
                                    </Box>
                                    <Text className="text-white text-[13px] font-bold">
                                        {myRank ? `#${myRank}` : "Not ranked"}
                                    </Text>
                                </HStack>
                                <Text className="text-white text-[13px] font-bold">
                                    of {totalContributors} contributors
                                </Text>
                            </HStack>

                            <Box className="gap-3 bg-white rounded-xl p-4">
                                <Text className="text-darkGray text-[16px] font-bold">Top Contributors</Text>
                                {topContributors.map((contributor) => (
                                    <Box key={contributor.rank} className="bg-white/20 rounded-xl backdrop-blur-md p-2">
                                        <Box className="flex-row justify-between items-center">
                                            <HStack className="items-center flex-1">
                                                <Box className="bg-gray rounded-full w-[40px] h-[40px] items-center justify-center mr-3">
                                                    <Text className="text-[13px] font-bold">
                                                        {contributor.initials}
                                                    </Text>
                                                    <Box className="absolute top-[-5px] right-[-5px]">
                                                        <Box>
                                                            <Image
                                                                source={ImageAssets.rank}
                                                                className="w-[18px] h-[18px]"
                                                                alt="rank"
                                                            />
                                                            <Box className="absolute top-0 left-0 right-0 bottom-0 rounded-full w-[18px] h-[18px] items-center justify-center">
                                                                <Text className="text-[13px] font-bold text-white">
                                                                    {contributor.rank}
                                                                </Text>
                                                            </Box>
                                                        </Box>
                                                    </Box>
                                                </Box>
                                                <Box className="flex-1">
                                                    <Text className="text-black font-semibold line-clamp-2">
                                                        {contributor.name}
                                                    </Text>
                                                </Box>
                                            </HStack>
                                            <HStack className="items-center gap-1">
                                                <Text className="text-darkGray font-bold text-[16px]">
                                                    {formatPoints(contributor.total_points)}
                                                </Text>
                                                <Text className="text-darkGray text-[13px]">
                                                    CO<Text className="text-[9px] align-bottom">2</Text> Points
                                                </Text>
                                            </HStack>
                                        </Box>
                                    </Box>
                                ))}

                                {myRankData && (
                                    <Box className="bg-lightBlue rounded-xl backdrop-blur-md p-2">
                                        <Box className="flex-row justify-between items-center gap-2">
                                            <HStack className="items-center flex-1">
                                                <Box className="bg-blue rounded-full w-[40px] h-[40px] items-center justify-center mr-3">
                                                    <Text className="text-[13px] font-bold text-white">
                                                        {myRankData.initials}
                                                    </Text>
                                                </Box>
                                                <Box className="flex-1">
                                                    <Text className="text-black font-semibold line-clamp-2">
                                                        {myRankData.name}
                                                    </Text>
                                                </Box>
                                            </HStack>
                                            <HStack className="items-center gap-1">
                                                <Text className="text-darkGray font-bold text-[16px]">
                                                    {formatPoints(myRankData.total_points)}
                                                </Text>
                                                <Text className="text-darkGray text-[13px]">
                                                    CO<Text className="text-[9px] align-bottom">2</Text> Points
                                                </Text>
                                            </HStack>
                                        </Box>
                                    </Box>
                                )}

                                {topContributors.length === 0 && !myRankData && (
                                    <Box className="bg-white/20 rounded-xl backdrop-blur-md p-4 items-center">
                                        <Text className="text-darkGray text-[14px] text-center">
                                            No leaderboard data available yet
                                        </Text>
                                    </Box>
                                )}
                            </Box>
                        </Box>
                    </Box>
                </ScrollView>
            </ImageBackground>
        </Container>
    );
};

export default ActivityScreen;
