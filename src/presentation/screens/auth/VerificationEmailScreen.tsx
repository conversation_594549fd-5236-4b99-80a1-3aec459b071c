import React from "react";

import { useUserQueries } from "@/data/queries";

import { useVerificationEmail } from "@/presentation/hooks";

import { Header } from "@/presentation/components/header";
import { Input } from "@/presentation/components/input";
import { ButtonSubmitVerify } from "@/presentation/components/myButton";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, ScrollView, Text } from "@/presentation/components/ui";
import { ImageAssets } from "@/shared/constants";

const VerificationEmailScreen: React.FC = () => {
    const { isEnable, timeFormatted, otpRef, isLoadingOtp, getInputProps, handleSubmit, handleResendOtp } =
        useVerificationEmail();
    const { user } = useUserQueries();

    const isLoadingPage = React.useMemo(() => isLoadingOtp, [isLoadingOtp]);

    return (
        <Container isLoading={isLoadingPage}>
            <Header title="Verification" rightComponent={ImageAssets.icInfo} />
            <ScrollView className="px-4 pt-5">
                <Text className="text-center">
                    {`We have sent you a verification OTP to \n ${user?.email} to complete verification`}
                </Text>
                <Box className="px-5 mt-10">
                    <Input ref={otpRef} type="otp" {...getInputProps("otp")} />
                    {isEnable ? (
                        <MyTouchable onPress={handleResendOtp} disabled={isLoadingPage}>
                            <Text className="text-center text-blue">Resend</Text>
                        </MyTouchable>
                    ) : (
                        <Text className="text-center ">You can resend your code in {timeFormatted}</Text>
                    )}
                </Box>
            </ScrollView>
            <Box className="py-4">
                <ButtonSubmitVerify handleVerification={handleSubmit} />
            </Box>
        </Container>
    );
};

export default VerificationEmailScreen;
