import React from "react";
import { object } from "yup";

import { useForm, useRouteParams } from "@/presentation/hooks";

import { RootNavigator } from "@/data/services/navigation";
import { Header } from "@/presentation/components/header";
import { Input } from "@/presentation/components/input";
import { ButtonSubmitVerify } from "@/presentation/components/myButton";
import { Box, Container, ScrollView, Text } from "@/presentation/components/ui";
import { ImageAssets, RouteName } from "@/shared/constants";
import { confirmPasswordSchema, passwordSchema } from "@/shared/validations";

const SetNewPassword = () => {
    const params = useRouteParams<typeof RouteName.SetNewPassword>();

    const { getInputProps, handleSubmit } = useForm({
        initialValues: {
            password: "",
            confirmPassword: ""
        },
        validationSchema: object().shape({
            password: passwordSchema,
            confirmPassword: confirmPasswordSchema("password")
        }),
        onSubmit: (values) => {
            if (!params?.phoneNumber) return;
            RootNavigator.navigate(RouteName.SetNewNickName, {
                phoneNumber: params?.phoneNumber,
                newPassword: values?.confirmPassword
            });
        }
    });

    return (
        <Container>
            <Header title="Set New Password" rightComponent={ImageAssets.icInfo} />
            <ScrollView className="px-4 pt-5">
                <Text>Please set your new password</Text>
                <Box className="mt-4 gap-y-4">
                    <Input
                        title="New Password"
                        placeholder="New Password"
                        secureTextEntry
                        required
                        {...getInputProps("password")}
                    />
                    <Input
                        title="Confirm Password"
                        placeholder="Confirm Password"
                        secureTextEntry
                        required
                        {...getInputProps("confirmPassword")}
                    />
                </Box>
            </ScrollView>
            <Box className="py-4">
                <ButtonSubmitVerify handleVerification={handleSubmit} needHelp={false} />
            </Box>
        </Container>
    );
};

export default SetNewPassword;
