import { useNavigation } from "@react-navigation/native";
import React from "react";
import { Alert, Keyboard } from "react-native";
import { object } from "yup";

import { validateApi } from "@/data/api";

import { useForm, usePhoneValidation, useRouteParams } from "@/presentation/hooks";

import { FirebaseAuthService } from "@/data/services/firebase";
import { Header } from "@/presentation/components/header";
import { Input } from "@/presentation/components/input";
import { ButtonSubmitVerify } from "@/presentation/components/myButton";
import { Box, Container, ScrollView, Text } from "@/presentation/components/ui";
import { ImageAssets, RouteName } from "@/shared/constants";
import { formatPhoneNumber } from "@/shared/helper";
import { phoneNumberSchema } from "@/shared/validations";

const ForgotPasswordScreen = () => {
    const params = useRouteParams<typeof RouteName.ForgotPassword>();

    const navigation = useNavigation();
    const [isLoading, setIsLoading] = React.useState<boolean>(false);
    const { isValidating } = usePhoneValidation();

    const { getInputProps, handleSubmit } = useForm({
        initialValues: {
            phoneNumber: ""
        },
        validationSchema: object().shape({
            phoneNumber: phoneNumberSchema
        }),
        onSubmit: async (values) => {
            Keyboard.dismiss();
            const errorMessage = await validateApi.validatePhoneNumber(values.phoneNumber);
            if (!errorMessage) {
                return Alert.alert("Error", "Phone number is not registered");
            }

            const formattedPhone = formatPhoneNumber({ phoneNumber: values.phoneNumber });

            if (params?.forOldUser) {
                navigation.navigate(RouteName.Auth, {
                    screen: RouteName.SetNewPassword,
                    params: {
                        phoneNumber: formattedPhone
                    }
                });
                return;
            }

            try {
                setIsLoading(true);
                const isSent = await FirebaseAuthService.sendOTP(formattedPhone);
                if (!isSent) return;

                navigation.navigate(RouteName.Verification, {
                    phoneNumber: formattedPhone,
                    type: "forgotPassword"
                });
            } catch (e) {
                /* empty */
            } finally {
                setIsLoading(false);
            }
        }
    });

    return (
        <Container>
            <Header title={params?.title || "Forgot Password"} rightComponent={ImageAssets.icInfo} />
            <ScrollView className="px-4 pt-5">
                <Text>
                    Enter your registered phone number to reset your password. An OTP will be sent to you for
                    verification
                </Text>
                <Box className="mt-4">
                    <Input
                        title="Phone number"
                        placeholder="Phone number"
                        type="phone"
                        required
                        {...getInputProps("phoneNumber")}
                        editable={!isLoading}
                    />
                </Box>
            </ScrollView>
            <Box className="py-4">
                <ButtonSubmitVerify handleVerification={handleSubmit} isLoading={isLoading || isValidating} />
            </Box>
        </Container>
    );
};

export default ForgotPasswordScreen;
