import React from "react";
import { object } from "yup";

import { useUpdatePasswordQueries } from "@/data/queries";

import { useForm, useRouteParams } from "@/presentation/hooks";

import { Header } from "@/presentation/components/header";
import { Input } from "@/presentation/components/input";
import { ButtonSubmitVerify } from "@/presentation/components/myButton";
import { Box, Container, ScrollView, Text } from "@/presentation/components/ui";
import { ImageAssets, RouteName } from "@/shared/constants";
import { formatPhoneWithCountryCode } from "@/shared/helper";
import { nicknameWithApiSchema } from "@/shared/validations";

const SetNewNickNameScreen = () => {
    const params = useRouteParams<typeof RouteName.SetNewNickName>();
    const { updatePassword, isLoading } = useUpdatePasswordQueries();

    const { getInputProps, handleSubmit } = useForm({
        initialValues: {
            newNickName: ""
        },
        validationSchema: object().shape({
            newNickName: nicknameWithApiSchema
        }),
        onSubmit: (values) => {
            if (!params?.phoneNumber || !params?.newPassword) return;
            updatePassword({
                password: params.newPassword,
                password_confirmation: params.newPassword,
                phone: formatPhoneWithCountryCode({ phone: params?.phoneNumber }),
                display_name: values?.newNickName
            });
        }
    });

    return (
        <Container>
            <Header title="Set New Nickname" rightComponent={ImageAssets.icInfo} />
            <ScrollView className="px-4 pt-5">
                <Text>Please set your new nickname</Text>
                <Box className="mt-4 gap-y-4">
                    <Input
                        title="New Nickname"
                        placeholder="New Nickname"
                        required
                        {...getInputProps("newNickName")}
                        editable={!isLoading}
                    />
                </Box>
            </ScrollView>
            <Box className="py-4">
                <ButtonSubmitVerify handleVerification={handleSubmit} needHelp={false} isLoading={isLoading} />
            </Box>
        </Container>
    );
};

export default SetNewNickNameScreen;
