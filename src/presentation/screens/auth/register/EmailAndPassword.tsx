import React from "react";
import { object } from "yup";

import { useRegisterStore } from "@/app/store";

import { useForm } from "@/presentation/hooks";

import { Input } from "@/presentation/components/input";
import { Text, VStack } from "@/presentation/components/ui";
import { formatPhoneNumber, splitPhoneNumber } from "@/shared/helper";
import {
    confirmPasswordSchema,
    emailSchema,
    emailWithApiSchema,
    passwordSchema,
    phoneNumberSchema
} from "@/shared/validations";

type EmailAndPasswordProps = {
    isSchool?: boolean;
    onNextStep?: () => void;
    initialValues?: RegisterRequest["individual"] | RegisterRequest["school"];
};

export type EmailAndPasswordRefType = {
    onSubmit: () => void;
};

const EmailAndPassword = React.forwardRef<EmailAndPasswordRefType, EmailAndPasswordProps>(
    ({ isSchool, onNextStep, initialValues }, ref) => {
        const { request, setIndividual, setSchool } = useRegisterStore();

        const getInitialValues = React.useMemo(() => {
            const school = initialValues as RegisterRequest["school"];
            const individual = initialValues as RegisterRequest["individual"];

            const { number } = splitPhoneNumber(initialValues?.phone ?? "");

            const schoolValues = {
                phone: number || "",
                password: school?.password || "",
                confirmPassword: school?.password || ""
            };

            const individualValues = {
                password: individual?.password || "",
                confirmPassword: individual?.password || ""
            };

            const commonValues = {
                email: school?.email || individual?.email || ""
            };

            return isSchool
                ? {
                      ...schoolValues,
                      ...commonValues
                  }
                : {
                      ...individualValues,
                      ...commonValues
                  };
        }, [isSchool, initialValues]);

        const validationSchema = React.useMemo(() => {
            const commonSchema = object().shape({
                email: emailWithApiSchema
            });

            if (isSchool) {
                return object().shape({
                    phone: phoneNumberSchema,
                    password: passwordSchema,
                    confirmPassword: confirmPasswordSchema("password"),
                    email: emailSchema
                });
            }
            return object()
                .shape({
                    password: passwordSchema,
                    confirmPassword: confirmPasswordSchema("password")
                })
                .concat(commonSchema);
        }, [isSchool]);

        const { getInputProps, handleSubmit } = useForm({
            enableReinitialize: true,
            initialValues: getInitialValues,
            validationSchema,
            onSubmit: (values) => {
                if (isSchool) {
                    const schoolValues = values as unknown as RegisterRequest["school"];

                    const formattedPhone = formatPhoneNumber({ phoneNumber: schoolValues?.phone });

                    setSchool({
                        ...request?.school,
                        email: schoolValues?.email,
                        phone: formattedPhone,
                        password: schoolValues?.password
                    });
                } else {
                    const individualValues = values as unknown as RegisterRequest["individual"];
                    setIndividual({
                        ...request?.individual,
                        email: individualValues?.email,
                        password: individualValues?.password
                    });
                }
                onNextStep?.();
            }
        });

        React.useImperativeHandle(ref, () => ({
            onSubmit: handleSubmit
        }));

        const renderContent = React.useMemo(() => {
            if (isSchool) {
                return (
                    <>
                        <Input
                            title="Email"
                            placeholder="Enter email"
                            required
                            {...getInputProps("email")}
                            type="email"
                        />
                        <Input
                            title="Password"
                            placeholder="Enter password"
                            required
                            secureTextEntry
                            {...getInputProps("password")}
                        />
                        <Input
                            title="Confirm Password"
                            placeholder="Enter confirm password"
                            required
                            secureTextEntry
                            {...getInputProps("confirmPassword")}
                        />
                        <Input
                            title="Phone number"
                            placeholder="Enter phone number"
                            required
                            type="phone"
                            {...getInputProps("phone" as keyof typeof getInitialValues)}
                        />
                    </>
                );
            }
            return (
                <>
                    <Input
                        title="Email"
                        placeholder="Enter email"
                        required
                        {...getInputProps("email")}
                        autoCapitalize="none"
                        keyboardType="email-address"
                        autoCorrect={false}
                    />
                    <Input
                        title="Password"
                        placeholder="Enter password"
                        required
                        secureTextEntry
                        {...getInputProps("password" as keyof typeof getInitialValues)}
                    />
                    <Input
                        title="Confirm Password"
                        placeholder="Enter confirm password"
                        required
                        secureTextEntry
                        {...getInputProps("confirmPassword" as keyof typeof getInitialValues)}
                    />
                </>
            );
        }, [isSchool, getInputProps]);

        const renderTitle = React.useMemo(() => {
            if (isSchool) {
                return "Email & Phone Number";
            }
            return "Email & password";
        }, [isSchool]);

        return (
            <VStack space="lg" className="mb-10">
                <Text className="text-[24px] font-bold">{renderTitle}</Text>
                <VStack className="gap-5">{renderContent}</VStack>
            </VStack>
        );
    }
);

export default EmailAndPassword;
