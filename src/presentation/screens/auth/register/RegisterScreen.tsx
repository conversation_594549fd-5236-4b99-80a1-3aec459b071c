import { useNavigation } from "@react-navigation/native";
import React from "react";
import { BackHandler } from "react-native";

import { useLoadValidation, useRegister } from "@/presentation/hooks";

import CreateProfile from "./CreateProfile";
import EmailAndPassword from "./EmailAndPassword";
import PhoneNumber from "./PhoneNumber";

import { CheckBox } from "@/presentation/components/checkBox";
import { Header } from "@/presentation/components/header";
import { MyButton } from "@/presentation/components/myButton";
import { Box, Container, HStack, ScrollView } from "@/presentation/components/ui";

const RegisterScreen = () => {
    const { isValidating } = useLoadValidation();

    const {
        step,
        nextStep,
        previousStep,
        isSchool,
        isChecked,
        setIsChecked,
        shouldShowCheckbox,
        isLoadingPage,
        isNextButtonDisabled,
        request,
        handleNext: handleNextRegister,
        createProfileRef,
        emailAndPasswordRef,
        phoneNumberRef
    } = useRegister();

    const navigation = useNavigation();

    const handleNext = React.useCallback(() => {
        if (isValidating) {
            return;
        }
        handleNextRegister();
    }, [handleNextRegister, isValidating]);

    const handleBackPress = React.useCallback(() => {
        if (step > 0) {
            previousStep();
        } else {
            navigation.goBack();
        }
    }, [step, previousStep, navigation]);

    const handleHardwareBackPress = React.useCallback(() => {
        handleBackPress();
        return true;
    }, [handleBackPress]);

    React.useEffect(() => {
        const backHandler = BackHandler.addEventListener("hardwareBackPress", handleHardwareBackPress);
        return () => backHandler.remove();
    }, [handleHardwareBackPress]);

    const renderSchoolStepIndicator = React.useCallback(() => {
        return (
            <HStack className="w-full justify-between flex-1 gap-2">
                <Box className="h-2 rounded-full bg-green flex-1" />
                <Box className={`h-2 rounded-full ${step >= 1 ? "bg-green" : "bg-lightGray"} flex-1`} />
            </HStack>
        );
    }, [step]);

    const renderIndividualStepIndicator = React.useCallback(() => {
        return (
            <HStack className="w-full justify-between flex-1 gap-2">
                <Box className="h-2 rounded-full bg-green flex-1" />
                <Box className={`h-2 rounded-full ${step >= 1 ? "bg-green" : "bg-lightGray"} flex-1`} />
                <Box className={`h-2 rounded-full ${step >= 2 ? "bg-green" : "bg-lightGray"} flex-1`} />
            </HStack>
        );
    }, [step]);

    const renderTitle = React.useMemo(() => {
        return isSchool ? renderSchoolStepIndicator() : renderIndividualStepIndicator();
    }, [isSchool, renderSchoolStepIndicator, renderIndividualStepIndicator]);

    const renderContent = React.useMemo(() => {
        const initialValues = request?.individual || request?.school;

        switch (step) {
            case 0:
                return (
                    <CreateProfile
                        isSchool={isSchool}
                        ref={createProfileRef}
                        onNextStep={nextStep}
                        initialValues={initialValues}
                    />
                );
            case 1:
                return (
                    <EmailAndPassword
                        isSchool={isSchool}
                        ref={emailAndPasswordRef}
                        onNextStep={nextStep}
                        initialValues={initialValues}
                    />
                );
            case 2:
                return <PhoneNumber ref={phoneNumberRef} onNextStep={nextStep} initialValues={request?.individual} />;
            default:
                return null;
        }
    }, [
        request?.individual,
        request?.school,
        step,
        isSchool,
        createProfileRef,
        nextStep,
        emailAndPasswordRef,
        phoneNumberRef
    ]);

    const renderCheckBox = React.useMemo(() => {
        return shouldShowCheckbox ? <CheckBox onChange={setIsChecked} value={isChecked} /> : null;
    }, [shouldShowCheckbox, setIsChecked, isChecked]);

    return (
        <Container isLoading={isLoadingPage}>
            <Header title={renderTitle} onBack={handleBackPress} />
            <ScrollView className="px-4">{renderContent}</ScrollView>

            <Box className="px-4 gap-3 py-4">
                {renderCheckBox}
                <MyButton text="Next" onPress={handleNext} disabled={isNextButtonDisabled} />
            </Box>
        </Container>
    );
};

export default RegisterScreen;
