import React from "react";
import { object, string } from "yup";

import { useRegisterStore } from "@/app/store";

import { useAddress, useForm } from "@/presentation/hooks";

import { businessDrop } from "@/data/local";
import { Input } from "@/presentation/components/input";
import { Text, VStack } from "@/presentation/components/ui";
import { BusinessType } from "@/shared/constants";
import {
    businessTypeSchema,
    dobSchema,
    firstNameSchema,
    lastNameSchema,
    nicknameWithApiSchema,
    postalCodeSchema,
    schoolNameSchema
} from "@/shared/validations";

type CreateProfileProps = {
    isSchool?: boolean;
    onNextStep?: () => void;
    initialValues?: RegisterRequest["individual"] | RegisterRequest["school"];
};

export type CreateProfileRefType = {
    onSubmit: () => void;
};

const CreateProfile = React.forwardRef<CreateProfileRefType, CreateProfileProps>(
    ({ isSchool, onNextStep, initialValues }, ref) => {
        const { setIndividual, setSchool, request } = useRegisterStore();
        const { getAddressByPostalCode, isLoading: isLoadingAddress } = useAddress();

        const getInitialValues = React.useMemo(() => {
            const school = initialValues as RegisterRequest["school"];
            const individual = initialValues as RegisterRequest["individual"];

            const schoolValues = {
                name: school?.name || "",
                type: school?.type || ""
            };

            const individualValues = {
                firstName: individual?.firstName || "",
                lastName: individual?.lastName || "",
                displayName: individual?.displayName || "",
                dob: individual?.dob || ""
            };

            const commonValues = {
                address: school?.address || individual?.address || "",
                postalCode: school?.postalCode || individual?.postalCode || ""
            };

            return isSchool
                ? {
                      ...schoolValues,
                      ...commonValues
                  }
                : {
                      ...individualValues,
                      ...commonValues
                  };
        }, [initialValues, isSchool]);

        const validationSchema = React.useMemo(() => {
            const commonSchema = object().shape({
                address: string(),
                postalCode: postalCodeSchema
            });

            if (isSchool) {
                return object()
                    .shape({
                        name: schoolNameSchema,
                        type: businessTypeSchema
                    })
                    .concat(commonSchema);
            }
            return object()
                .shape({
                    firstName: firstNameSchema,
                    lastName: lastNameSchema,
                    displayName: nicknameWithApiSchema,
                    dob: dobSchema
                })
                .concat(commonSchema);
        }, [isSchool]);

        const {
            getInputProps,
            handleSubmit,
            values: valuesForm,
            setFieldValue
        } = useForm({
            enableReinitialize: true,
            initialValues: getInitialValues,
            validationSchema,
            onSubmit: (values) => {
                if (isSchool) {
                    const schoolValues = values as unknown as RegisterRequest["school"];
                    setSchool({
                        ...request?.school,
                        name: schoolValues?.name,
                        address: schoolValues?.address,
                        postalCode: schoolValues?.postalCode,
                        type: Number(schoolValues?.type) === 1 ? BusinessType.School : BusinessType.Enterprise
                    });
                } else {
                    const individualValues = values as unknown as RegisterRequest["individual"];
                    setIndividual({
                        ...request?.individual,
                        firstName: individualValues?.firstName,
                        lastName: individualValues?.lastName,
                        displayName: individualValues?.displayName,
                        dob: individualValues?.dob,
                        address: individualValues?.address,
                        postalCode: individualValues?.postalCode
                    });
                }
                onNextStep?.();
            }
        });

        React.useEffect(() => {
            const fetchAddress = async () => {
                if (valuesForm?.postalCode?.length === 6 && valuesForm?.postalCode !== initialValues?.postalCode) {
                    const addressResponse = await getAddressByPostalCode(valuesForm.postalCode);
                    setFieldValue("address", addressResponse?.location?.ADDRESS || "");
                } else if (
                    valuesForm?.postalCode?.length === 0 &&
                    valuesForm?.postalCode !== initialValues?.postalCode
                ) {
                    setFieldValue("address", "");
                }
            };

            fetchAddress();
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [valuesForm.postalCode]);

        React.useImperativeHandle(ref, () => ({
            onSubmit: handleSubmit
        }));

        const renderContent = React.useMemo(() => {
            if (isSchool) {
                return (
                    <>
                        <Input
                            title="School Name"
                            placeholder="Enter school name"
                            required
                            {...getInputProps("name" as keyof typeof getInitialValues)}
                        />
                        <Input
                            title="Postal Code"
                            placeholder="Postal Code"
                            {...getInputProps("postalCode" as keyof typeof getInitialValues)}
                            keyboardType="numeric"
                            isLoading={isLoadingAddress}
                            maxLength={6}
                        />
                        {valuesForm?.address && !isLoadingAddress && (
                            <Input
                                title="Address"
                                placeholder="Address"
                                {...getInputProps("address" as keyof typeof getInitialValues)}
                                editable={false}
                                isLoading={isLoadingAddress}
                            />
                        )}
                        <Input
                            title="School/Enterprise"
                            placeholder="Select school/enterprise"
                            type="dropdown"
                            required
                            dataDropdown={businessDrop}
                            {...getInputProps("type" as keyof typeof getInitialValues)}
                        />
                    </>
                );
            }
            return (
                <>
                    <Input
                        title="First Name"
                        placeholder="Enter first name"
                        required
                        {...getInputProps("firstName" as keyof typeof getInitialValues)}
                    />
                    <Input
                        title="Last Name"
                        placeholder="Enter last name"
                        required
                        {...getInputProps("lastName" as keyof typeof getInitialValues)}
                    />
                    <Input
                        title="Nickname"
                        placeholder="Enter nickname"
                        required
                        {...getInputProps("displayName" as keyof typeof getInitialValues)}
                    />
                    <Input
                        title="Date of birth"
                        type="date"
                        {...getInputProps("dob" as keyof typeof getInitialValues)}
                    />
                    <Input
                        title="Postal Code"
                        placeholder="Postal Code"
                        required
                        {...getInputProps("postalCode" as keyof typeof getInitialValues)}
                        keyboardType="numeric"
                        isLoading={isLoadingAddress}
                        maxLength={6}
                    />
                    {valuesForm?.address && !isLoadingAddress && (
                        <Input
                            title="Address"
                            placeholder="Address"
                            {...getInputProps("address" as keyof typeof getInitialValues)}
                            editable={false}
                            isLoading={isLoadingAddress}
                        />
                    )}
                </>
            );
        }, [isSchool, getInputProps, isLoadingAddress, valuesForm?.address]);

        return (
            <VStack space="lg" className="mb-10">
                <Text className="text-[24px] font-bold">Create profile</Text>
                <VStack className="gap-5">{renderContent}</VStack>
            </VStack>
        );
    }
);

export default CreateProfile;
