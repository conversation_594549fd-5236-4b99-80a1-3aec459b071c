import { useNavigation } from "@react-navigation/native";
import React from "react";
import { StyleSheet } from "react-native";
import { Camera, useCameraDevice } from "react-native-vision-camera";

import { useOptimizeImage, useRouteParams } from "@/presentation/hooks";

import { walkthroughService, WalkthroughStep } from "@/data/services/observable";
import { CameraEmptyPermission } from "@/presentation/components/emptyState";
import TookPhotoFrameOverlay from "@/presentation/components/frame/TookPhotoFrameOverlay";
import { Header } from "@/presentation/components/header";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container } from "@/presentation/components/ui";
import TookPhotoWalkthrough from "@/presentation/components/walkthrough/TookPhotoWalkthrough";
import { useCameraPermission } from "@/presentation/hooks/camera";
import { ImageAssets, RouteName } from "@/shared/constants";

const TookPhotoScreen = () => {
    const params = useRouteParams<typeof RouteName.TookPhoto>();

    const { permissionState, requestPermission, onError } = useCameraPermission();
    const { optimizeImage } = useOptimizeImage();

    const hasPermission = permissionState.isGranted;
    const [isActive, setIsActive] = React.useState(true);
    const [isTorchOn, setIsTorchOn] = React.useState(false);
    const [isWalkthroughVisible, setIsWalkthroughVisible] = React.useState(false);
    const [isTakingPhoto, setIsTakingPhoto] = React.useState(false);
    const cameraRef = React.useRef<Camera | null>(null);
    const device = useCameraDevice("back");
    const navigation = useNavigation();

    React.useEffect(() => {
        return () => {
            setIsActive(false);
        };
    }, []);

    React.useEffect(() => {
        if (!hasPermission) return;

        const initWalkthrough = async () => {
            if (params?.fromWalkthrough) {
                setIsWalkthroughVisible(true);
            } else if (params?.fromWalkthroughBack) {
                const timer = setTimeout(() => {
                    setIsWalkthroughVisible(true);
                }, 300);
                return () => clearTimeout(timer);
            }
        };
        initWalkthrough();
    }, [hasPermission, params?.fromWalkthrough, params?.fromWalkthroughBack]);

    React.useEffect(() => {
        const subscription = walkthroughService.walkthrough$.subscribe((event) => {
            if (event.show && event.currentStep === WalkthroughStep.TOOK_PHOTO) {
                setIsWalkthroughVisible(true);
            }
        });

        return () => {
            subscription.unsubscribe();
        };
    }, []);

    React.useEffect(() => {
        if (hasPermission && device) {
            setIsActive(false);

            const resetTimer = setTimeout(() => {
                try {
                    setIsActive(true);
                } catch (error) {
                    setTimeout(() => {
                        setIsActive(true);
                    }, 500);
                }
            }, 500);

            return () => {
                clearTimeout(resetTimer);
            };
        }
    }, [hasPermission, device]);

    const handleCloseWalkthrough = React.useCallback(() => {
        setIsWalkthroughVisible(false);
        walkthroughService.hideWalkthrough();
    }, []);

    const takePhoto = React.useCallback(async () => {
        if (isTakingPhoto || !cameraRef.current) return;

        try {
            setIsTakingPhoto(true);

            const photo = await cameraRef.current.takeSnapshot();

            const optimizedPhoto = await optimizeImage(photo);

            navigation.navigate(RouteName.ScanStack, {
                screen: RouteName.ConfirmPhoto,
                params: { photoPath: optimizedPhoto.path, binId: __DEV__ ? 1749 : params?.binId }
            });
        } catch (error) {
            /* empty */
        } finally {
            setIsTakingPhoto(false);
        }
    }, [isTakingPhoto, navigation, params?.binId, optimizeImage]);

    const handleTurnOnOffLight = React.useCallback(() => {
        setIsTorchOn((prev) => !prev);
    }, []);

    if (!device || !hasPermission) {
        return <CameraEmptyPermission device={device} requestPermission={requestPermission} />;
    }

    return (
        <Container isLoading={isTakingPhoto}>
            <Box className="flex-1 bg-black">
                <Camera
                    ref={cameraRef}
                    style={StyleSheet.absoluteFill}
                    device={device}
                    isActive={isActive && !isTakingPhoto}
                    torch={isTorchOn ? "on" : "off"}
                    photo
                    video
                    audio={false}
                    enableZoomGesture={false}
                    photoQualityBalance="balanced"
                    onError={onError}
                />

                <Box className="absolute top-5 left-0 right-0 z-10">
                    <Header
                        title="Take Photo"
                        icBack={ImageAssets.icClose}
                        titleColor="white"
                        rightComponent={ImageAssets.icLight}
                        onPress={handleTurnOnOffLight}
                    />
                </Box>

                <TookPhotoWalkthrough
                    isVisible={isWalkthroughVisible}
                    onClose={handleCloseWalkthrough}
                    currentStep={3}
                    totalSteps={4}>
                    <TookPhotoFrameOverlay />
                </TookPhotoWalkthrough>

                <Box className="absolute bottom-10 left-0 right-0 items-center justify-center z-10">
                    <MyTouchable disabled={isTakingPhoto} onPress={takePhoto}>
                        <Box className="w-[64px] h-[64px] rounded-full bg-white items-center justify-center">
                            <Box className="w-[56px] h-[56px] rounded-full border-2 border-gray-300" />
                        </Box>
                    </MyTouchable>
                </Box>
            </Box>
        </Container>
    );
};

export default TookPhotoScreen;
