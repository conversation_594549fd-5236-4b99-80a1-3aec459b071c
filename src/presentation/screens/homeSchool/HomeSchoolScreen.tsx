import { Blur<PERSON>iew } from "expo-blur";
import { LinearGradient } from "expo-linear-gradient";
import React from "react";
import { ImageBackground, ScrollView, TouchableOpacity, View } from "react-native";

import ImageAssets from "../../../shared/constants/ImageAssets";
import { Box, Container, HStack, IconComponent, Image, Text } from "../../components/ui";

import { MyButton } from "@/presentation/components/myButton";

interface BinStatsData {
    id: string;
    type: "3in1" | "recycling" | "batteries-bulb" | "batteries";
    name: string;
    activeBins: number;
    co2Points: string;
    icon: number;
}

interface LeaderboardData {
    rank: number;
    username: string;
    co2Points: number;
    isTop3: boolean;
}

interface ActivityData {
    id: string;
    binType: "3in1" | "recycling" | "batteries-bulb" | "batteries";
    username: string;
    timestamp: string;
    reference: string;
    points: number;
    icon: number;
}

const HomeSchoolScreen: React.FC = () => {
    // Mock data - replace with actual data from your API
    const binStatsData: BinStatsData[] = [
        {
            id: "1",
            type: "3in1",
            name: "3-in-1 bin",
            activeBins: 5,
            co2Points: "10.01",
            icon: ImageAssets.ictBulbEwasteBins
        },
        {
            id: "2",
            type: "recycling",
            name: "Recycling bin",
            activeBins: 17,
            co2Points: "1001.0",
            icon: ImageAssets.recycleBin
        },
        {
            id: "3",
            type: "batteries-bulb",
            name: "Batteries & Bulbs",
            activeBins: 17,
            co2Points: "1001.01",
            icon: ImageAssets.bulbEwasteBins
        },
        {
            id: "4",
            type: "batteries",
            name: "Batteries",
            activeBins: 17,
            co2Points: "1001.01",
            icon: ImageAssets.batteriesEwasteBins
        }
    ];

    const leaderboardData: LeaderboardData[] = [
        { rank: 1, username: "copybarra", co2Points: 1050, isTop3: true },
        { rank: 2, username: "ctrlaltdeliciouys", co2Points: 687, isTop3: true },
        { rank: 3, username: "sauce123", co2Points: 123, isTop3: true },
        { rank: 4, username: "3656yahoo", co2Points: 87, isTop3: false },
        { rank: 5, username: "3656yahoo", co2Points: 87, isTop3: false }
    ];

    const activityData: ActivityData[] = [
        {
            id: "1",
            binType: "3in1",
            username: "copybarra737",
            timestamp: "17/01/25 | 3:08pm",
            reference: "#123884",
            points: 200,
            icon: ImageAssets.ictBulbEwasteBins
        },
        {
            id: "2",
            binType: "recycling",
            username: "copybarra737",
            timestamp: "17/01/25 | 3:08pm",
            reference: "#123884",
            points: 200,
            icon: ImageAssets.recycleBin
        },
        {
            id: "3",
            binType: "batteries",
            username: "copybarra737",
            timestamp: "17/01/25 | 3:08pm",
            reference: "#123884",
            points: 200,
            icon: ImageAssets.batteriesEwasteBins
        },
        {
            id: "4",
            binType: "batteries-bulb",
            username: "copybarra737",
            timestamp: "17/01/25 | 3:08pm",
            reference: "#123884",
            points: 200,
            icon: ImageAssets.bulbEwasteBins
        }
    ];

    const getBinTypeColor = (type: string) => {
        switch (type) {
            case "3in1":
                return "#C9E7FF";
            case "recycling":
                return "#E8F4FD";
            case "batteries-bulb":
                return "#F0F8FF";
            case "batteries":
                return "#F5F5F5";
            default:
                return "#C9E7FF";
        }
    };

    return (
        <Container>
            <ImageBackground source={ImageAssets.bgActivity} className="w-full h-full">
                {/* Main content */}
                <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
                    {/* Hero Statistics Section */}
                    <Box className="mx-4 mt-4 mb-6">
                        <Box className="rounded-xl overflow-hidden relative">
                            {/* Background content that will be blurred */}
                            <Box className="absolute inset-0 bg-gradient-to-t from-[#D6F4FF] to-[#FFFFFF] rounded-xl" />

                            {/* Blur overlay */}
                            <BlurView intensity={60} tint="light" className="absolute inset-0 rounded-xl" />

                            {/* Content on top */}
                            <Box className="relative z-10 p-4">
                                <Text className="text-white font-bold text-base mb-3">Recycling Activity</Text>

                                <Box className="flex-row gap-2 mb-2">
                                    <Box className="flex-1 bg-white rounded-xl p-3 flex-row items-center gap-2">
                                        <Box className="w-6 h-6 bg-[#ECF6EA] rounded-xl justify-center items-center">
                                            <Image
                                                source={ImageAssets.icDailyPoint}
                                                className="w-6 h-6"
                                                alt="Daily Point"
                                            />
                                        </Box>
                                        <Box className="flex-1">
                                            <Text className="font-bold text-xl">25.6</Text>
                                            <Text className="text-[#A0AEC0] font-semibold text-xs">
                                                Daily CO2 Points
                                            </Text>
                                        </Box>
                                    </Box>

                                    <Box className="flex-1 bg-white rounded-xl p-3 flex-row items-center gap-2">
                                        <Box className="w-6 h-6 bg-[#D6F4FF] rounded-xl justify-center items-center">
                                            <Image
                                                source={ImageAssets.icWeeklyPoint}
                                                className="w-6 h-6"
                                                alt="Weekly Point"
                                            />
                                        </Box>
                                        <Box className="flex-1">
                                            <Text className="font-bold text-xl">735.67</Text>
                                            <Text className="text-[#A0AEC0] font-semibold text-xs">
                                                Weekly CO2 Points
                                            </Text>
                                        </Box>
                                    </Box>
                                </Box>

                                <Box className="flex-row gap-2">
                                    <Box className="flex-1 bg-white rounded-xl p-3 flex-row items-center gap-2">
                                        <Box className="w-6 h-6 bg-[#EDEDED] rounded-xl justify-center items-center">
                                            <Image
                                                source={ImageAssets.icActiveUser}
                                                className="w-6 h-6"
                                                alt="Active User"
                                            />
                                        </Box>
                                        <Box className="flex-1">
                                            <Text className="font-bold text-xl">1.457</Text>
                                            <Text className="text-[#A0AEC0] font-semibold text-xs">Active Users</Text>
                                        </Box>
                                    </Box>

                                    <Box className="flex-1 bg-white rounded-xl p-3 flex-row items-center gap-2">
                                        <Box className="w-6 h-6 bg-[#FFF2BC] rounded-xl justify-center items-center">
                                            <Image
                                                source={ImageAssets.icTotalPoint}
                                                className="w-6 h-6"
                                                alt="Total Point"
                                            />
                                        </Box>
                                        <Box className="flex-1">
                                            <Text className="font-bold text-xl">2,457</Text>
                                            <Text className="text-[#A0AEC0] font-semibold text-xs">
                                                Total CO2 Points
                                            </Text>
                                        </Box>
                                    </Box>
                                </Box>
                            </Box>
                        </Box>
                    </Box>

                    {/* Content Container */}
                    <Box className=" bg-white rounded-t-2xl pt-6 pb-10">
                        {/* Bin Activity Section */}
                        <Box className="mb-6 mx-4">
                            <Text className="text-darkBlue font-bold text-base mb-4">Bin Activity</Text>

                            <Box className="gap-4">
                                {binStatsData.map((bin) => (
                                    <TouchableOpacity key={bin.id} activeOpacity={0.8}>
                                        <Box className="bg-[#FBFBFB] rounded-xl p-3 flex-row items-center gap-3">
                                            <Box
                                                className="w-14 h-14 rounded-lg justify-center items-center border-2 border-white shadow-md"
                                                style={{ backgroundColor: getBinTypeColor(bin.type) }}>
                                                <Image source={bin.icon} className="w-8 h-8" alt={bin.name} />
                                            </Box>

                                            <Box className="flex-1">
                                                <Text className="text-[#183362] font-medium text-base">{bin.name}</Text>
                                                <Box className="flex-row items-center gap-1 mt-1">
                                                    <Box className="w-2 h-2 bg-[#44A12B] rounded-full" />
                                                    <Text className="text-[#4A5568] font-semibold text-sm">
                                                        {bin.activeBins} active bin(s)
                                                    </Text>
                                                </Box>
                                            </Box>

                                            <Box className="items-end">
                                                <Text className="text-[#009DD3] font-bold text-xl">
                                                    {bin.co2Points}
                                                </Text>
                                                <Text className="text-[#A0AEC0] font-semibold text-sm">
                                                    Total CO2 points
                                                </Text>
                                            </Box>

                                            <IconComponent
                                                name="chevron-right"
                                                font="feather"
                                                size={20}
                                                color="#183362"
                                            />
                                        </Box>
                                    </TouchableOpacity>
                                ))}
                            </Box>

                            <MyButton text="See All" variant="secondary" className="mt-4" height={40} />
                        </Box>

                        {/* Leaderboard Section */}
                        <Box className="mx-4 mb-6">
                            {/* Gradient border container */}
                            <Box className="relative">
                                {/* Background gradient border */}
                                <View className="absolute inset-0 rounded-xl overflow-hidden">
                                    <LinearGradient
                                        colors={["#FFFFFF", "#009DD3", "#FFFFFF"]}
                                        start={{ x: 0, y: 0 }}
                                        end={{ x: 1, y: 0 }}
                                        style={{ width: "100%", height: "100%" }}
                                    />
                                </View>

                                {/* Content with margin to show border */}
                                <Box className="m-0.5 bg-white rounded-xl py-4">
                                    <Text className="text-darkGray font-bold text-base mb-4">Leaderboard</Text>

                                    {/* Podium */}
                                    <Box className="bg-[#C9E7FF] rounded-t-xl h-[280px] ">
                                        <Box className="relative justify-end items-end h-full pb-[40px]">
                                            <HStack className="items-center flex-1 h-full w-full">
                                                <Box className="flex-1 justify-center items-center">
                                                    <Box className="absolute bottom-[-70px] right-0 left-0 items-center">
                                                        <Box className="w-10 h-10 rounded-full justify-center items-center bg-white">
                                                            <Text className="text-center text-[#183362] font-semibold text-sm">
                                                                2
                                                            </Text>
                                                        </Box>
                                                        <Text className="text-center text-darkBlue font-semibold text-xs mt-1">
                                                            sauce123
                                                        </Text>
                                                    </Box>
                                                </Box>
                                                <Box className="flex-1 justify-center items-center">
                                                    <Box className="absolute bottom-[-50px] right-0 left-0 items-center">
                                                        <Box className="w-10 h-10 rounded-full justify-center items-center bg-white">
                                                            <Text className="text-center text-[#183362] font-semibold text-sm">
                                                                1
                                                            </Text>
                                                        </Box>
                                                        <Text className="text-center text-darkBlue font-semibold text-sm">
                                                            copybarra
                                                        </Text>
                                                    </Box>
                                                </Box>
                                                <Box className="flex-1 justify-center items-center ">
                                                    <Box className="absolute bottom-[-90px] right-0 left-0 items-center">
                                                        <Box className="w-10 h-10 rounded-full justify-center items-center bg-white">
                                                            <Text className="text-center text-[#183362] font-semibold text-sm">
                                                                3
                                                            </Text>
                                                        </Box>
                                                        <Text className="text-center text-darkBlue font-semibold text-xs mt-1">
                                                            ctrlaltdelicious
                                                        </Text>
                                                    </Box>
                                                </Box>
                                            </HStack>
                                            <Box className="absolute bottom-0 left-0 right-0">
                                                <Image
                                                    source={ImageAssets.ellipPodium}
                                                    className="w-full h-[100px]"
                                                    resizeMode="contain"
                                                    alt="Ellip Podium"
                                                />
                                            </Box>
                                            <Image
                                                source={ImageAssets.podium}
                                                className="w-full h-[120px]"
                                                resizeMode="contain"
                                                alt="Podium"
                                            />
                                        </Box>
                                    </Box>

                                    {/* Top Contributors */}
                                    <Box className="bg-background-light rounded-xl p-4 mt-[-20px]">
                                        <Text className="text-darkGray font-bold text-base mb-3">Top Contributors</Text>

                                        <Box className="gap-3">
                                            {leaderboardData.slice(0, 3).map((user) => (
                                                <Box
                                                    key={user.rank}
                                                    className="bg-greenCard rounded-xl p-2 flex-row items-center justify-between">
                                                    <Box className="flex-row items-center gap-2">
                                                        <Box>
                                                            <Image
                                                                source={ImageAssets.rank}
                                                                className="w-[18px] h-[18px]"
                                                                alt="rank"
                                                            />
                                                            <Box className="absolute top-0 left-0 right-0 bottom-0 rounded-full w-[18px] h-[18px] items-center justify-center">
                                                                <Text className="text-[13px] font-bold text-white">
                                                                    {user.rank}
                                                                </Text>
                                                            </Box>
                                                        </Box>
                                                        <Box className="w-8 h-8 bg-white rounded-full justify-center items-center">
                                                            <IconComponent
                                                                name="user"
                                                                font="feather"
                                                                size={12}
                                                                color="#009DD3"
                                                            />
                                                        </Box>
                                                        <Text className="text-[#183362] font-medium text-base">
                                                            {user.username}
                                                        </Text>
                                                    </Box>

                                                    <Box className="items-end">
                                                        <Text className="text-[#183362] font-bold text-base">
                                                            {user.co2Points}
                                                        </Text>
                                                        <Text className="text-[#183362] font-semibold text-sm">
                                                            CO2 pts
                                                        </Text>
                                                    </Box>
                                                </Box>
                                            ))}
                                        </Box>

                                        <MyButton text="Load More" variant="secondary" className="mt-4" height={40} />
                                    </Box>
                                </Box>
                            </Box>
                        </Box>

                        {/* Recycling Activity Section */}
                        <Box className="mx-4 mb-6">
                            <Text className="text-darkBlue font-bold text-base mb-4">Recycling Activity</Text>

                            <Box className="gap-2">
                                {activityData.map((activity) => (
                                    <Box
                                        key={activity.id}
                                        className="bg-[#FBFBFB] rounded-lg p-2 flex-row items-center justify-between">
                                        <Box className="flex-row items-center gap-2">
                                            <Box
                                                className="w-10 h-10 rounded-lg justify-center items-center border border-white shadow-sm"
                                                style={{ backgroundColor: getBinTypeColor(activity.binType) }}>
                                                <Image
                                                    source={activity.icon}
                                                    className="w-6 h-6"
                                                    alt={activity.binType}
                                                />
                                            </Box>

                                            <Box>
                                                <Text className="text-[#183362] font-medium text-base">
                                                    {activity.username}
                                                </Text>
                                                <Text className="text-[#A0AEC0] font-semibold text-sm">
                                                    {activity.timestamp} | {activity.reference}
                                                </Text>
                                            </Box>
                                        </Box>

                                        <Text className="text-[#183362] font-bold text-base">
                                            {activity.points} points
                                        </Text>
                                    </Box>
                                ))}
                            </Box>

                            <MyButton text="See All" variant="secondary" className="mt-4" height={40} />
                        </Box>
                    </Box>
                </ScrollView>
            </ImageBackground>
        </Container>
    );
};

export default HomeSchoolScreen;
