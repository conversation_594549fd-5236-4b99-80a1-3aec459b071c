import React from "react";

import { useDistrictStore } from "@/app/store";

import { EmptyState } from "@/presentation/components/emptyState";
import { Header } from "@/presentation/components/header";
import { ItemSeparator } from "@/presentation/components/item";
import { ListView } from "@/presentation/components/listView";
import { AreaItemSkeleton } from "@/presentation/components/skeleton";
import { MyTouchable } from "@/presentation/components/touchable";
import { Container, HStack, Image, Text, VStack } from "@/presentation/components/ui";
import { useSearchDistrict } from "@/presentation/hooks/district";
import { useGuest } from "@/presentation/hooks/user";
import { ImageAssets } from "@/shared/constants";

const AreaClickScreen = () => {
    const { districts, isLoading, search } = useSearchDistrict();
    const { setDistrictPicked, districtPicked, clearDistrictPicked } = useDistrictStore();
    const { isGuestMode } = useGuest();

    const handleAreaSelect = React.useCallback(
        (area: DistrictResponse) => {
            if (districtPicked?.id === area.id) {
                clearDistrictPicked();
            } else {
                setDistrictPicked(area);
            }
        },
        [setDistrictPicked, clearDistrictPicked, districtPicked]
    );

    const renderRadioButton = React.useCallback((isSelected: boolean) => {
        const image = isSelected ? ImageAssets.radioEnable : ImageAssets.radioDisable;
        return <Image source={image} className="w-5 h-5" alt="radio" />;
    }, []);

    const renderAreaItem = React.useCallback(
        ({ item }: { item: { id: number; name: string; region: string } }) => {
            const isSelected = districtPicked?.id === item.id;
            return (
                <MyTouchable onPress={() => handleAreaSelect(item)} className="py-4 px-2">
                    <HStack className="items-center gap-2">
                        {renderRadioButton(isSelected)}
                        <Text className="text-blackLight font-medium text-base">{item.name}</Text>
                    </HStack>
                </MyTouchable>
            );
        },
        [districtPicked, handleAreaSelect, renderRadioButton]
    );

    const handleSearch = React.useCallback(
        (text: string) => {
            search(text);
        },
        [search]
    );

    const renderEmptyState = React.useMemo(() => {
        return <EmptyState title="No Location Found" description="Let’s try another location" />;
    }, []);

    return (
        <Container>
            <Header
                type="searchArea"
                setSearchQuery={handleSearch}
                placeholder="Search your location"
                editable={!isGuestMode()}
            />

            <VStack className="flex-1 px-4 gap-2">
                {districts && districts.length > 0 && (
                    <Text className="text-gray2 font-medium text-[16px]">
                        Below listed the area that covers e-drive feature
                    </Text>
                )}

                <ListView
                    data={districts}
                    renderItem={renderAreaItem}
                    keyList="id"
                    className="flex-1"
                    extraData={districtPicked}
                    ItemSeparatorComponent={ItemSeparator}
                    isLoading={isLoading}
                    skeletonComponent={AreaItemSkeleton}
                    skeletonCount={20}
                    emptyComponent={renderEmptyState}
                />
            </VStack>
        </Container>
    );
};

export default AreaClickScreen;
