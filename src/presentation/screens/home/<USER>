import { useNavigation } from "@react-navigation/native";
import React from "react";

import { useRecyclables } from "@/presentation/hooks";

import { Banner } from "@/presentation/components/advertisement";
import { Chip } from "@/presentation/components/chip";
import { Notch } from "@/presentation/components/notch";
import { Skeleton } from "@/presentation/components/skeleton";
import { Box, ScrollView, Text } from "@/presentation/components/ui";
import { useBanner } from "@/presentation/hooks/banner";
import { ImageAssets, RouteName } from "@/shared/constants";
import { IconName } from "@/shared/types/icon";

interface FilterChip {
    id: string;
    label: string;
    icon?: string | number;
    isSearch?: boolean;
    category?: string;
    onPress?: () => void;
    isActive?: boolean;
}

interface FilterProps {
    onPress?: (id: number) => void;
    activeFilterId?: number | null;
}

const Filter: React.FC<FilterProps> = ({ onPress, activeFilterId }) => {
    const { recyclables, isLoading } = useRecyclables();
    const navigation = useNavigation();
    const [isShowingMore, setIsShowingMore] = React.useState(false);
    const { getBannerHomePage, bannerHomePage } = useBanner();

    const handleSearchPress = React.useCallback(() => {
        navigation.navigate(RouteName.Search);
    }, [navigation]);

    const handleMorePress = React.useCallback(() => {
        setIsShowingMore((prev) => !prev);
    }, []);

    const handleChipPress = React.useCallback(
        (id: number) => {
            onPress?.(id);
        },
        [onPress]
    );

    React.useEffect(() => {
        getBannerHomePage();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const filterChips = React.useMemo(() => {
        const chips: FilterChip[] = [
            {
                id: "search",
                label: "Search",
                icon: ImageAssets.icSearch,
                isSearch: true,
                onPress: handleSearchPress
            }
        ];

        if (recyclables && recyclables.length > 0) {
            const itemsToShow = isShowingMore ? recyclables : recyclables.slice(0, 3);

            itemsToShow.forEach((item) => {
                chips.push({
                    id: String(item.id),
                    label: item.name,
                    category: item.name,
                    isActive: activeFilterId === item.id,
                    onPress: () => handleChipPress(item.id)
                });
            });
        }

        if (recyclables && recyclables.length > 3) {
            chips.push({
                id: "more",
                label: isShowingMore ? "Less" : "More",
                icon: isShowingMore ? "chevron-up" : ("more-horizontal" as IconName),
                onPress: handleMorePress
            });
        }

        return chips;
    }, [recyclables, isShowingMore, handleSearchPress, handleChipPress, handleMorePress, activeFilterId]);

    const renderChip = React.useCallback((chip: FilterChip) => {
        return (
            <Chip key={chip.id} icon={chip.icon} label={chip.label} onPress={chip.onPress} isSelected={chip.isActive} />
        );
    }, []);

    const renderSkeletonChips = React.useMemo(() => {
        const skeletonWidths = [60, 70, 90, 80, 100, 120];

        return (
            <>
                {Array.from({ length: 6 }).map((_, index) => (
                    <Box key={index} className="h-[36px] rounded-md border border-gray bg-white">
                        <Box className="flex-1 items-center justify-center">
                            <Skeleton width={skeletonWidths[index]} height={36} borderRadius={4} />
                        </Box>
                    </Box>
                ))}
            </>
        );
    }, []);

    return (
        <Box
            className="bg-white rounded-t-[16px] overflow-hidden relative z-20 shadow-lg"
            style={{ minHeight: bannerHomePage?.banner && bannerHomePage.banner?.status ? 190 : 120 }}>
            <Notch />

            <Box className="px-4 flex-1 pb-5">
                <Text className="text-base font-bold mb-3 text-textColor">
                    Ready to recycle? Let&apos;s locate the point
                </Text>

                <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    className="flex-row mb-4"
                    contentContainerStyle={{ gap: 8 }}>
                    {isLoading ? renderSkeletonChips : filterChips.map(renderChip)}
                </ScrollView>
                {bannerHomePage?.banner && bannerHomePage.banner?.status && <Banner banner={bannerHomePage} />}
            </Box>
        </Box>
    );
};

export default Filter;
