import { useNavigation } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import { debounce } from "radash";
import React from "react";
import { ActivityIndicator } from "react-native";

import { getColor, useBin, useRouteParams } from "@/presentation/hooks";

import Filter from "./Filter";

import { SYNC_STATE } from "@/data/services/binStorage";
import { ShowBottomSheetObs, ShowPopupRequiredObs, walkthroughService } from "@/data/services/observable";
import DataDownloadUI from "@/presentation/components/download/DataDownloadUI";
import { MapComponent, MapComponentRef, MapMarker } from "@/presentation/components/map";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, HStack, IconComponent, Image, Text } from "@/presentation/components/ui";
import { PointsWalkthrough } from "@/presentation/components/walkthrough";
import { usePoint } from "@/presentation/hooks/point";
import { useLocationServices } from "@/presentation/hooks/useLocationServices";
import { useGuest } from "@/presentation/hooks/user";
import { ImageAssets, RouteName, TypeBottomSheet } from "@/shared/constants";
import BinTypes from "@/shared/constants/BinTypes";

const HomeScreen = () => {
    const navigation = useNavigation();
    const params = useRouteParams<typeof RouteName.Home>();

    const [isPointsWalkthroughVisible, setIsPointsWalkthroughVisible] = React.useState(false);
    const [isMapInitialized, setIsMapInitialized] = React.useState(false);

    const { isLoadingLocation, userLocation, locationError } = useLocationServices();
    const mapRef = React.useRef<MapComponentRef>(null);

    const { binsToUse, getBinsByWasteTypeId, activeFilterId, storageStatus, isDownloadUIVisible, retryDownload } =
        useBin();
    const { point } = usePoint();
    const { isGuestMode } = useGuest();

    const markers = React.useMemo(() => {
        if (!binsToUse) return [];

        if (!Array.isArray(binsToUse)) {
            console.warn("binsToUse is not an array:", binsToUse);
            return [];
        }

        const filteredMarkers = binsToUse
            .map((bin): MapMarker | null => {
                try {
                    if (!bin || !bin.long || !bin.lat || !bin.id || !bin.type) {
                        console.warn("Invalid bin data:", bin);
                        return null;
                    }

                    const longitude = parseFloat(bin.long);
                    const latitude = parseFloat(bin.lat);

                    if (
                        isNaN(longitude) ||
                        isNaN(latitude) ||
                        !isFinite(longitude) ||
                        !isFinite(latitude) ||
                        longitude < -180 ||
                        longitude > 180 ||
                        latitude < -90 ||
                        latitude > 90
                    ) {
                        console.warn("Invalid coordinates for bin:", bin.id, longitude, latitude);
                        return null;
                    }

                    const category = BinTypes.determineBinCategory(bin.bin_type_id, bin.e_waste_bin_type_id);

                    const iconImage = { uri: bin.type.image_url };

                    const id = `bin_${bin.id}`;
                    const title = `${bin.type.name || "Unknown"} - ${bin.code || "No Code"}`;
                    const description = bin.address || "No Address";

                    if (!id || !title) {
                        console.warn("Missing required fields for bin:", bin.id);
                        return null;
                    }

                    return {
                        id,
                        coordinate: [longitude, latitude],
                        title,
                        description,
                        category,
                        iconImage
                    };
                } catch (error) {
                    console.error("DEBUG: Marker processing error:", error);
                    console.error("DEBUG: Failed bin data:", bin);
                    console.error("DEBUG: Error stack:", error.stack);
                    return null;
                }
            })
            .filter((m): m is MapMarker => m !== null);

        return filteredMarkers;
    }, [binsToUse]);

    const handleAccountPress = React.useCallback(() => {
        if (isGuestMode()) {
            ShowPopupRequiredObs.action();
            return;
        }
        navigation.navigate(RouteName.Account);
    }, [navigation, isGuestMode]);

    const handleShowWalkthrough = React.useCallback(() => {
        walkthroughService.showWalkthrough(true);
    }, []);

    React.useEffect(() => {
        const initWalkthrough = async () => {
            if (params?.fromWalkthrough) {
                const timer = setTimeout(() => {
                    setIsPointsWalkthroughVisible(true);
                }, 500);
                return () => clearTimeout(timer);
            } else if (params?.fromWalkthroughBack) {
                const timer = setTimeout(() => {
                    walkthroughService.showWalkthrough(true);
                }, 500);
                return () => clearTimeout(timer);
            }
        };
        initWalkthrough();
    }, [params?.fromWalkthrough, params?.fromWalkthroughBack]);

    const handleMarkerPress = React.useCallback((_marker: MapMarker) => {
        const idString = _marker.id;
        const binIdMatch = idString.match(/bin_(\d+)/);
        if (binIdMatch && binIdMatch[1]) {
            const extractedBinId = Number(binIdMatch[1]);
            ShowBottomSheetObs.action({
                type: TypeBottomSheet.BIN_PRESS,
                binId: extractedBinId
            });
        }
    }, []);

    const isInitialDataLoading =
        storageStatus.isInitialLoad &&
        (storageStatus.state === SYNC_STATE.DOWNLOADING || storageStatus.state === SYNC_STATE.SYNCING);

    const canInteractWithMap = !isInitialDataLoading;

    React.useEffect(() => {
        if (canInteractWithMap && !isMapInitialized) {
            setIsMapInitialized(true);
        }
    }, [canInteractWithMap, isMapInitialized]);

    React.useEffect(() => {
        if (mapRef.current && canInteractWithMap && isMapInitialized) {
            const debouncedHandleMyLocationPress = debounce({ delay: 1000 }, () => {
                mapRef.current?.handleMyLocationPress();
            });
            debouncedHandleMyLocationPress();
            return () => debouncedHandleMyLocationPress.cancel();
        }
    }, [canInteractWithMap, isMapInitialized]);

    const MemoizedMap = React.useMemo(() => {
        return (
            <MapComponent
                markers={canInteractWithMap ? markers : []}
                onMarkerPress={handleMarkerPress}
                userLocation={userLocation}
                isLoadingLocation={isLoadingLocation}
                showDefaultLocationButton={false}
                ref={mapRef}
            />
        );
    }, [canInteractWithMap, markers, handleMarkerPress, userLocation, isLoadingLocation]);

    return (
        <>
            <Box className="z-10 bg-white pt-safe" />
            <Container safeArea={false}>
                <Box className="flex-1 items-center justify-center">
                    {MemoizedMap}
                    <DataDownloadUI status={storageStatus} isVisible={isDownloadUIVisible} onRetry={retryDownload} />

                    {/* <MapInfoButton /> */}

                    <Box className="absolute top-[10px] right-[10px] left-[10px] z-10">
                        <HStack className="items-center justify-between w-full">
                            <LinearGradient
                                colors={[getColor("blue"), getColor("green")]}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 0 }}
                                style={{ borderRadius: 999, padding: 2 }}>
                                <MyTouchable
                                    onPress={handleShowWalkthrough}
                                    className="px-3 py-1 flex-row items-center justify-center bg-transparent"
                                    style={{ borderRadius: 999 }}>
                                    <Image
                                        source={ImageAssets.icActivity}
                                        className="w-[24px] h-[24px]"
                                        alt="activity"
                                        tintColor="white"
                                    />
                                    <Text className="text-white font-bold ml-2">Recycling Tip</Text>
                                </MyTouchable>
                            </LinearGradient>
                            <HStack className="items-center gap-x-3">
                                <PointsWalkthrough
                                    isVisible={isPointsWalkthroughVisible}
                                    onClose={() => {
                                        setIsPointsWalkthroughVisible(false);
                                        walkthroughService.hideWalkthrough();
                                    }}
                                    currentStep={4}
                                    totalSteps={4}>
                                    <HStack
                                        alignItems="center"
                                        className="gap-x-2 bg-white rounded-full px-4 h-[36px] shadow-md"
                                        onPress={() => {
                                            navigation.navigate(RouteName.Rewards);
                                        }}>
                                        <Image source={ImageAssets.icPoint} className="w-[15px] h-[15px]" alt="Point" />
                                        <Text className="text-[13px]">{point}</Text>
                                    </HStack>
                                </PointsWalkthrough>
                                <MyTouchable
                                    onPress={handleAccountPress}
                                    className="bg-white rounded-full w-[36px] h-[36px] shadow-md">
                                    <Box className="flex-1 items-center justify-center">
                                        <IconComponent
                                            name="user"
                                            font="feather"
                                            size={20}
                                            color={getColor("darkGray")}
                                        />
                                    </Box>
                                </MyTouchable>
                            </HStack>
                        </HStack>
                    </Box>
                    {locationError && (
                        <Box className="absolute top-[100px] left-[10px] right-[10px] bg-error-100 border border-error-300 rounded-lg p-3 z-20">
                            <Text className="text-error-700 text-sm text-center">{locationError}</Text>
                        </Box>
                    )}
                </Box>
                <Box className="absolute bottom-0 left-0 right-0 z-10">
                    <MyTouchable
                        onPress={() => mapRef.current?.handleMyLocationPress()}
                        className="bg-white p-2 rounded-full shadow-md self-end mb-2 mr-2"
                        disabled={isLoadingLocation}>
                        {isLoadingLocation ? (
                            <ActivityIndicator size="small" color={getColor("green")} />
                        ) : (
                            <Image source={ImageAssets.icLocation} className="w-[16px] h-[16px]" alt="location" />
                        )}
                    </MyTouchable>
                    <Filter onPress={getBinsByWasteTypeId} activeFilterId={activeFilterId} />
                </Box>
            </Container>
        </>
    );
};

export default HomeScreen;
