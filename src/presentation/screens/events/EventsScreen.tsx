import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import React from "react";

import { useDistrictStore } from "@/app/store";

import AlbaEventScreen from "./AlbaEventScreen";
import CashForTrashList from "./CashForTrashList";
import EDrive from "./EDrive";
import JoinEventScreen from "./JoinEventScreen";

import { EventTypeSelector } from "@/presentation/components/eventTypeSelector";
import { Header } from "@/presentation/components/header";
import { MyTouchable } from "@/presentation/components/touchable";
import { Container, IconComponent, Image, Text } from "@/presentation/components/ui";
import { useEventType, useGetEventList } from "@/presentation/hooks/event";
import { EventTypes, ImageAssets, RouteName } from "@/shared/constants";
import { EventTypeK } from "@/shared/constants/EventTypes";

const EventsScreen = () => {
    const [selectedEventType, setSelectedEventType] = React.useState<EventTypeK>(EventTypes.ALBA_EVENT);
    const { eventTypes: rawEventTypes = [], isLoading } = useEventType();

    const eventTypes = React.useMemo(() => {
        if (rawEventTypes.length === 0) return [];

        const albaEvent = rawEventTypes.find((event) => event.id === EventTypes.ALBA_EVENT);
        const otherEvents = rawEventTypes.filter((event) => event.id !== EventTypes.ALBA_EVENT);

        return albaEvent ? [albaEvent, ...otherEvents] : otherEvents;
    }, [rawEventTypes]);

    const { districtPicked } = useDistrictStore();
    const { getEventList, eventList, isLoading: isLoadingEventList, refetch } = useGetEventList();

    const navigation = useNavigation();

    useFocusEffect(
        React.useCallback(() => {
            if (selectedEventType === EventTypes.PRIVATE_EVENT) return;
            if (eventTypes.length > 0) {
                getEventList(selectedEventType, districtPicked?.id);
            }
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [districtPicked?.id, eventTypes.length, selectedEventType])
    );

    const onRefresh = React.useCallback(() => {
        if (selectedEventType === EventTypes.PRIVATE_EVENT) return;
        if (eventTypes.length > 0) {
            refetch(selectedEventType);
        }
    }, [eventTypes.length, refetch, selectedEventType]);

    const isLoadingEvents = React.useMemo(() => {
        return isLoading || isLoadingEventList;
    }, [isLoading, isLoadingEventList]);

    const handleEventTypeChange = React.useCallback((type: { id: EventTypeK; name: string }) => {
        setSelectedEventType(type.id);
    }, []);

    const getEventIcon = React.useCallback((eventTypeId: number) => {
        switch (eventTypeId) {
            case EventTypes.E_DRIVE:
                return ImageAssets.icCar;
            case EventTypes.CASH_FOR_TRASH:
                return ImageAssets.icDollaSign;
            case EventTypes.PRIVATE_EVENT:
                return ImageAssets.icSparkles;
            case EventTypes.ALBA_EVENT:
                return ImageAssets.calendarEvent;
            default:
                return ImageAssets.icCar;
        }
    }, []);

    const handleAreaClick = React.useCallback(() => {
        navigation.navigate(RouteName.AreaClick);
    }, [navigation]);

    const renderLocationFilter = React.useMemo(() => {
        return (
            <MyTouchable className="absolute bottom-[25px] self-center shadow-md" onPress={handleAreaClick}>
                <LinearGradient
                    colors={["#44A12B", "#009DD3"]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={{
                        flexDirection: "row",
                        alignItems: "center",
                        padding: 8,
                        borderRadius: 20,
                        gap: 3
                    }}>
                    <Image source={ImageAssets.icMapPin} className="w-[16px] h-[16px]" alt="Map Pin" />
                    <Text className="text-white font-semibold ml-1">{districtPicked?.name}</Text>
                    <IconComponent name="chevron-down" font="feather" size={16} color="#FFFFFF" />
                </LinearGradient>
            </MyTouchable>
        );
    }, [handleAreaClick, districtPicked]);

    const handleGetEventDetail = React.useCallback((eventId: number) => {
        navigation.navigate(RouteName.EventDetail, { eventId });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const renderEventContent = React.useMemo(() => {
        switch (selectedEventType) {
            case EventTypes.CASH_FOR_TRASH:
                return (
                    <CashForTrashList
                        eventList={eventList}
                        isLoading={isLoadingEvents}
                        getEventDetail={handleGetEventDetail}
                        onRefresh={onRefresh}
                    />
                );
            case EventTypes.PRIVATE_EVENT:
                return <JoinEventScreen />;
            case EventTypes.ALBA_EVENT:
                return <AlbaEventScreen eventList={eventList} isLoading={isLoadingEvents} onRefresh={onRefresh} />;
            default:
                return (
                    <EDrive
                        eventList={eventList}
                        isLoading={isLoadingEvents}
                        getEventDetail={handleGetEventDetail}
                        onRefresh={onRefresh}
                    />
                );
        }
    }, [selectedEventType, eventList, isLoadingEvents, handleGetEventDetail, onRefresh]);

    return (
        <Container>
            <Header title="Events" isShowBack={false} />

            <EventTypeSelector
                selectedEventType={selectedEventType}
                eventTypes={eventTypes as Array<{ id: EventTypeK; name: string }>}
                isLoading={isLoading}
                onEventTypeChange={handleEventTypeChange}
                getEventIcon={getEventIcon}
            />

            {renderEventContent}
            {selectedEventType !== EventTypes.PRIVATE_EVENT && renderLocationFilter}
        </Container>
    );
};

export default EventsScreen;
