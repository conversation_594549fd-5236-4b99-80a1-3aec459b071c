import React from "react";

import { CashForTrashItem } from "@/presentation/components/item";
import { ListView } from "@/presentation/components/listView";
import { EventItemSkeleton } from "@/presentation/components/skeleton";
import { Box, Text } from "@/presentation/components/ui";
import { extractLocationName, GroupedItem, groupItems } from "@/shared/helper";

type CashForTrashListProps = {
    eventList?: EventResponse[];
    isLoading?: boolean;
    getEventDetail: (eventId: number) => void;
    onRefresh: () => void;
};

const CashForTrashList: React.FC<CashForTrashListProps> = ({ eventList, isLoading, getEventDetail, onRefresh }) => {
    const [cashForTrashLocations, setCashForTrashLocations] = React.useState<GroupedItem<EventResponse>[]>([]);

    const groupedLocations = React.useMemo(() => {
        return groupItems(eventList, (event) => extractLocationName(event.district_name), 0);
    }, [eventList]);

    React.useEffect(() => {
        if (isLoading) return;
        setCashForTrashLocations(groupedLocations);
    }, [groupedLocations, isLoading]);

    const renderCashForTrashItem = React.useCallback(
        ({ item }: { item: GroupedItem<EventResponse> }) => {
            return (
                <CashForTrashItem
                    item={item}
                    setCashForTrashLocations={setCashForTrashLocations}
                    getEventDetail={getEventDetail}
                />
            );
        },
        [getEventDetail]
    );

    return (
        <Box className="flex-1 px-4">
            <Text className="text-gray-600 font-medium mb-3">Trash Out. Cash in. Find one near you!</Text>
            <ListView
                data={cashForTrashLocations}
                renderItem={renderCashForTrashItem}
                keyList="id"
                estimatedItemSize={300}
                isLoading={isLoading}
                skeletonComponent={EventItemSkeleton}
                onPullToRefresh={onRefresh}
            />
        </Box>
    );
};

export default CashForTrashList;
