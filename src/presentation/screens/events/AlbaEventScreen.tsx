import React from "react";

import { ShowPopupRequiredObs } from "@/data/services/observable";
import { AlbaEventItem, ItemSeparator } from "@/presentation/components/item";
import { ListView } from "@/presentation/components/listView";
import { AlbaEventItemSkeleton } from "@/presentation/components/skeleton";
import { Container, Text } from "@/presentation/components/ui";
import { useJoinEvent } from "@/presentation/hooks/event";
import { useGuest } from "@/presentation/hooks/user";

type AlbaEventProps = {
    eventList?: EventResponse[];
    isLoading?: boolean;
    onRefresh: () => void;
};

const AlbaEventScreen = ({ eventList, isLoading, onRefresh }: AlbaEventProps) => {
    const { joinEvent, isLoading: isLoadingJointEvent } = useJoinEvent();
    const [loadingEventId, setLoadingEventId] = React.useState<number | null>(null);
    const { isGuestMode } = useGuest();

    const handleJoinEvent = React.useCallback(
        async (id: number) => {
            if (isGuestMode()) {
                return ShowPopupRequiredObs.action();
            }
            setLoadingEventId(id);
            try {
                await joinEvent({ code: "", eventId: id });
            } finally {
                setLoadingEventId(null);
            }
        },
        [joinEvent, isGuestMode]
    );

    const renderEventItem = React.useCallback(
        ({ item }: { item: EventResponse }) => {
            const isThisItemLoading = loadingEventId === item.id;
            const isAnyItemLoading = loadingEventId !== null;
            return (
                <AlbaEventItem
                    item={item}
                    handleJoin={handleJoinEvent}
                    isLoading={isThisItemLoading}
                    isAnyItemLoading={isAnyItemLoading}
                />
            );
        },
        [handleJoinEvent, loadingEventId]
    );

    return (
        <Container safeArea={false} className="px-4">
            <Text className="text-gray-600 font-medium mb-3">You can only be part of one event at a time</Text>

            <ListView
                data={eventList}
                renderItem={renderEventItem}
                keyList="id"
                estimatedItemSize={100}
                ItemSeparatorComponent={ItemSeparator}
                isLoading={isLoading}
                skeletonComponent={AlbaEventItemSkeleton}
                extraData={isLoadingJointEvent}
                onPullToRefresh={onRefresh}
            />
        </Container>
    );
};

export default AlbaEventScreen;
