import React from "react";

import { EventItem, ItemSeparator } from "@/presentation/components/item";
import { ListView } from "@/presentation/components/listView";
import { EventItemSkeleton } from "@/presentation/components/skeleton";
import { Box, Text } from "@/presentation/components/ui";

type EDriveProps = {
    eventList?: EventResponse[];
    isLoading?: boolean;
    getEventDetail: (eventId: number) => void;
    onRefresh: () => void;
};

const EDrive: React.FC<EDriveProps> = ({ eventList, isLoading, getEventDetail, onRefresh }) => {
    const handleGetEventDetail = React.useCallback((eventId: number) => getEventDetail(eventId), [getEventDetail]);

    const renderEventItem = React.useCallback(
        ({ item }: { item: EventResponse }) => {
            return <EventItem item={item} onPress={handleGetEventDetail} />;
        },
        [handleGetEventDetail]
    );

    return (
        <Box className="flex-1 px-4">
            <Text className="text-gray-600 font-medium mb-3">Find the latest e-drive</Text>
            <ListView
                data={eventList}
                renderItem={renderEventItem}
                keyList="id"
                estimatedItemSize={100}
                ItemSeparatorComponent={ItemSeparator}
                isLoading={isLoading}
                skeletonComponent={EventItemSkeleton}
                onPullToRefresh={onRefresh}
            />
        </Box>
    );
};

export default EDrive;
