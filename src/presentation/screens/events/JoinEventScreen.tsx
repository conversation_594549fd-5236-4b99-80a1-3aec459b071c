import React from "react";
import { Keyboard } from "react-native";

import { useForm } from "@/presentation/hooks";

import { Input } from "@/presentation/components/input";
import { MyButton } from "@/presentation/components/myButton";
import { Box, Container, Image, ScrollView, Text, VStack } from "@/presentation/components/ui";
import { useJoinEvent } from "@/presentation/hooks/event";
import { ImageAssets } from "@/shared/constants";

const JoinEventScreen = () => {
    const { joinEvent, isLoading } = useJoinEvent();

    const { getInputProps, handleSubmit } = useForm({
        initialValues: {
            eventCode: ""
        },
        onSubmit: async (values) => {
            await joinEvent({ code: values.eventCode });
            Keyboard.dismiss();
        }
    });

    return (
        <Container safeArea={false} className="px-4">
            <ScrollView>
                <VStack className="gap-5  pt-10">
                    <Text className="text-[16px] text-center font-[700]">Let’s join the special event together!</Text>

                    <Text className="text-center text-darkGray text-[16px]">
                        Have a code? Enter it below to join your event. It only takes a moment to get started.
                    </Text>

                    <Image
                        source={ImageAssets.icJoinEvent}
                        className="w-full h-[150px]"
                        alt="Join Event"
                        resizeMode="contain"
                    />

                    <VStack className="items-center gap-y-5">
                        <Text className="text-center text-xl font-medium text-[15px]">Enter Event Code</Text>

                        <Box className="w-2/3">
                            <Input
                                {...getInputProps("eventCode")}
                                placeholder=""
                                className="bg-gray-100 rounded-lg p-4 text-lg"
                                autoCapitalize="characters"
                                editable={!isLoading}
                            />
                        </Box>
                    </VStack>

                    <Text className="text-center text-blue text-[13px]">
                        Heads up! You can only be part of one private event at a time.
                    </Text>

                    <Box className="flex-1 items-center justify-center">
                        <Box className="w-1/2">
                            <MyButton
                                text="Join Event"
                                onPress={handleSubmit}
                                variant="primary"
                                isLoading={isLoading}
                            />
                        </Box>
                    </Box>
                </VStack>
            </ScrollView>
        </Container>
    );
};

export default JoinEventScreen;
