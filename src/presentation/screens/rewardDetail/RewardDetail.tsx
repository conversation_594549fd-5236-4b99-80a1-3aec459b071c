import Clipboard from "@react-native-clipboard/clipboard";
import { useNavigation } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import React, { useState } from "react";
import { Alert, ColorValue } from "react-native";

import { useUserQueries } from "@/data/queries";

import { getColor, useRouteParams, useShowToast, useVerificationEmail } from "@/presentation/hooks";

import { GradientDivider } from "@/presentation/components/gradientDivider";
import { Header } from "@/presentation/components/header";
import { MyButton } from "@/presentation/components/myButton";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, HStack, IconComponent, Image, Text, VStack } from "@/presentation/components/ui";
import { ImageAssets, RouteName } from "@/shared/constants";
import { formatPoints } from "@/shared/helper";
import { formatDateTimeFull } from "@/shared/helper/dateFormat";

const RewardDetail = () => {
    const params = useRouteParams<typeof RouteName.RewardDetail>() || {};
    const showToast = useShowToast();
    const { isLoading: isLoadingUser, user } = useUserQueries();
    const { handleSendOtp, isLoadingOtp } = useVerificationEmail();

    const navigation = useNavigation();
    const [activeTab, setActiveTab] = useState<"details" | "terms">("details");

    let displayReward: RewardDetailResponse | MyRewardResponse["reward"] | undefined;
    let voucher: MyRewardResponse["voucher"] | undefined;

    if (params?.myVoucher) {
        const myReward = params.reward as MyRewardResponse;
        displayReward = myReward?.reward;
        voucher = myReward?.voucher;
    } else {
        displayReward = params.reward as RewardDetailResponse;
    }

    const enableRedeem = React.useMemo(() => {
        return displayReward?.remaining_vouchers && displayReward?.remaining_vouchers > 0;
    }, [displayReward?.remaining_vouchers]);

    const handleBackPress = React.useCallback(() => {
        navigation.goBack();
    }, [navigation]);

    const handleTabPress = React.useCallback(
        (tab: "details" | "terms") => {
            setActiveTab(tab);
        },
        [setActiveTab]
    );

    const handleRedeemPress = React.useCallback(() => {
        if (user?.email_verified_at) {
            navigation.navigate(RouteName.RedeemPoint, { reward: params?.reward as RewardDetailResponse });
        } else {
            Alert.alert("", "Please verify first", [{ text: "Cancle" }, { text: "OK", onPress: handleSendOtp }]);
            // navigation.navigate(RouteName.Auth, { screen: RouteName.VerificationEmail });
        }
    }, [navigation, params?.reward, user?.email_verified_at, handleSendOtp]);

    const handleCopyCode = React.useCallback(() => {
        if (voucher?.code) {
            Clipboard.setString(voucher.code);
            showToast?.showSuccess("Voucher code copied to clipboard!");
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [voucher?.code]);

    const renderTabButton = React.useCallback(
        (tab: "details" | "terms", label: string) => {
            const isActive = activeTab === tab;
            return (
                <MyTouchable
                    onPress={() => handleTabPress(tab)}
                    className={`px-4 py-2 rounded-full ${isActive ? "bg-lightBlue" : "border border-neutralGray"}`}>
                    <Text
                        className={`text-base ${isActive ? "text-darkBlue font-normal" : "text-darkBlue font-normal opacity-50"}`}>
                        {label}
                    </Text>
                </MyTouchable>
            );
        },
        [activeTab, handleTabPress]
    );

    const renderTabContent = React.useMemo(() => {
        return (
            <Text className="text-base text-darkGray leading-6">
                {activeTab === "details" ? displayReward?.description : displayReward?.tnc}
            </Text>
        );
    }, [activeTab, displayReward?.description, displayReward?.tnc]);

    const colors: readonly [ColorValue, ColorValue, ...ColorValue[]] = React.useMemo(() => {
        if (enableRedeem) {
            return [getColor("blue"), getColor("lightBlue")];
        }
        return [getColor("darkGray"), getColor("neutralGray")];
    }, [enableRedeem]);

    const renderCard = React.useMemo(() => {
        if (params?.myVoucher) {
            return (
                <Box className="bg-white rounded-lg p-3 mt-5 mx-4">
                    <Box className="bg-white rounded-lg p-3 gap-2">
                        <HStack className="items-center gap-2">
                            <Image
                                source={{ uri: displayReward?.image_url }}
                                className="w-14 h-14 rounded-lg"
                                alt={displayReward?.name}
                            />
                            <VStack className="flex-1">
                                <Text className="text-neutralGray text-[13px] font-semibold">REWARD</Text>
                                <Text className="text-darkBlue text-[19px] font-bold">{displayReward?.label}</Text>
                            </VStack>
                        </HStack>

                        <Box className="bg-background-light rounded-2xl p-2">
                            <HStack className="items-center justify-between">
                                <Box className="flex-1">
                                    <Text className="text-blue text-[19px] font-bold">{voucher?.code}</Text>
                                </Box>
                                <MyTouchable
                                    className="bg-lightBlue rounded-full px-4 py-2.5 flex-row items-center gap-1"
                                    onPress={handleCopyCode}>
                                    <IconComponent
                                        name="copy-outline"
                                        font="ionicons"
                                        size={20}
                                        color={getColor("darkBlue")}
                                    />
                                    <Text className="text-darkBlue text-[17px]">Copy</Text>
                                </MyTouchable>
                            </HStack>
                        </Box>

                        <Text className="text-darkBlue text-[13px] font-semibold">
                            Use before {formatDateTimeFull((displayReward as any)?.created_at)}
                        </Text>

                        <GradientDivider horizontal />

                        <Text className="text-neutralGray text-[10px] font-semibold">
                            Purchased on {formatDateTimeFull(voucher?.created_at)}
                        </Text>
                    </Box>
                </Box>
            );
        }

        return (
            <Box className="bg-white rounded-lg p-3 mt-5 mx-4">
                <HStack className="justify-between items-center">
                    <HStack className="items-center gap-2 flex-1">
                        <Box className="w-14 h-14 bg-gray-200 rounded-lg overflow-hidden">
                            <Image
                                source={{ uri: displayReward?.image_url }}
                                className="w-full h-full"
                                alt="Reward"
                                resizeMode="cover"
                            />
                        </Box>
                        <VStack className="flex-1">
                            <Text className="text-neutralGray text-[13px] font-semibold leading-5">
                                {displayReward?.status_text}
                            </Text>
                            <Text className="text-darkBlue text-[19px] font-bold leading-7">
                                {displayReward?.label}
                            </Text>
                        </VStack>
                    </HStack>

                    <GradientDivider />

                    <HStack className="items-center gap-2 bg-transparent rounded-full px-2 py-2">
                        <Box className="w-6 h-6">
                            <Image
                                source={enableRedeem ? ImageAssets.icPoint : ImageAssets.co2pDisabled}
                                className="w-[24px] h-[24px]"
                                alt="Point"
                            />
                        </Box>
                        <Text className="text-darkBlue text-[17px] font-bold">
                            {formatPoints(displayReward?.price)}
                        </Text>
                    </HStack>
                </HStack>
            </Box>
        );
    }, [enableRedeem, voucher?.code, voucher?.created_at, params?.myVoucher, displayReward, handleCopyCode]);

    const renderRedeemEmailText = React.useMemo(() => {
        return (
            <Text className="text-green text-[15px] font-semibold text-center">
                We&apos;ll email the voucher to your registered email
            </Text>
        );
    }, []);

    return (
        <Container isLoading={isLoadingUser || isLoadingOtp}>
            <Header title={params?.myVoucher ? "My Voucher" : "Rewards"} isShowBack={true} onPress={handleBackPress} />

            <LinearGradient colors={colors} start={{ x: 0, y: 0 }} end={{ x: 1, y: 0.73 }} style={{ flex: 1 }}>
                <VStack className="flex-1 gap-6">
                    {renderCard}

                    <Box className="bg-white rounded-t-2xl flex-1 p-4">
                        <VStack className="gap-3">
                            <Box className="self-center w-8 h-1 bg-neutralGray rounded-full" />

                            <HStack className="gap-3 py-2">
                                {renderTabButton("details", "Details")}
                                {renderTabButton("terms", "Term & Condition")}
                            </HStack>

                            <VStack className="gap-3">
                                <Text className="text-darkBlue text-base font-bold">{displayReward?.name}</Text>
                                {renderTabContent}
                            </VStack>
                            {params?.myVoucher && renderRedeemEmailText}
                        </VStack>

                        {!params?.myVoucher && (
                            <VStack className="gap-3 mt-auto pt-4">
                                <MyButton
                                    text="Proceed to redeem"
                                    onPress={handleRedeemPress}
                                    variant="primary"
                                    disabled={!enableRedeem}
                                    colorDisabled={getColor("gray2")}
                                />

                                {enableRedeem ? (
                                    renderRedeemEmailText
                                ) : (
                                    <Text className="text-red text-[15px] font-semibold text-center">
                                        All Monthly Rewards Claimed – You Crushed It!
                                    </Text>
                                )}
                            </VStack>
                        )}
                    </Box>
                </VStack>
            </LinearGradient>
        </Container>
    );
};

export default RewardDetail;
