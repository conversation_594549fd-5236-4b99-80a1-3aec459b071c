import React from "react";
import { ScrollView } from "react-native";

import { ImageAssets, RouteName } from "../../../shared/constants";
import { Header } from "../../components/header";
import { Box, Container, Image, Text, VStack } from "../../components/ui";
import { useRouteParams } from "../../hooks";

const AboutEventScreen = () => {
    const params = useRouteParams<typeof RouteName.AboutEvent>();

    return (
        <Container>
            <Header title="About event" isShowBack={true} />

            <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
                <VStack className="gap-4">
                    <Box className="w-full h-[130px] bg-gray-200">
                        <Image
                            source={params?.image ? { uri: params?.image } : ImageAssets.icJoinEvent}
                            className="w-full h-full"
                            alt="Event Image"
                            resizeMode="cover"
                        />
                    </Box>

                    <Box className="px-4">
                        <VStack className="gap-4">
                            <Text className="text-darkGray font-bold text-[16px]" numberOfLines={2}>
                                {params?.title || "Trash to Treasure 2025"}
                            </Text>

                            <Text className="text-darkGray text-[16px]">{params?.description}</Text>
                        </VStack>
                    </Box>
                </VStack>
            </ScrollView>
        </Container>
    );
};

export default AboutEventScreen;
