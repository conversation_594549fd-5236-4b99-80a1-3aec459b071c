import { useActionSheet } from "@expo/react-native-action-sheet";
import { Camera, PointAnnotation } from "@maplibre/maplibre-react-native";
import React from "react";
import { LayoutAnimation, UIManager } from "react-native";
import Animated, {
    Easing,
    Extrapolation,
    interpolate,
    runOnJS,
    useAnimatedStyle,
    useSharedValue,
    withSpring,
    withTiming
} from "react-native-reanimated";

import { useRouteParams } from "@/presentation/hooks";

import MyButton from "../../components/myButton/MyButton";
import { Box, Container, HStack, Image, Text, VStack } from "../../components/ui";
import IconComponent from "../../components/ui/icon";

import { MapBase } from "@/presentation/components/base";
import { Header } from "@/presentation/components/header";
import { Notch } from "@/presentation/components/notch";
import { MyTouchable } from "@/presentation/components/touchable";
import { useGetEventDetail } from "@/presentation/hooks/event";
import { useLocationServices } from "@/presentation/hooks/useLocationServices";
import { getColor } from "@/presentation/hooks/useThemeColor";
import { EventTypes, RouteName } from "@/shared/constants";
import ImageAssets from "@/shared/constants/ImageAssets";
import { formatDate, handleGetDirections, isAndroid } from "@/shared/helper";

if (isAndroid) {
    if (UIManager.setLayoutAnimationEnabledExperimental) {
        UIManager.setLayoutAnimationEnabledExperimental(true);
    }
}

const BOTTOM_SHEET_STYLES = {
    position: "absolute" as const,
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    backgroundColor: "white",
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 5
};

const ANIMATION_CONFIG = {
    duration: 400,
    springDamping: 0.7,
    easing: Easing.bezier(0.25, 0.1, 0.25, 1)
};

interface PriceListItemProps {
    item: {
        waste_type: { name: string };
        price: string;
    };
}

const PriceListItem: React.FC<PriceListItemProps> = ({ item }) => (
    <Box className="flex-row justify-between items-center bg-background-light rounded-xl py-2 px-4">
        <HStack className="items-center gap-2 flex-1">
            <Image source={ImageAssets.icActivity} alt="recycle" className="w-5 h-5" tintColor={getColor("grayText")} />
            <Text className="text-darkGray text-[15px] font-medium flex-1">{item.waste_type.name}</Text>
        </HStack>
        <Box className="items-end">
            <Text className="text-darkBlue text-[16px] font-bold">${item.price}</Text>
            <Text className="text-grayText text-[10px] font-semibold">/kg</Text>
        </Box>
    </Box>
);

interface AcceptedItemProps {
    item: { name: string };
}

const AcceptedItem: React.FC<AcceptedItemProps> = ({ item }) => (
    <Box className="flex-row items-center bg-background-light rounded-xl py-1 px-2">
        <Image source={ImageAssets.circleCheck} alt="check" className="w-4 h-4" />
        <Text className="text-darkGray text-[13px] ml-1 font-semibold">{item.name}</Text>
    </Box>
);

const EventDetailScreen = () => {
    const params = useRouteParams<typeof RouteName.EventDetail>();

    const { eventDetail, getEventDetail, isLoading } = useGetEventDetail();

    React.useEffect(() => {
        if (params?.eventId) {
            getEventDetail(params.eventId);
        }
    }, [params?.eventId, getEventDetail]);

    const { showActionSheetWithOptions } = useActionSheet();
    const { calculatedDistance } = useLocationServices();

    const [isAcceptedItemsExpanded, setIsAcceptedItemsExpanded] = React.useState(false);
    const [isBottomSheetReady, setIsBottomSheetReady] = React.useState(false);
    const [isMapReady, setIsMapReady] = React.useState(false);

    const rotateAnim = useSharedValue(0);
    const opacityAnim = useSharedValue(0);
    const scaleAnim = useSharedValue(0);
    const bottomSheetAnim = useSharedValue(400);

    const eventData = React.useMemo(
        () => ({
            title: eventDetail?.type.name,
            location: eventDetail?.district?.name,
            address: eventDetail?.address,
            date: new Date(eventDetail?.date_start || ""),
            startTime: eventDetail?.time_start_formatted,
            endTime: eventDetail?.time_end_formatted,
            distance: eventDetail?.distance,
            coordinate: [Number(eventDetail?.long), Number(eventDetail?.lat)] as [number, number]
        }),
        [eventDetail]
    );

    const acceptedItems = React.useMemo(
        () => eventDetail?.event_waste_type.map((item) => ({ name: item.waste_type.name })),
        [eventDetail]
    );

    const distance = calculatedDistance(eventDetail?.lat, eventDetail?.long);

    const displayDistance = distance || eventData.distance;

    const handleGetDirectionsPress = () => {
        handleGetDirections(
            {
                address: eventData.address,
                latitude: eventData.coordinate[1],
                longitude: eventData.coordinate[0]
            },
            { showActionSheetWithOptions }
        );
    };

    const setBottomSheetReadyJS = React.useCallback(() => {
        setIsBottomSheetReady(true);
    }, []);

    const handleMapReady = React.useCallback(() => {
        setTimeout(() => {
            setIsMapReady(true);
        }, 100);
    }, []);

    React.useEffect(() => {
        bottomSheetAnim.value = 400;

        const timer = setTimeout(() => {
            bottomSheetAnim.value = withSpring(
                0,
                {
                    damping: 15,
                    stiffness: 100
                },
                (finished) => {
                    if (finished) {
                        runOnJS(setBottomSheetReadyJS)();
                    }
                }
            );
        }, 300);

        return () => clearTimeout(timer);
    }, [bottomSheetAnim, setBottomSheetReadyJS]);

    React.useEffect(() => {
        const customLayoutAnimation = {
            duration: ANIMATION_CONFIG.duration,
            create: {
                type: LayoutAnimation.Types.spring,
                property: LayoutAnimation.Properties.scaleXY,
                springDamping: ANIMATION_CONFIG.springDamping
            },
            update: {
                type: LayoutAnimation.Types.spring,
                springDamping: ANIMATION_CONFIG.springDamping
            },
            delete: {
                type: LayoutAnimation.Types.spring,
                property: LayoutAnimation.Properties.scaleXY,
                springDamping: ANIMATION_CONFIG.springDamping
            }
        };

        rotateAnim.value = withTiming(isAcceptedItemsExpanded ? 1 : 0, {
            duration: ANIMATION_CONFIG.duration,
            easing: ANIMATION_CONFIG.easing
        });

        opacityAnim.value = withTiming(isAcceptedItemsExpanded ? 1 : 0, {
            duration: ANIMATION_CONFIG.duration,
            easing: ANIMATION_CONFIG.easing
        });

        scaleAnim.value = withSpring(isAcceptedItemsExpanded ? 1 : 0, {
            damping: 8,
            stiffness: 40
        });

        LayoutAnimation.configureNext(customLayoutAnimation);
    }, [isAcceptedItemsExpanded, rotateAnim, opacityAnim, scaleAnim]);

    const toggleAcceptedItems = () => {
        setIsAcceptedItemsExpanded((prev) => !prev);
    };

    const timeDisplay = `${eventData.startTime} - ${eventData.endTime}`;

    const rotateStyle = useAnimatedStyle(() => {
        return {
            transform: [{ rotate: `${interpolate(rotateAnim.value, [0, 1], [0, 180])}deg` }]
        };
    });

    const contentStyle = useAnimatedStyle(() => {
        return {
            opacity: opacityAnim.value,
            transform: [{ scale: interpolate(scaleAnim.value, [0, 1], [0.95, 1], Extrapolation.CLAMP) }],
            overflow: "hidden"
        };
    });

    const bottomSheetStyle = useAnimatedStyle(() => {
        const opacity = interpolate(bottomSheetAnim.value, [0, 400], [1, 0.3], Extrapolation.CLAMP);

        return {
            transform: [{ translateY: bottomSheetAnim.value }],
            opacity: isBottomSheetReady ? 1 : opacity
        };
    });

    const renderTextEvent = React.useMemo(() => {
        switch (eventDetail?.type.id) {
            case EventTypes.E_DRIVE:
                return "Accepted Items";
            case EventTypes.CASH_FOR_TRASH:
                return "Price List";
        }
    }, [eventDetail?.type.id]);

    // Render Functions
    const renderPriceList = React.useMemo(
        () => (
            <Box className="mt-2 gap-3">
                {eventDetail?.event_waste_type?.map((item, index) => <PriceListItem key={index} item={item} />)}
            </Box>
        ),
        [eventDetail?.event_waste_type]
    );

    const renderAcceptedItems = React.useMemo(
        () => (
            <Box className="flex-row flex-wrap mt-2 gap-2">
                {acceptedItems?.map((item, index) => <AcceptedItem key={index} item={item} />)}
            </Box>
        ),
        [acceptedItems]
    );

    const renderExpandableContent = React.useMemo(() => {
        if (!isAcceptedItemsExpanded) return null;

        return (
            <Animated.View style={contentStyle}>
                {eventDetail?.type.id === EventTypes.CASH_FOR_TRASH ? renderPriceList : renderAcceptedItems}
            </Animated.View>
        );
    }, [contentStyle, isAcceptedItemsExpanded, eventDetail?.type.id, renderAcceptedItems, renderPriceList]);

    const renderDateCard = React.useMemo(
        () => (
            <Box className="w-[60px] h-[76px] bg-background-light rounded-lg items-center justify-between p-1">
                <Box className="w-full items-center bg-white rounded-t-sm py-[2px]">
                    <Text className="text-gray-500 text-xs font-semibold">
                        {formatDate(eventData.date, "EEE").toUpperCase()}
                    </Text>
                </Box>
                <Text className="text-blue text-2xl font-bold">{formatDate(eventData.date, "dd")}</Text>
                <Text className="text-darkGray text-xs font-semibold">
                    {formatDate(eventData.date, "MMM").toUpperCase()}
                </Text>
            </Box>
        ),
        [eventData.date]
    );

    const renderEventInfo = React.useMemo(
        () => (
            <MyTouchable className="flex-row p-2 bg-white rounded-xl mb-2 border border-gray">
                <HStack className="gap-2 flex-1">
                    {renderDateCard}
                    <VStack className="flex-1">
                        <HStack className="items-center gap-1">
                            <IconComponent name="clock" font="feather" size={16} color={getColor("darkBlue")} />
                            <Text className="text-darkBlue ml-1">{timeDisplay}</Text>
                        </HStack>
                        <Box className="flex-1">
                            <Text className="text-grayText font-semibold mt-2 text-[15px]">{eventData.address}</Text>
                        </Box>
                    </VStack>
                </HStack>
            </MyTouchable>
        ),
        [renderDateCard, timeDisplay, eventData.address]
    );

    return (
        <Container isLoading={isLoading}>
            <Header
                title={eventData.title}
                // rightComponent={<IconComponent name="share" size={24} color={getColor("textColor")} font="feather" />}
            />
            <Box position="relative" height={570}>
                {eventData.coordinate && eventData.coordinate[0] && eventData.coordinate[1] ? (
                    <MapBase style={{ flex: 1 }} onDidFinishRenderingMapFully={handleMapReady}>
                        <Camera centerCoordinate={eventData.coordinate} zoomLevel={13} />
                        {isMapReady && (
                            <PointAnnotation id="event-marker" coordinate={eventData.coordinate}>
                                <Box className="w-[25px] h-[43px] items-center justify-center z-10">
                                    <Image source={ImageAssets.icMarker} alt="marker" className="w-full h-full" />
                                </Box>
                            </PointAnnotation>
                        )}
                    </MapBase>
                ) : (
                    <Box className="flex-1 bg-gray-100 items-center justify-center">
                        <Text className="text-gray-500">Loading map...</Text>
                    </Box>
                )}
            </Box>

            <Animated.View style={[BOTTOM_SHEET_STYLES, bottomSheetStyle]}>
                <Notch />

                <HStack alignItems="center" marginBottom={8} paddingVertical={12} paddingHorizontal={8}>
                    <IconComponent name="location-pin" size={24} color={getColor("darkGray")} font="entypo" />
                    <Text fontSize={16} fontWeight="700" color={getColor("darkGray")} marginLeft={8}>
                        {eventData.location}
                    </Text>
                </HStack>

                {renderEventInfo}

                <VStack marginBottom={8}>
                    <MyTouchable onPress={toggleAcceptedItems} activeOpacity={0.7}>
                        <HStack
                            justifyContent="space-between"
                            alignItems="center"
                            paddingVertical={12}
                            paddingHorizontal={8}>
                            <HStack alignItems="center">
                                <Image
                                    source={ImageAssets.icActivity}
                                    alt="activity"
                                    className="w-[24px] h-[24px]"
                                    tintColor={getColor("darkGray")}
                                />
                                <Text className="text-darkGray text-[16px] ml-2 font-bold">{renderTextEvent}</Text>
                            </HStack>
                            <Animated.View style={rotateStyle}>
                                <IconComponent name="chevron-down" size={20} color={getColor("blue")} font="feather" />
                            </Animated.View>
                        </HStack>
                    </MyTouchable>

                    {renderExpandableContent}
                </VStack>

                <HStack className="items-center w-full justify-center pb-4">
                    <Image
                        source={ImageAssets.tablerWalk}
                        className="w-[18px] h-[18px]"
                        alt="Walk"
                        tintColor={getColor("blue")}
                    />
                    <Text className="text-blue text-[16px] ml-2">
                        {displayDistance ? `${displayDistance} from you` : "Distance not available"}
                    </Text>
                </HStack>

                <MyButton
                    text="Get Directions"
                    onPress={handleGetDirectionsPress}
                    variant="primary"
                    height={56}
                    className="bg-darkBlue rounded-full"
                />
            </Animated.View>
        </Container>
    );
};

export default EventDetailScreen;
