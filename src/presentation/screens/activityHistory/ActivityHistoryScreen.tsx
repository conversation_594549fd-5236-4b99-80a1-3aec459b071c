import React from "react";

import { Header } from "@/presentation/components/header";
import { ActivityHistoryItem } from "@/presentation/components/item";
import { ActivityHistoryItemSkeleton, Skeleton } from "@/presentation/components/skeleton";
import { Box, Container, ScrollView, Text, VStack } from "@/presentation/components/ui";
import { useActivityHistory } from "@/presentation/hooks/activity";
import { formatDate } from "@/shared/helper/dateFormat";

const ActivityHistoryScreen = () => {
    const { recyclingHistory, isLoading } = useActivityHistory();

    const processedData = React.useMemo(() => {
        if (!recyclingHistory) return {};

        const processed: Record<string, EventLeaderboardHistoryItem[]> = {};

        Object.entries(recyclingHistory).forEach(([monthKey, activities]) => {
            const [year, month] = monthKey.split("-");
            const date = new Date(parseInt(year, 10), parseInt(month, 10) - 1);
            const formattedMonth = formatDate(date, "MMM yyyy").toUpperCase();

            const sortedActivities = activities.sort(
                (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
            );

            processed[formattedMonth] = sortedActivities;
        });

        const sortedEntries = Object.entries(processed).sort(([a], [b]) => {
            const dateA = new Date(a);
            const dateB = new Date(b);
            return dateB.getTime() - dateA.getTime();
        });

        return Object.fromEntries(sortedEntries);
    }, [recyclingHistory]);

    const renderActivityItem = React.useCallback((item: EventLeaderboardHistoryItem) => {
        return <ActivityHistoryItem key={item.id} item={item} />;
    }, []);

    const renderSkeletonItems = React.useMemo(() => {
        return (
            <VStack className="gap-4">
                <Box className="pt-4">
                    <Skeleton width={100} height={16} borderRadius={4} />
                </Box>
                {[1, 2, 3].map((index) => (
                    <ActivityHistoryItemSkeleton key={index} />
                ))}
            </VStack>
        );
    }, []);

    return (
        <Container>
            <Header title="Activity History" />
            <Box className="flex-1 px-4 ">
                <ScrollView>
                    {isLoading ? (
                        renderSkeletonItems
                    ) : (
                        <Box className="mb-10">
                            {Object.entries(processedData).map(([month, activities]) => (
                                <VStack key={month} className="gap-4">
                                    <Box className="pt-4">
                                        <Text className="text-neutralGray text-[16px] font-bold">{month}</Text>
                                    </Box>
                                    {activities.map(renderActivityItem)}
                                </VStack>
                            ))}
                        </Box>
                    )}
                </ScrollView>
            </Box>
        </Container>
    );
};

export default ActivityHistoryScreen;
