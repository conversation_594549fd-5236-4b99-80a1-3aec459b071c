import { useNavigation } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import React from "react";
import { ScrollView } from "react-native";

import { useRouteParams } from "@/presentation/hooks";

import { Box, Container, HStack, IconComponent, Image, Text, VStack } from "../../components/ui";

import { Header } from "@/presentation/components/header";
import { MyButton } from "@/presentation/components/myButton";
import { MyTouchable } from "@/presentation/components/touchable";
import { useEventLeaderboard } from "@/presentation/hooks/event";
import { useEventJoined } from "@/presentation/hooks/user";
import { ImageAssets, RouteName } from "@/shared/constants";
import { formatDate, formatPoints } from "@/shared/helper";

interface ContributorItemProps {
    rank: number;
    name: string;
    initials: string;
    totalPoints: number;
    isCurrentUser?: boolean;
}

const ContributorItem = ({ rank, name, initials, totalPoints, isCurrentUser = false }: ContributorItemProps) => (
    <Box
        className="flex-row justify-between items-center py-3 px-4"
        style={{
            backgroundColor: isCurrentUser ? "#ECF6EA" : "transparent",
            borderRadius: isCurrentUser ? 8 : 0
        }}>
        <HStack className="flex-1 items-center gap-2">
            <Box
                className={`bg-gray rounded-full w-[40px] h-[40px] items-center justify-center mr-3 ${
                    isCurrentUser ? "bg-green" : "bg-grayCard2"
                }`}>
                <Text className={`text-[13px] font-bold ${isCurrentUser ? "text-white" : ""}`}>{initials}</Text>

                {!isCurrentUser && (
                    <Box className="absolute top-[-5px] right-[-5px] ">
                        <Box>
                            <Image source={ImageAssets.rank} className="w-[18px] h-[18px]" alt="rank" />
                            <Box className="absolute top-0 left-0 right-0 bottom-0  rounded-full w-[18px] h-[18px] items-center justify-center">
                                <Text className="text-[13px] font-bold text-white">{rank}</Text>
                            </Box>
                        </Box>
                    </Box>
                )}
            </Box>
            <Text
                className={`text-[16px] flex-1 ${
                    isCurrentUser ? "font-bold" : "font-medium"
                } ${isCurrentUser ? "text-green" : "text-darkBlue"} line-clamp-2`}>
                {name}
            </Text>
        </HStack>

        <HStack className="items-center gap-2">
            <Text className={`text-[16px] font-bold ${isCurrentUser ? "text-green" : "text-darkBlue"}`}>
                {formatPoints(totalPoints)}
            </Text>
            <Text className={`text-[13px] font-semibold ${isCurrentUser ? "text-green" : "text-darkBlue"}`}>
                CO<Text className="text-[9px] align-bottom">2</Text> Points
            </Text>
        </HStack>
    </Box>
);

const SpecialEventScreen = () => {
    const params = useRouteParams<typeof RouteName.SpecialEvent>();
    const navigation = useNavigation();

    const { getEventsJoined, isLoading: isLoadingEventDetail, eventJoined } = useEventJoined();
    const { getEventLeaderboard, isLoading: isLoadingEventLeaderboard, eventLeaderboard } = useEventLeaderboard();

    React.useEffect(() => {
        if (params?.eventId) {
            getEventsJoined(params.eventId);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [params?.eventId]);

    React.useEffect(() => {
        if (params?.eventId) {
            getEventLeaderboard(params.eventId);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [params?.eventId]);

    const onStartRecycling = React.useCallback(() => {
        navigation.navigate(RouteName.ScanStack, {
            screen: RouteName.Scan
        });
    }, [navigation]);

    const handleAboutPress = React.useCallback(() => {
        navigation.navigate(RouteName.AboutEvent, {
            image: eventJoined?.image_url || undefined,
            title: eventJoined?.name || undefined,
            description: eventJoined?.description || undefined
        });
    }, [eventJoined?.description, eventJoined?.image_url, eventJoined?.name, navigation]);

    const isLoading = React.useMemo(() => {
        return isLoadingEventDetail || isLoadingEventLeaderboard;
    }, [isLoadingEventDetail, isLoadingEventLeaderboard]);

    const contributorsList = React.useMemo(() => {
        if (!eventLeaderboard) return [];

        const topContributors = eventLeaderboard.top_contributors.map((contributor) => ({
            ...contributor,
            isCurrentUser: false
        }));

        if (eventLeaderboard.my_rank_data && eventLeaderboard.leaderboard_position) {
            const currentUser = {
                rank: eventLeaderboard.leaderboard_position.my_rank,
                name: eventLeaderboard.my_rank_data.name,
                initials: eventLeaderboard.my_rank_data.initials,
                total_points: eventLeaderboard.my_rank_data.total_points,
                isCurrentUser: true
            };

            const isInTopContributors = topContributors.some((contributor) => contributor.rank === currentUser.rank);

            if (!isInTopContributors) {
                return [...topContributors, currentUser];
            }

            return topContributors.map((contributor) =>
                contributor.rank === currentUser.rank
                    ? { ...contributor, isCurrentUser: true, name: contributor.name + currentUser.name }
                    : contributor
            );
        }

        return topContributors;
    }, [eventLeaderboard]);

    return (
        <Container isLoading={isLoading}>
            <Header title="Your Special Event" isShowBack={true} />

            <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
                <LinearGradient
                    colors={["#183362", "#009DD3"]}
                    start={{ x: 0, y: 0.12 }}
                    end={{ x: 1, y: 1 }}
                    style={{ flex: 1 }}>
                    <VStack className="items-center px-4 pt-4 pb-10 gap-4">
                        <Box className="w-20 h-20 bg-gray-300 rounded-full mb-4" />

                        <VStack className="items-center gap-1">
                            <Text className="text-white text-4xl font-medium">
                                {formatPoints(eventLeaderboard?.my_impact?.total_points)} CO
                                <Text className="text-[9px] align-bottom">2</Text> Points
                            </Text>
                            <Text className="text-white text-sm font-semibold opacity-50">
                                Total CO2 emission saved on this event
                            </Text>
                        </VStack>

                        <Image
                            source={ImageAssets.lineWhite}
                            className="h-[2px] w-full"
                            resizeMode="cover"
                            alt="Line"
                            tintColor="white"
                        />

                        <Text className="text-white text-lg font-bold">{eventJoined?.name}</Text>

                        <Box className="w-full bg-white/10 rounded-xl p-3 border border-green">
                            <VStack className="gap-2">
                                <VStack className="gap-1">
                                    <Text className="text-white text-sm font-semibold">ENDED IN</Text>
                                    <HStack className="items-center gap-2">
                                        <Image
                                            source={ImageAssets.icCalendar}
                                            className="w-5 h-5 tint-white"
                                            alt="calendar"
                                            tintColor="white"
                                        />
                                        <Text className="text-white text-sm font-semibold">
                                            {eventJoined?.date_end && eventJoined?.time_end
                                                ? `${formatDate(eventJoined.date_end, "dd MMMM yyyy")} • ${eventJoined.time_end}:00`
                                                : formatDate(eventJoined?.date_end, "dd MMMM yyyy")}
                                        </Text>
                                    </HStack>
                                </VStack>

                                <VStack className="gap-1">
                                    <Text className="text-white text-sm font-semibold">LOCATION</Text>
                                    <HStack className="items-center gap-2">
                                        <Image
                                            source={ImageAssets.homeOff}
                                            className="w-5 h-5 tint-white"
                                            alt="location"
                                            tintColor="white"
                                        />
                                        <Text className="text-white text-sm font-semibold flex-1">
                                            {eventJoined?.address || "Singapore"}
                                        </Text>
                                    </HStack>
                                </VStack>

                                <VStack className="gap-1">
                                    <Text className="text-white text-sm font-semibold">ABOUT</Text>
                                    <MyTouchable onPress={handleAboutPress}>
                                        <HStack className="justify-between items-center">
                                            <HStack className="items-center flex-1 gap-2">
                                                <IconComponent name="info" font="feather" size={16} color="white" />
                                                <Text className="text-white text-sm font-semibold">
                                                    Let&apos;s know the details of it here
                                                </Text>
                                            </HStack>
                                            <IconComponent name="chevron-right" font="entypo" size={16} color="white" />
                                        </HStack>
                                    </MyTouchable>
                                </VStack>
                            </VStack>
                        </Box>
                    </VStack>
                </LinearGradient>

                <Box className="bg-white rounded-t-2xl -mt-4">
                    <VStack className="p-4 gap-4">
                        <VStack className="gap-2">
                            <Text className="text-grayText text-[13px] font-semibold">Your impact so far</Text>

                            <Box className="p-3 rounded-xl bg-greenCard">
                                <HStack className="items-center justify-between ">
                                    <Image source={ImageAssets.trophy} className="w-12 h-12 mr-4" alt="trophy" />
                                    <VStack className="flex-1 ml-2">
                                        <HStack className="items-end gap-2">
                                            <Text className="text-darkBlue text-[24px] font-bold">0</Text>
                                            <Text className="text-darkGray text-[13px] font-semibold">
                                                CO<Text className="text-[9px] align-bottom">2</Text> Points
                                            </Text>
                                        </HStack>
                                        <Text className="text-darkGray text-[13px] font-semibold">
                                            Your total CO2 emission saved
                                        </Text>
                                    </VStack>
                                </HStack>
                            </Box>
                        </VStack>

                        <VStack className="gap-2">
                            <Text className="text-grayText text-[13px] font-semibold">
                                Let&apos;s climb the leaderboard!
                            </Text>

                            <HStack className="items-center justify-between py-2">
                                <HStack className="items-center gap-2">
                                    <Image
                                        source={ImageAssets.crown}
                                        className="w-6 h-6 mr-2"
                                        alt="crown"
                                        tintColor="black"
                                    />
                                    <HStack className="flex-1 justify-between">
                                        <Text className="text-darkBlue text-sm font-semibold">
                                            #{eventLeaderboard?.leaderboard_position?.my_rank}
                                        </Text>
                                        <Text className="text-darkBlue text-sm font-semibold">
                                            of {eventLeaderboard?.leaderboard_position?.total_contributors} contributors
                                        </Text>
                                    </HStack>
                                </HStack>
                            </HStack>

                            <Box className="rounded-xl p-4 bg-background-light">
                                <Text className="text-darkGray text-[16px] font-bold mb-4">Top Contributors</Text>

                                <VStack className="gap-1">
                                    {contributorsList.map((contributor) => (
                                        <ContributorItem
                                            key={`${contributor.rank}-${contributor.isCurrentUser ? "current" : "top"}`}
                                            rank={contributor.rank || 0}
                                            name={contributor.name}
                                            initials={contributor.initials}
                                            totalPoints={contributor.total_points}
                                            isCurrentUser={contributor.isCurrentUser}
                                        />
                                    ))}
                                </VStack>
                            </Box>
                        </VStack>
                    </VStack>
                </Box>
            </ScrollView>
            <Box className="p-5">
                <MyButton text="Start Recycling" variant="outline" onPress={onStartRecycling} />
            </Box>
        </Container>
    );
};

export default SpecialEventScreen;
