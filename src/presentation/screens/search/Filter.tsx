import { useNavigation } from "@react-navigation/native";
import React from "react";

import { FilterItem, useFilterStore } from "@/app/store";

import { useRecyclables } from "@/presentation/hooks";

import { Header } from "@/presentation/components/header";
import { ListView } from "@/presentation/components/listView";
import { FilterItemSkeleton } from "@/presentation/components/skeleton";
import { MyTouchable } from "@/presentation/components/touchable";
import { Box, Container, HStack, Text } from "@/presentation/components/ui";

const Filter = () => {
    const { recyclables, isLoading } = useRecyclables();
    const navigation = useNavigation();

    const { toggleItem, resetFilter, checkedItems } = useFilterStore();

    const filterItems = React.useMemo(
        () =>
            !recyclables
                ? []
                : recyclables.map((item) => ({
                      ...item
                  })),
        [recyclables]
    );

    const handleResetFilter = React.useCallback(() => {
        resetFilter();
    }, [resetFilter]);

    const handleToggleItem = React.useCallback(
        (checkedItem: FilterItem) => {
            toggleItem(checkedItem);
        },
        [toggleItem]
    );

    const handleBack = React.useCallback(() => {
        navigation.goBack();
    }, [navigation]);

    const renderFilterItem = React.useCallback(
        ({ item }: { item: FilterItem }) => {
            const isChecked = checkedItems.some((checkedItem) => checkedItem.id === item.id);

            return (
                <HStack key={item.id} className="justify-between items-center py-1 px-4 mb-2">
                    <Box className="bg-[#F7F7F7] py-2 px-4 rounded-xl">
                        <Text className="text-[13px] font-semibold text-textColor">{item.name}</Text>
                    </Box>
                    <MyTouchable onPress={() => handleToggleItem(item)}>
                        <Box
                            className={`w-6 h-6 rounded border-2 items-center justify-center ${
                                isChecked ? "bg-[#44A12B] border-[#44A12B]" : "border-[#44A12B]"
                            }`}>
                            {isChecked && <Text className="text-white font-bold">✓</Text>}
                        </Box>
                    </MyTouchable>
                </HStack>
            );
        },
        [checkedItems, handleToggleItem]
    );

    return (
        <Container>
            <Header
                title="Filter"
                rightComponent={
                    <MyTouchable onPress={handleResetFilter}>
                        <Text className="text-darkBlue text-base">Reset filter</Text>
                    </MyTouchable>
                }
                onPress={handleBack}
            />
            <ListView
                data={filterItems}
                renderItem={renderFilterItem}
                keyList="id"
                className="flex-1"
                isLoading={isLoading}
                extraData={checkedItems}
                skeletonComponent={FilterItemSkeleton}
                skeletonCount={20}
            />
        </Container>
    );
};

export default Filter;
