import { useNavigation } from "@react-navigation/native";
import React from "react";

import { useFilterStore } from "@/app/store";

import { useSearchBin } from "@/presentation/hooks";

import { ShowBottomSheetObs } from "@/data/services/observable";
import { Banner } from "@/presentation/components/advertisement";
import { BottomSheetCustom } from "@/presentation/components/bottomSheet";
import { BottomSheetCustomRef } from "@/presentation/components/bottomSheet/BottomSheetCustom";
import { Header } from "@/presentation/components/header";
import { ItemSeparator, SearchBinItem } from "@/presentation/components/item";
import { ListView } from "@/presentation/components/listView";
import { LoadingAnimation } from "@/presentation/components/loading";
import { MapComponent, MapMarker } from "@/presentation/components/map";
import { Box, Container, Text, VStack } from "@/presentation/components/ui";
import { useBanner } from "@/presentation/hooks/banner";
import { RouteName, TypeBottomSheet } from "@/shared/constants";
import BinTypes from "@/shared/constants/BinTypes";

const SearchScreen = () => {
    const navigation = useNavigation();
    const [searchQuery, setSearchQuery] = React.useState("");
    const [hasSearched, setHasSearched] = React.useState(false);
    const [isScreenFocused] = React.useState(true);
    const [isMapInitialized, setIsMapInitialized] = React.useState(false);
    const bottomSheetRef = React.useRef<BottomSheetCustomRef>(null);

    const { checkedItems } = useFilterStore();
    const { bannerSearchPage, getBannerSearchPage } = useBanner();

    const { isLoading, binsByWasteTypeIdAndAcceptedRecyclables, getBinsByWasteTypeIdAndAcceptedRecyclables } =
        useSearchBin();

    React.useEffect(() => {
        getBannerSearchPage();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const markers = React.useMemo(() => {
        if (!binsByWasteTypeIdAndAcceptedRecyclables || !isScreenFocused) return [];

        if (!Array.isArray(binsByWasteTypeIdAndAcceptedRecyclables)) {
            console.warn(
                "binsByWasteTypeIdAndAcceptedRecyclables is not an array:",
                binsByWasteTypeIdAndAcceptedRecyclables
            );
            return [];
        }

        const filteredMarkers = binsByWasteTypeIdAndAcceptedRecyclables
            .map((bin): MapMarker | null => {
                try {
                    if (!bin || !bin.long || !bin.lat || !bin.id || !bin.type) {
                        console.warn("Invalid bin data:", bin);
                        return null;
                    }

                    const longitude = parseFloat(bin.long);
                    const latitude = parseFloat(bin.lat);

                    if (
                        isNaN(longitude) ||
                        isNaN(latitude) ||
                        !isFinite(longitude) ||
                        !isFinite(latitude) ||
                        longitude < -180 ||
                        longitude > 180 ||
                        latitude < -90 ||
                        latitude > 90
                    ) {
                        console.warn("Invalid coordinates for bin:", bin.id, longitude, latitude);
                        return null;
                    }

                    const category = BinTypes.determineBinCategory(bin.bin_type_id, bin.e_waste_bin_type_id);

                    const iconImage = { uri: bin.type.image_url };

                    const id = `bin_${bin.id}`;
                    const title = `${bin.type.name || "Unknown"} - ${bin.code || "No Code"}`;
                    const description = bin.address || "No Address";

                    if (!id || !title) {
                        console.warn("Missing required fields for bin:", bin.id);
                        return null;
                    }

                    return {
                        id,
                        coordinate: [longitude, latitude],
                        title,
                        description,
                        category,
                        iconImage
                    };
                } catch (error) {
                    console.error("DEBUG: Marker processing error:", error);
                    console.error("DEBUG: Failed bin data:", bin);
                    console.error("DEBUG: Error stack:", error.stack);
                    return null;
                }
            })
            .filter((m): m is MapMarker => m !== null);

        return filteredMarkers;
    }, [binsByWasteTypeIdAndAcceptedRecyclables, isScreenFocused]);

    const handleFilterPress = React.useCallback(() => {
        navigation.navigate(RouteName.Filter);
    }, [navigation]);

    const handleNavigatePress = React.useCallback((result: BinResponse) => {
        ShowBottomSheetObs.action({
            type: TypeBottomSheet.BIN_PRESS,
            binId: result.id
        });
    }, []);

    const handleSearch = React.useCallback(() => {
        if (searchQuery.trim().length > 0) {
            setHasSearched(true);
            getBinsByWasteTypeIdAndAcceptedRecyclables(
                checkedItems.map((item) => item.id),
                searchQuery
            );
            bottomSheetRef.current?.open();
        }
    }, [checkedItems, getBinsByWasteTypeIdAndAcceptedRecyclables, searchQuery]);

    const handleSearchSubmit = React.useCallback(() => {
        handleSearch();
    }, [handleSearch]);

    const handleSearchChange = React.useCallback((text: string) => {
        setSearchQuery(text);
        if (text.trim().length === 0) {
            setHasSearched(false);
        }
    }, []);

    const renderItem = React.useCallback(
        ({ item }: { item: BinResponse }) => {
            return <SearchBinItem item={item} handleNavigatePress={handleNavigatePress} />;
        },
        [handleNavigatePress]
    );

    const handleMarkerPress = React.useCallback((_marker: MapMarker) => {
        const idString = _marker.id;
        const binIdMatch = idString.match(/bin_(\d+)/);
        if (binIdMatch && binIdMatch[1]) {
            const extractedBinId = Number(binIdMatch[1]);
            ShowBottomSheetObs.action({
                type: TypeBottomSheet.BIN_PRESS,
                binId: extractedBinId
            });
        }
    }, []);

    React.useEffect(() => {
        const initializeMap = async () => {
            try {
                await new Promise((resolve) => setTimeout(resolve, 100));
                setIsMapInitialized(true);
            } catch (error) {
                setIsMapInitialized(true);
            }
        };

        initializeMap();
    }, []);

    return (
        <Container isLoading={isLoading}>
            <Header
                type="search"
                handleFilterPress={handleFilterPress}
                handleSearchSubmit={handleSearchSubmit}
                searchQuery={searchQuery}
                setSearchQuery={handleSearchChange}
                placeholder="What do you want to recycle?"
            />

            <Box className="flex-1 bg-white">
                {hasSearched && searchQuery.trim().length > 0 ? (
                    isMapInitialized ? (
                        <MapComponent markers={markers} onMarkerPress={handleMarkerPress} />
                    ) : (
                        <LoadingAnimation />
                    )
                ) : null}
            </Box>

            <BottomSheetCustom ref={bottomSheetRef} height={562} closeOnPressMask={true}>
                <Box className="flex-1 px-4">
                    {bannerSearchPage?.banner && bannerSearchPage.banner?.status && (
                        <Banner banner={bannerSearchPage} />
                    )}
                    <Box>
                        <Text className="text-darkGray text-[15px] mt-4">
                            Use these bins to sort and dispose of recyclables correctly.
                        </Text>

                        <VStack className="mt-4">
                            <VStack>
                                <Text className="font-bold text-[16px] text-blackLight">
                                    {binsByWasteTypeIdAndAcceptedRecyclables?.[0]?.type.name}
                                </Text>
                                <Text className="text-[15px] text-darkGray">Accepts: {searchQuery}</Text>
                            </VStack>
                        </VStack>
                    </Box>
                    <ListView
                        data={binsByWasteTypeIdAndAcceptedRecyclables}
                        renderItem={renderItem}
                        keyList="id"
                        ItemSeparatorComponent={ItemSeparator}
                    />
                </Box>
            </BottomSheetCustom>
        </Container>
    );
};

export default SearchScreen;
