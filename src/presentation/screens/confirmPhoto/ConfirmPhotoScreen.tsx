import React, { useCallback } from "react";
import { SafeAreaView } from "react-native";

import { useRecyclingQueries } from "@/data/queries";

import { useRouteParams } from "@/presentation/hooks";

import { Header } from "@/presentation/components/header";
import { MyButton } from "@/presentation/components/myButton";
import { Box, Container, Image } from "@/presentation/components/ui";
import { ImageAssets, RouteName } from "@/shared/constants";

const ConfirmPhotoScreen = () => {
    const { recycling, isLoading } = useRecyclingQueries();
    const params = useRouteParams<typeof RouteName.ConfirmPhoto>();
    const photoPath = params?.photoPath;

    const handleSubmit = useCallback(async () => {
        if (!params?.binId || !photoPath) return;

        try {
            await recycling(params.binId, photoPath);
        } catch (err) {
            /* empty */
        }
    }, [photoPath, params?.binId, recycling]);

    return (
        <Container isLoading={isLoading}>
            <SafeAreaView className="flex-1">
                <Header title="Recyclable Submission" isShowBack />

                <Box className="flex-1 mx-4 mb-6 justify-center">
                    <Image
                        source={photoPath ? { uri: `file://${photoPath}` } : ImageAssets.placeholderImage}
                        className="w-full h-full rounded-lg"
                        alt="Recyclable item photo"
                        resizeMode="cover"
                    />
                </Box>

                <Box className="px-4 pb-8">
                    <MyButton text="Submit" onPress={handleSubmit} variant="primary" height={50} disabled={isLoading} />
                </Box>
            </SafeAreaView>
        </Container>
    );
};

export default ConfirmPhotoScreen;
