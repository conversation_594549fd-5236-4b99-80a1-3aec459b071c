import { BottomTabBarProps, BottomTabNavigationOptions, createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import React from "react";

import { Popup } from "../components/popup";
import { BottomTab } from "../components/tab";
import { Box } from "../components/ui";
import { useBanner } from "../hooks/banner";
import { usePointGoal } from "../hooks/point";
import { useRole } from "../hooks/role";
import { useGetProfile } from "../hooks/user";
import { ActivityScreen } from "../screens/activity";
import { EventsScreen } from "../screens/events";
import { HomeScreen } from "../screens/home";
import { HomeSchoolScreen } from "../screens/homeSchool";
import { RewardsScreen } from "../screens/rewards";

import { RouteName } from "@/shared/constants";

const Tabs = createBottomTabNavigator();

const bottomTabOptions: BottomTabNavigationOptions = {
    tabBarHideOnKeyboard: true,
    headerShown: false
};

const BoxTab = () => <Box />;

const BottomNavigator = () => {
    const { getPointGoal } = usePointGoal();
    useGetProfile();
    const { getBannerPopupHomePage } = useBanner();
    const { isIndividual } = useRole();

    React.useEffect(() => {
        getPointGoal();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    React.useEffect(() => {
        getBannerPopupHomePage();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const renderTabBar = React.useCallback((props: BottomTabBarProps) => <BottomTab {...props} />, []);

    const tabs = React.useMemo(() => {
        if (isIndividual) {
            return [
                { key: "home", name: RouteName.Home, component: HomeScreen },
                { key: "rewards", name: RouteName.Rewards, component: RewardsScreen },
                { key: "scan", name: "Box", component: BoxTab },
                { key: "event", name: RouteName.Event, component: EventsScreen },
                { key: "activity", name: RouteName.Activity, component: ActivityScreen }
            ];
        }
        return [
            { key: "home", name: RouteName.HomeSchool, component: HomeSchoolScreen },
            { key: "event", name: RouteName.EventSchool, component: EventsScreen },
            { key: "services", name: RouteName.ServiceSchool, component: BoxTab },
            { key: "profile", name: RouteName.ProfileSchool, component: BoxTab }
        ];
    }, [isIndividual]);

    return (
        <>
            <Tabs.Navigator
                tabBar={renderTabBar}
                screenOptions={bottomTabOptions}
                initialRouteName={isIndividual ? RouteName.Home : RouteName.HomeSchool}>
                {tabs.map((tab) => (
                    <Tabs.Screen key={tab.key} name={tab.name} component={tab.component} />
                ))}
            </Tabs.Navigator>
            <Popup />
        </>
    );
};

export default BottomNavigator;
