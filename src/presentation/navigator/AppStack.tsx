import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import React from "react";

import { ShowBottomSheet } from "../components/bottomSheet";
import { KeyboardViewSpacer } from "../components/keyboardSpace";
import { WebViewModal } from "../components/webview";
import { useShowToast } from "../hooks";
import { AboutEventScreen } from "../screens/aboutEvent";
import { AccountScreen } from "../screens/account";
import { ActivityHistoryScreen } from "../screens/activityHistory";
import { AreaClickScreen } from "../screens/areaClick";
import { ContactScreen } from "../screens/contact";
import { EditAccountScreen } from "../screens/editAccount";
import { EventDetailScreen } from "../screens/eventDetail";
import { MyVoucherScreen } from "../screens/myVoucher";
import { NotificationScreen } from "../screens/notification";
import { RedeemPointScreen } from "../screens/redeemPoint";
import { RewardDetailScreen } from "../screens/rewardDetail";
import { RewardsHistoryScreen } from "../screens/rewardsHistory";
import { Filter, SearchScreen } from "../screens/search";
import { SpecialEventScreen } from "../screens/specialEvent";
import { SplashScreen } from "../screens/splash";
import { WelcomeScreen } from "../screens/welcome";

import AuthStack from "./AuthStack";
import BottomNavigator from "./BottomNavigator";
import ScanStack from "./ScanStack";

import { setToastInstance } from "@/data/services/httpClient/HttpProblem";
import { RootNavigator } from "@/data/services/navigation";
import { RouteName } from "@/shared/constants";
import { screenOptions } from "@/shared/helper";

const Stack = createStackNavigator<RootStackParamList>();

const AppStack = () => {
    const showToast = useShowToast();

    React.useEffect(() => {
        if (showToast) {
            setToastInstance(showToast);
        }

        return () => {
            setToastInstance(null);
        };
    }, [showToast]);

    return (
        <KeyboardViewSpacer>
            <NavigationContainer ref={RootNavigator.navigationRef}>
                <Stack.Navigator screenOptions={screenOptions} initialRouteName={RouteName.Splash}>
                    <Stack.Screen name={RouteName.Splash} component={SplashScreen} />
                    <Stack.Screen name={RouteName.Welcome} component={WelcomeScreen} />
                    <Stack.Screen name={RouteName.Auth} component={AuthStack} />
                    <Stack.Screen name={RouteName.Contact} component={ContactScreen} />
                    <Stack.Screen name={RouteName.Bottom} component={BottomNavigator} />
                    <Stack.Screen name={RouteName.MyVoucher} component={MyVoucherScreen} />
                    <Stack.Screen name={RouteName.RewardsHistory} component={RewardsHistoryScreen} />
                    <Stack.Screen name={RouteName.RedeemPoint} component={RedeemPointScreen} />
                    <Stack.Screen name={RouteName.EventDetail} component={EventDetailScreen} />
                    <Stack.Screen name={RouteName.Account} component={AccountScreen} />
                    <Stack.Screen name={RouteName.EditAccount} component={EditAccountScreen} />
                    <Stack.Screen name={RouteName.ScanStack} component={ScanStack} />
                    <Stack.Screen name={RouteName.ActivityHistory} component={ActivityHistoryScreen} />
                    <Stack.Screen name={RouteName.Notification} component={NotificationScreen} />
                    <Stack.Screen name={RouteName.Search} component={SearchScreen} />
                    <Stack.Screen name={RouteName.Filter} component={Filter} />
                    <Stack.Screen name={RouteName.AreaClick} component={AreaClickScreen} />
                    <Stack.Screen name={RouteName.RewardDetail} component={RewardDetailScreen} />
                    <Stack.Screen name={RouteName.SpecialEvent} component={SpecialEventScreen} />
                    <Stack.Screen name={RouteName.AboutEvent} component={AboutEventScreen} />
                </Stack.Navigator>
                <ShowBottomSheet />

                <WebViewModal />
            </NavigationContainer>
        </KeyboardViewSpacer>
    );
};

export default AppStack;
