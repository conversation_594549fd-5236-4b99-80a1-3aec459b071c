import * as ImagePicker from "expo-image-picker";
import { UIImagePickerPresentationStyle } from "expo-image-picker";

export const pickerMedia = async (mediaType: "photo" | "video", isMultiple?: boolean) => {
    const options: ImagePicker.ImagePickerOptions = {
        mediaTypes: mediaType === "photo" ? "images" : "videos",
        allowsMultipleSelection: isMultiple,
        selectionLimit: isMultiple ? 5 : 1,
        quality: 1,
        presentationStyle: UIImagePickerPresentationStyle.FULL_SCREEN
    };

    const result = await ImagePicker.launchImageLibraryAsync(options);

    if (!result.canceled) {
        return result;
    }

    return undefined;
};
