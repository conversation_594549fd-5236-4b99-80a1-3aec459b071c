/**
 * Creates a FormData object from a plain JavaScript object
 * @template Data Type of the input object (must be a Record with string/number values)
 * @param paramObject Object to convert to FormData
 * @returns FormData object with all key-value pairs from the input object
 * @example
 * const data = {
 *   name: '<PERSON>',
 *   age: 30,
 *   photo: fileObject
 * };
 *
 * const formData = createDataFromDataObject(data);
 * // Returns FormData with:
 * // - name: '<PERSON>'
 * // - age: '30'
 * // - photo: fileObject
 *
 * // Use with fetch:
 * fetch(url, {
 *   method: 'POST',
 *   body: createDataFromDataObject(data)
 * });
 */
export const createDataFromDataObject = <Data extends Record<string, any | number>>(paramObject?: Data): FormData => {
    if (!paramObject) return new FormData();
    const form_data = new FormData();
    for (const key in paramObject) {
        form_data.append(key, paramObject[key]);
    }
    return form_data;
};
