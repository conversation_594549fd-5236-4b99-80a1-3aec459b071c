/**
 * Formats a number with comma separators for thousands
 * @param value Number to format
 * @param options Formatting options
 * @returns Formatted number string with commas
 * @example
 * formatNumberWithCommas(1234.56) // returns "1,234.56"
 * formatNumberWithCommas(8085.00) // returns "8,085"
 * formatNumberWithCommas(1234, { shouldShowDecimals: true }) // returns "1,234.00"
 */
export const formatNumberWithCommas = (
    value: number | string | undefined | null,
    options: {
        shouldShowDecimals?: boolean;
        minimumFractionDigits?: number;
        maximumFractionDigits?: number;
    } = {}
): string => {
    if (value === undefined || value === null || value === "") return "0";

    const numericValue = typeof value === "string" ? parseFloat(value) : value;

    if (isNaN(numericValue)) return "0";

    const { shouldShowDecimals = false, minimumFractionDigits = 0, maximumFractionDigits = 2 } = options;

    const isShowingDecimals = shouldShowDecimals || numericValue % 1 !== 0;

    return numericValue.toLocaleString("en-US", {
        minimumFractionDigits: isShowingDecimals ? minimumFractionDigits : 0,
        maximumFractionDigits: isShowingDecimals ? maximumFractionDigits : 0
    });
};

/**
 * Formats a number specifically for points display with abbreviation for large numbers
 * @param points Number of points to format
 * @returns Formatted points string with abbreviation for large numbers
 * @example
 * formatPoints(8085.00) // returns "8,085"
 * formatPoints(1234.56) // returns "1,234.56"
 * formatPoints(100) // returns "100"
 * formatPoints(1902) // returns "1,902"
 * formatPoints(1500000) // returns "1,5M"
 * formatPoints(1500000000) // returns "1,5B"
 */
export const formatPoints = (points: number | string | undefined | null): string => {
    if (points === undefined || points === null || points === "") return "0";

    const numericValue = typeof points === "string" ? parseFloat(points) : points;

    if (isNaN(numericValue)) return "0";

    if (numericValue >= 1000000000) {
        const billions = numericValue / 1000000000;
        const formatted = billions % 1 === 0 ? `${billions}` : `${billions.toFixed(1)}`;
        return formatted.replace(".", ",") + "B";
    }

    if (numericValue >= 1000000) {
        const millions = numericValue / 1000000;
        const formatted = millions % 1 === 0 ? `${millions}` : `${millions.toFixed(1)}`;
        return formatted.replace(".", ",") + "M";
    }

    return formatNumberWithCommas(points, { shouldShowDecimals: false });
};
