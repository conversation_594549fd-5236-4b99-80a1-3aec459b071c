interface MapLibreError {
    message: string;
    level: string;
    tag: string;
}

class MapLibreErrorHandler {
    private static instance: MapLibreErrorHandler;
    private originalConsoleWarn: typeof console.warn;
    private originalConsoleError: typeof console.error;
    private isInitialized = false;

    private constructor() {
        this.originalConsoleWarn = console.warn;
        this.originalConsoleError = console.error;
    }

    static getInstance(): MapLibreErrorHandler {
        if (!MapLibreErrorHandler.instance) {
            MapLibreErrorHandler.instance = new MapLibreErrorHandler();
        }
        return MapLibreErrorHandler.instance;
    }

    initialize(): void {
        if (this.isInitialized) return;

        console.warn = (...args) => {
            const message = args.join(" ");
            if (this.isExpectedMapLibreError(message)) {
                return;
            }
            this.originalConsoleWarn.apply(console, args);
        };

        console.error = (...args) => {
            const message = args.join(" ");
            if (this.isExpectedMapLibreError(message)) {
                return;
            }
            this.originalConsoleError.apply(console, args);
        };

        this.isInitialized = true;
    }

    cleanup(): void {
        if (!this.isInitialized) return;

        console.warn = this.originalConsoleWarn;
        console.error = this.originalConsoleError;
        this.isInitialized = false;
    }

    private isExpectedMapLibreError(message: string): boolean {
        const expectedErrorPatterns = [
            "Request failed due to a permanent error: Canceled",
            "Mbgl-HttpRequest",
            "canceled",
            "Request was canceled",
            "Network request was canceled",
            "HTTP request canceled"
        ];

        return expectedErrorPatterns.some((pattern) => message.toLowerCase().includes(pattern.toLowerCase()));
    }

    logError(error: MapLibreError | string): void {
        if (typeof error === "string") {
            if (!this.isExpectedMapLibreError(error)) {
                this.originalConsoleWarn("MapLibre Error:", error);
            }
        } else {
            if (!this.isExpectedMapLibreError(error.message)) {
                this.originalConsoleWarn("MapLibre Error:", {
                    message: error.message,
                    level: error.level,
                    tag: error.tag
                });
            }
        }
    }
    shouldShowErrors(): boolean {
        return __DEV__;
    }
}

export const maplibreErrorHandler = MapLibreErrorHandler.getInstance();

export const initializeMapLibreErrorHandling = (): void => {
    maplibreErrorHandler.initialize();
};

export const cleanupMapLibreErrorHandling = (): void => {
    maplibreErrorHandler.cleanup();
};
