/**
 * Helper functions for grouping data
 */

/**
 * Interface for a grouped item
 */
export type GroupedItem<T> = {
    id: string;
    name: string;
    isExpanded: boolean;
    items: T[];
};

/**
 * Groups items by a key extracted using the provided extractor function
 * @param items Array of items to group
 * @param keyExtractor Function to extract the grouping key from an item
 * @param initialExpandedIndex Index of the group that should be expanded by default
 * @returns Array of grouped items
 */
export const groupItems = <T>(
    items: T[] | undefined,
    keyExtractor: (item: T) => string,
    initialExpandedIndex = 0
): GroupedItem<T>[] => {
    if (!items || items.length === 0) {
        return [];
    }

    // Group items by the extracted key while preserving original order
    const grouped: Record<string, T[]> = {};
    const keyOrder: string[] = []; // Track order of first occurrence

    items.forEach((item) => {
        const key = keyExtractor(item);
        if (!grouped[key]) {
            grouped[key] = [];
            keyOrder.push(key); // Record first occurrence order
        }
        grouped[key].push(item);
    });

    // Convert to entries preserving original order (no sorting)
    const sortedEntries = keyOrder.map((key) => [key, grouped[key]] as [string, T[]]);

    return sortedEntries.map(([key, groupedItems], index) => {
        return {
            id: `group-${key}`, // Use key instead of index for stable ID
            name: key,
            isExpanded: index === initialExpandedIndex,
            items: groupedItems
        };
    });
};

/**
 * Extracts a simplified location name from an address string
 * @param address Full address string
 * @returns Simplified location name
 */
export const extractLocationName = (address: string): string => {
    if (!address || typeof address !== "string") {
        return "Unknown Location";
    }

    const normalizedAddress = address.trim();

    if (normalizedAddress.includes(",")) {
        return normalizedAddress.split(",")[0].trim();
    }

    return normalizedAddress;
};
