import * as Linking from "expo-linking";

export interface MapDirectionsOptions {
    address?: string;
    latitude?: number;
    longitude?: number;
    googleMapsUrl?: string;
    appleMapsUrl?: string;
}

export interface ActionSheetHandler {
    showActionSheetWithOptions: (
        options: {
            options: string[];
            cancelButtonIndex: number;
            title?: string;
        },
        callback: (buttonIndex?: number) => void
    ) => void;
}

/**
 * Opens navigation apps (Google Maps or Apple Maps) with the given location
 * @param options - Location and URL options
 * @param actionSheetHandler - Action sheet handler from useActionSheet hook
 */
export const handleGetDirections = (options: MapDirectionsOptions, actionSheetHandler: ActionSheetHandler) => {
    const { address, latitude, longitude, googleMapsUrl, appleMapsUrl } = options;

    if (!address && (!latitude || !longitude)) {
        return;
    }

    const mapOptions = ["Open with Google Maps", "Open with Apple Maps", "Cancel"];
    const cancelButtonIndex = 2;

    const handleMapSelection = (buttonIndex?: number) => {
        if (buttonIndex === 0) {
            // Google Maps
            let url = googleMapsUrl;
            if (!url) {
                if (latitude && longitude) {
                    url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
                } else if (address) {
                    url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(address)}`;
                }
            }
            if (url) {
                Linking.openURL(url);
            }
        } else if (buttonIndex === 1) {
            // Apple Maps
            let url = appleMapsUrl;
            if (!url) {
                if (latitude && longitude) {
                    url = `http://maps.apple.com/?q=${latitude},${longitude}`;
                } else if (address) {
                    url = `http://maps.apple.com/?q=${encodeURIComponent(address)}`;
                }
            }
            if (url) {
                Linking.openURL(url);
            }
        }
    };

    actionSheetHandler.showActionSheetWithOptions(
        {
            options: mapOptions,
            cancelButtonIndex,
            title: "Get Directions"
        },
        handleMapSelection
    );
};

/**
 * Calculate distance between two points using Haversine formula
 * @param lat1 - Latitude of first point
 * @param lon1 - Longitude of first point
 * @param lat2 - Latitude of second point
 * @param lon2 - Longitude of second point
 * @returns Distance in kilometers
 */
export const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * (Math.PI / 180);
    const dLon = (lon2 - lon1) * (Math.PI / 180);

    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(lat1 * (Math.PI / 180)) * Math.cos(lat2 * (Math.PI / 180)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in kilometers

    return distance;
};

/**
 * Format distance for display
 * @param distance - Distance in kilometers
 * @returns Formatted string like "1.2km", "150m", etc.
 */
export const formatDistance = (distance: number): string => {
    if (distance < 0.1) {
        // Less than 100m, show in meters
        const meters = Math.round(distance * 1000);
        return `${meters}m`;
    } else if (distance < 1) {
        // Less than 1km, show one decimal place
        return `${(distance * 1000).toFixed(0)}m`;
    } else if (distance < 10) {
        // Less than 10km, show one decimal place
        return `${distance.toFixed(1)}km`;
    } else {
        // 10km or more, show whole numbers
        return `${distance.toFixed(0)}km`;
    }
};

/**
 * Calculate and format distance between user location and target location
 * @param userLat - User's latitude
 * @param userLon - User's longitude
 * @param targetLat - Target latitude
 * @param targetLon - Target longitude
 * @returns Formatted distance string or null if user location not available
 */
export const calculateFormattedDistance = (
    userLat?: number,
    userLon?: number,
    targetLat?: number,
    targetLon?: number
): string | null => {
    if (!userLat || !userLon || !targetLat || !targetLon) {
        return null;
    }

    const distance = calculateDistance(userLat, userLon, targetLat, targetLon);
    return formatDistance(distance);
};

export const createGeoJSONCircle = (
    center: [number, number],
    radiusInKm: number,
    points: number = 64
): GeoJSON.Feature => {
    if (radiusInKm <= 0) {
        return {
            type: "Feature",
            geometry: {
                type: "Polygon",
                coordinates: [
                    [
                        [center[0], center[1]],
                        [center[0] + 0.0001, center[1]],
                        [center[0] + 0.0001, center[1] + 0.0001],
                        [center[0], center[1]]
                    ]
                ]
            },
            properties: {}
        };
    }

    const coords = {
        latitude: center[1],
        longitude: center[0]
    };

    const km = radiusInKm;

    const ret: number[][] = [];

    const distanceX = km / (111.32 * Math.cos((coords.latitude * Math.PI) / 180));

    const distanceY = km / 110.574;

    let theta, x, y;
    for (let i = 0; i < points; i++) {
        theta = (i / points) * (2 * Math.PI);
        x = distanceX * Math.cos(theta);
        y = distanceY * Math.sin(theta);

        ret.push([coords.longitude + x, coords.latitude + y]);
    }
    ret.push(ret[0]);

    return {
        type: "Feature",
        geometry: {
            type: "Polygon",
            coordinates: [ret]
        },
        properties: {}
    };
};
