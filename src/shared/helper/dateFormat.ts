import { differenceInDays, format, parseISO, startOfDay } from "date-fns";

/**
 * Formats a date string or Date object to specified format
 * @param date Date to format
 * @param formatStr date-fns format string (default: "dd/MM/yyyy")
 * @returns Formatted date string or empty string if date is undefined
 * @example
 * formatDate('2024-03-15') // returns "15/03/2024"
 * formatDate('2024-03-15', 'yyyy/MM/dd') // returns "2024/03/15"
 * formatDate() // returns ""
 */
export const formatDate = (date?: string | Date, formatStr: string = "dd/MM/yyyy"): string => {
    if (!date) return "";

    try {
        const dateObj = typeof date === "string" ? parseISO(date) : date;

        if (isNaN(dateObj.getTime())) {
            return "";
        }

        return format(dateObj, formatStr);
    } catch (error) {
        return "";
    }
};

/**
 * Formats a date relative to current time with specific display rules:
 * - Today: "today | HH:mm A"
 * - Within 7 days: "X days ago"
 * - Older than 7 days: "DD MMM, YYYY"
 * @param date Date string or Date object to format
 * @returns Formatted date string
 * @example
 * formatRelativeDate('2024-03-15T09:24:00Z') // returns "today | 09:24 AM" (if today)
 * formatRelativeDate('2024-03-13T09:24:00Z') // returns "2 days ago"
 * formatRelativeDate('2024-03-01T09:24:00Z') // returns "01 Mar, 2024"
 */
export const formatRelativeDate = (date: string | Date): string => {
    const dateObj = typeof date === "string" ? parseISO(date) : date;
    const now = new Date();
    const today = startOfDay(now);

    const diffDays = differenceInDays(today, startOfDay(dateObj));

    // Today
    if (diffDays === 0) {
        return `today | ${format(dateObj, "hh:mm a")}`;
    }

    // Within last 7 days
    if (diffDays > 0 && diffDays <= 7) {
        return `${diffDays} ${diffDays === 1 ? "day" : "days"} ago`;
    }

    // Older than 7 days
    return format(dateObj, "dd MMM, yyyy");
};

/**
 * Formats a date string or Date object to "dd/MM/yyyy • HH:mm:ss"
 * @param date Date string or Date object
 * @returns Formatted string like "20/05/2025 • 24:00:00"
 */
export function formatDateTimeFull(date?: string | Date): string {
    if (!date) return "";
    const dateObj = typeof date === "string" ? parseISO(date) : date;
    return `${format(dateObj, "dd/MM/yyyy")} • ${format(dateObj, "HH:mm:ss")}`;
}
