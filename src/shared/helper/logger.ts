import { isAndroid } from "./device";

class Logger {
    static error(tag: string, message: any, ...args: any[]) {
        if (__DEV__) {
            console.error(`[${tag}]`, message, ...args);
        } else if (isAndroid) {
            console.warn(`[${tag}] ${JSON.stringify(message)}`, ...args);
        } else {
            console.error(`[${tag}]`, message, ...args);
        }
    }

    static info(tag: string, message: any, ...args: any[]) {
        if (__DEV__ || isAndroid) {
            console.warn(`[${tag}]`, message, ...args);
        }
    }
}

export default Logger;
