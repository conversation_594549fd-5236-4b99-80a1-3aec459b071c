export const getFirstErrorMessage = (response: any): string | undefined => {
    if (response.errors && typeof response.errors === "object") {
        for (const val of Object.values(response.errors)) {
            if (Array.isArray(val) && val.length > 0) {
                return val[0];
            }
            if (typeof val === "string") {
                return val;
            }
        }
    }
};
