import { isEqual } from "radash";
import { Dimensions } from "react-native";

export * from "./dateFormat";
export * from "./dateOfBirthValidation";
export * from "./device";
export * from "./formDataHelper";
export * from "./getFirstErrorMessage";
export * from "./grouping";
export { default as Logger } from "./logger";
export * from "./map";
export * from "./navigation";
export * from "./number";
export * from "./phoneNumber";
export * from "./pickerMedia";
export * from "./storage";
export * from "./string";

export const fullWidth = Dimensions.get("window").width;

export const fullHeight = Dimensions.get("window").height;
/**
 * Checks if two values are deeply equal
 * @param val1 First value to compare
 * @param val2 Second value to compare
 * @returns True if values are equal, false otherwise
 * @example
 * compareValue({ a: 1 }, { a: 1 }) // returns true
 * compareValue([1, 2], [1, 2]) // returns true
 */
export const compareValue = <T>(val1: T, val2: T): boolean => {
    return isEqual(val1, val2);
};

/**
 * Deeply compares two objects for value changes
 * @param initial Initial object state
 * @param current Current object state
 * @returns True if values are different, false if they're the same
 * @example
 * compareValues(
 *   { name: 'John', date: '2023-01-01' },
 *   { name: 'John', date: '2023-01-02' }
 * ) // returns true
 */
export const compareFormValues = (initial: Record<string, any>, current: Record<string, any>): boolean => {
    if (initial === current) return false;
    if (!initial || !current) return true;

    const keys = [...new Set([...Object.keys(initial), ...Object.keys(current)])];

    return keys.some((key) => {
        const initialValue = String(initial[key] || "");
        const currentValue = String(current[key] || "");

        if (initialValue === "" && currentValue === "") return false;

        if (key.toLowerCase().includes("date") && initialValue && currentValue) {
            const initialDate = new Date(initialValue).getTime();
            const currentDate = new Date(currentValue).getTime();
            return !isNaN(initialDate) && !isNaN(currentDate) && initialDate !== currentDate;
        }

        if (Array.isArray(initial[key]) && Array.isArray(current[key])) {
            return JSON.stringify(initial[key]) !== JSON.stringify(current[key]);
        }

        if (typeof initial[key] === "object" && typeof current[key] === "object") {
            return compareFormValues(initial[key], current[key]);
        }

        return initialValue !== currentValue;
    });
};
