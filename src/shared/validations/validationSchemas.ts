import { BehaviorSubject } from "rxjs";
import { ref, string } from "yup";

import { validateApi } from "@/data/api";

import Errors from "../constants/Errors";

// RxJS subject to track postal code validation state
export const postalCodeValidatingSubject = new BehaviorSubject<boolean>(false);

// RxJS subject to track email validation state
export const emailValidatingSubject = new BehaviorSubject<boolean>(false);

// RxJS subject to track phone number validation state
export const phoneNumberValidatingSubject = new BehaviorSubject<boolean>(false);

// RxJS subject to track nickname validation state
export const nicknameValidatingSubject = new BehaviorSubject<boolean>(false);

export const emailSchema = string().email(Errors.EMAIL_INVALID).required(Errors.REQUIRED_EMAIL_INPUT);

/**
 * Email validation schema
 * - Must be a valid email format
 * - Is required
 */
export const emailWithApiSchema = string().test("is-email-valid", async (value, ctx) => {
    if (!value || !value.trim()) return ctx.createError({ message: Errors.REQUIRED_EMAIL_INPUT });
    if (!value.includes("@")) return ctx.createError({ message: Errors.EMAIL_INVALID });
    emailValidatingSubject.next(true);
    try {
        const errorMessage = await validateApi.validateEmail(value);
        if (errorMessage) {
            return ctx.createError({ message: errorMessage });
        }
        return true;
    } catch (error) {
        return ctx.createError({ message: error.message });
    } finally {
        emailValidatingSubject.next(false);
    }
});

/**
 * Username validation schema
 * - Must be at least 3 characters
 * - Can only contain letters, numbers, and underscores
 * - Is required
 */
export const usernameSchema = string()
    .required(Errors.REQUIRED_USERNAME_INPUT)
    .matches(/^[a-zA-Z0-9_]{3,}$/, Errors.USERNAME_INVALID);

/**
 * Password validation schema
 * - Is required
 */
export const passwordSchema = string().required(Errors.REQUIRED_PASSWORD_INPUT).min(8, Errors.PASSWORD_MIN_LENGTH);

/**
 * Confirm password validation schema
 * - Is required
 * - Must match password field
 * @param passwordField - The name of the password field to match against
 */
export const confirmPasswordSchema = (passwordField: string) =>
    string()
        .oneOf([ref(passwordField), undefined], Errors.PASSWORD_NOT_MATCH)
        .required(Errors.REQUIRED_CONFIRM_PASSWORD_INPUT)
        .min(8, Errors.PASSWORD_MIN_LENGTH);

/**
 * Phone number validation schema
 * - Is required
 */
export const phoneNumberSchema = string().required(Errors.REQUIRED_PHONE_NUMBER_INPUT);

/**
 * Phone number validation schema
 * - Is required
 * - Must be a valid Singapore phone number format (8 digits starting with 6, 8, or 9)
 */
export const phoneNumberWithApiSchema = string().test("is-phone-number-valid", async (value, ctx) => {
    if (!value || !value.trim()) return ctx.createError({ message: Errors.REQUIRED_PHONE_NUMBER_INPUT });
    if (value.length !== 8) return ctx.createError({ message: Errors.INVALID_PHONE_NUMBER_LENGTH });
    phoneNumberValidatingSubject.next(true);
    try {
        const errorMessage = await validateApi.validatePhoneNumber(value);
        if (errorMessage) {
            return ctx.createError({ message: errorMessage });
        }
        return true;
    } catch (error) {
        return ctx.createError({ message: error.message });
    } finally {
        phoneNumberValidatingSubject.next(false);
    }
});

/**
 * Nickname validation schema
 * - Is required
 */
export const nicknameSchema = string().required(Errors.REQUIRED_NICKNAME_INPUT);

/**
 * Name validation schema
 * - Is required
 */
export const nameSchema = string().required(Errors.REQUIRED_NAME_INPUT);

/**
 * Nickname validation schema
 * - Is required
 */
export const nicknameWithApiSchema = string().test("is-nickname-valid", async (value, ctx) => {
    if (!value || !value.trim()) return ctx.createError({ message: Errors.REQUIRED_NICKNAME_INPUT });
    if (value.length < 3) return ctx.createError({ message: Errors.NICKNAME_MIN_LENGTH });
    nicknameValidatingSubject.next(true);
    try {
        const errorMessage = await validateApi.validateNickname(value.trim());
        if (errorMessage) {
            return ctx.createError({ message: errorMessage });
        }
        return true;
    } catch (error) {
        return ctx.createError({ message: error.message });
    } finally {
        nicknameValidatingSubject.next(false);
    }
});

/**
 * OTP validation schema
 * - Is required
 * - Must be 6 characters long
 * - Must be a number
 */
export const otpSchema = string()
    .required(Errors.REQUIRED_OTP_INPUT)
    .length(6, Errors.INVALID_OTP_LENGTH.replace("{length}", "6"))
    .matches(/^\d+$/, Errors.INVALID_OTP_FORMAT);

/**
 * First name validation schema
 * - Is required
 */
export const firstNameSchema = string().required(Errors.REQUIRED_FIRST_NAME_INPUT);

/**
 * Last name validation schema
 * - Is required
 */
export const lastNameSchema = string().required(Errors.REQUIRED_LAST_NAME_INPUT);

/**
 * Address validation schema
 * - Is required
 */
export const addressSchema = string().required(Errors.REQUIRED_ADDRESS_INPUT);

/**
 * Postal code validation schema
 * - Is optional (not required)
 * - If provided, must be 6 digits
 * - Validates against OneMap API to check if postal code exists
 */
export const postalCodeSchema = string()
    .optional()
    .test("is-valid-postal-code", async (value, ctx) => {
        if (!value || value.trim() === "") {
            return ctx.createError({ message: Errors.REQUIRED_POSTAL_CODE_INPUT });
        }

        if (value.length !== 6 || !/^[0-9]{6}$/.test(value)) {
            return ctx.createError({ message: Errors.REQUIRED_POSTAL_CODE_INPUT });
        }

        postalCodeValidatingSubject.next(true);
        try {
            const errorMessage = await validateApi.validatePostalCode(value);
            if (errorMessage) {
                return ctx.createError({ message: errorMessage });
            }
            return true;
        } catch (error) {
            return ctx.createError({ message: error.message });
        } finally {
            postalCodeValidatingSubject.next(false);
        }
    });

/**
 * Date of birth validation schema
 * - Is required
 */
export const dobSchema = string().optional();

/**
 * School name validation schema
 * - Is required
 */
export const schoolNameSchema = string().required(Errors.REQUIRED_SCHOOL_NAME_INPUT);

/**
 * Business type validation schema
 * - Is required
 */
export const businessTypeSchema = string().required(Errors.REQUIRED_BUSINESS_TYPE_INPUT);

/**
 * Message validation schema
 * - Is required
 */
export const messageSchema = string().required(Errors.REQUIRED_MESSAGE_INPUT);

/**
 * Nature of query validation schema
 * - Is required
 */
export const natureOfQuerySchema = string().required(Errors.REQUIRED_NATURE_OF_QUERY_INPUT);
