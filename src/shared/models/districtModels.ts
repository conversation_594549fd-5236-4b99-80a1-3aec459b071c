import { z } from "zod";

export const DistrictResponseSchema = z.object({
    id: z.number(),
    name: z.string(),
    region: z.string()
});

export const AddressResponseSchema = z.object({
    location: z.object({
        SEARCHVAL: z.string(),
        BLK_NO: z.string(),
        ROAD_NAME: z.string(),
        BUILDING: z.string(),
        ADDRESS: z.string(),
        POSTAL: z.string(),
        X: z.string(),
        Y: z.string(),
        LATITUDE: z.string(),
        LONGITUDE: z.string()
    })
});

declare global {
    type DistrictResponse = z.infer<typeof DistrictResponseSchema>;
    type AddressResponse = z.infer<typeof AddressResponseSchema>;

    export type DistrictStateData = {
        districtPicked: DistrictResponse | undefined;
    };
}
