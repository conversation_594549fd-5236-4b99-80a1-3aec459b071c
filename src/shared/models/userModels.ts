import { capitalize } from "radash";
import * as z from "zod";

import { BusinessType } from "../constants";

export const UserSchema = z.object({
    id: z.number(),
    name: z.string(),
    first_name: z.string(),
    last_name: z.string(),
    display_name: z.string(),
    username: z.null(),
    email: z.string(),
    phone: z.string(),
    dob: z.null(),
    address: z.null(),
    postal_code: z.string(),
    email_verified_at: z.string(),
    status: z.boolean(),
    profile_photo_path: z.null(),
    verified: z.number(),
    verify_code: z.null(),
    verification_expires_at: z.null(),
    last_login: z.null(),
    point: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
    role_name: z.nativeEnum(BusinessType).transform((val) => capitalize(val)),
    profile_photo_url: z.string(),
    status_text: z.string(),
    unique_id: z.null(),
    roles: z.array(
        z.object({
            id: z.number(),
            name: z.string(),
            guard_name: z.string(),
            created_at: z.string(),
            updated_at: z.string(),
            pivot: z.object({
                model_type: z.string(),
                model_id: z.number(),
                role_id: z.number()
            })
        })
    )
});

export const UpdateUserSchema = z.object({
    first_name: z.string().optional(),
    last_name: z.string().optional(),
    display_name: z.string().optional(),
    address: z.string().optional(),
    dob: z.string().optional(),
    postal_code: z.string().optional()
});

export const MyRewardResponseSchema = z.object({
    reward: z.object({
        id: z.number(),
        code: z.string(),
        name: z.string(),
        price: z.string(),
        label: z.string(),
        description: z.string(),
        tnc: z.string(),
        image: z.string(),
        status: z.boolean(),
        expired_date: z.null(),
        created_at: z.string(),
        updated_at: z.string(),
        status_text: z.string(),
        image_url: z.string(),
        remaining_vouchers: z.number(),
        is_expired: z.boolean()
    }),
    voucher: z.object({
        id: z.number(),
        reward_id: z.number(),
        code: z.string(),
        status: z.number(),
        created_at: z.string(),
        updated_at: z.string(),
        user_id: z.null(),
        status_text: z.string()
    })
});

export const PointGoalResponseSchema = z.object({
    user_total_point: z.string(),
    daily_point_goal: z.string(),
    today_reward: z.string()
});

export const EventJoinedResponseSchema = z.object({
    id: z.number(),
    code: z.string(),
    secret_code: z.string(),
    event_type_id: z.number(),
    district_id: z.number(),
    user_id: z.number(),
    name: z.string(),
    address: z.string(),
    postal_code: z.string(),
    lat: z.string(),
    long: z.string(),
    date_start: z.string(),
    date_end: z.string(),
    time_start: z.string(),
    time_end: z.string(),
    description: z.string(),
    image: z.null(),
    status: z.boolean(),
    created_at: z.string(),
    updated_at: z.string(),
    status_text: z.string(),
    image_url: z.null(),
    time_start_formatted: z.string(),
    time_end_formatted: z.string(),
    distance: z.null()
});

export const EventsJoinedAllResponseSchema = z.object({
    id: z.number(),
    code: z.string(),
    secret_code: z.string(),
    event_type_id: z.number(),
    district_id: z.null(),
    user_id: z.number(),
    name: z.string(),
    address: z.string(),
    postal_code: z.string(),
    lat: z.string(),
    long: z.string(),
    date_start: z.string(),
    date_end: z.string(),
    time_start: z.string(),
    time_end: z.string(),
    description: z.string(),
    image: z.string(),
    status: z.boolean(),
    created_at: z.string(),
    updated_at: z.string(),
    status_text: z.string(),
    image_url: z.string().nullable(),
    time_start_formatted: z.string(),
    time_end_formatted: z.string(),
    distance: z.null(),
    pivot: z.object({ user_id: z.number(), event_id: z.number() }),
    type: z.object({ id: z.number(), name: z.string() })
});

declare global {
    type User = z.infer<typeof UserSchema>;
    type UpdateUserRequest = z.infer<typeof UpdateUserSchema>;
    type MyRewardResponse = z.infer<typeof MyRewardResponseSchema>;
    type PointGoalResponse = z.infer<typeof PointGoalResponseSchema>;
    type EventJoinedResponse = z.infer<typeof EventJoinedResponseSchema>;
    type EventsJoinedAllResponse = z.infer<typeof EventsJoinedAllResponseSchema>;

    type UserStateData = {
        user?: User;
        pointGoal?: PointGoalResponse;
        isGuest?: boolean;
    };
}
