import { ImagePickerAsset } from "expo-image-picker";
import { z } from "zod";

export const ContactRequestSchema = z.object({
    nature_of_query: z.string(),
    name: z.string(),
    email: z.string(),
    phone: z.string(),
    address: z.string(),
    postal_code: z.string(),
    message: z.string(),
    attachments: z.any() as z.ZodType<ImagePickerAsset[] | undefined>
});

declare global {
    type ContactRequest = z.infer<typeof ContactRequestSchema>;
}
