import { z } from "zod";

export const RewardResponseModel = z.object({
    id: z.number(),
    name: z.string(),
    label: z.string(),
    image: z.string(),
    price: z.string(),
    status_text: z.string(),
    image_url: z.string(),
    remaining_vouchers: z.number(),
    is_expired: z.boolean()
});

export const RewardDetailResponseModel = z.object({
    id: z.number(),
    name: z.string(),
    label: z.string(),
    image: z.string(),
    price: z.string(),
    description: z.string(),
    tnc: z.string(),
    status: z.boolean(),
    status_text: z.string(),
    image_url: z.string(),
    remaining_vouchers: z.number(),
    is_expired: z.boolean()
});

export const RewardRedeemResponseModel = z.object({
    voucher: z.object({
        id: z.number(),
        reward_id: z.number(),
        code: z.string(),
        status: z.number(),
        created_at: z.string(),
        updated_at: z.string(),
        user_id: z.null(),
        status_text: z.string()
    }),
    user_point: z.number(),
    reward_price: z.string()
});

declare global {
    type RewardResponse = z.infer<typeof RewardResponseModel>;
    type RewardDetailResponse = z.infer<typeof RewardDetailResponseModel>;
    type RewardRedeemResponse = z.infer<typeof RewardRedeemResponseModel>;
}
