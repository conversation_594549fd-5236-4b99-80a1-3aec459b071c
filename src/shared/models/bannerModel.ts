import { z } from "zod";

export const BannerResponseSchema = z.object({
    id: z.number(),
    name: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
    banner: z
        .object({
            id: z.number(),
            image: z.string().optional(),
            url: z.string().optional(),
            placement_id: z.number(),
            status: z.boolean(),
            created_at: z.string().optional(),
            updated_at: z.string().optional(),
            deleted_at: z.null().optional(),
            image_url: z.string().optional(),
            status_text: z.string().optional()
        })
        .optional()
});

declare global {
    type BannerResponse = z.infer<typeof BannerResponseSchema>;
}
