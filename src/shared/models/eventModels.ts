import { z } from "zod";

export const EventTypeResponseSchema = z.object({
    id: z.number(),
    name: z.string()
});

export const EventResponseSchema = z.object({
    id: z.number(),
    code: z.string(),
    date_start: z.string(),
    date_end: z.string(),
    time_start: z.string(),
    time_end: z.string(),
    address: z.string(),
    district_name: z.string(),
    postal_code: z.string(),
    lat: z.string(),
    long: z.string(),
    is_ongoing: z.boolean(),
    status_text: z.string(),
    image_url: z.string().nullable(),
    time_start_formatted: z.string(),
    time_end_formatted: z.string(),
    name: z.string(),
    description: z.string(),
    joined: z.boolean().optional()
});

export const CashForTrashLocationSchema = z.object({
    id: z.string(),
    name: z.string(),
    events: z.array(EventResponseSchema),
    isExpanded: z.boolean()
});

export const EventDetailResponseSchema = z.object({
    id: z.number(),
    code: z.string(),
    name: z.null(),
    description: z.string(),
    lat: z.string(),
    long: z.string(),
    date_start: z.string(),
    date_end: z.string(),
    time_start: z.string(),
    time_end: z.string(),
    address: z.string(),
    postal_code: z.string(),
    event_type_id: z.number(),
    district_id: z.number(),
    image: z.null(),
    status_text: z.string(),
    image_url: z.null(),
    time_start_formatted: z.string(),
    time_end_formatted: z.string(),
    distance: z.null(),
    type: z.object({ id: z.number(), name: z.string() }),
    district: z.object({ id: z.number(), name: z.string(), region: z.string() }),
    event_waste_type: z.array(
        z.object({
            id: z.number(),
            event_id: z.number(),
            waste_type_id: z.number(),
            price: z.string(),
            waste_type: z.object({ id: z.number(), name: z.string() })
        })
    ),
    event_bins: z.array(z.unknown())
});

export const JoinEventResponseSchema = z.object({
    id: z.number(),
    code: z.string(),
    secret_code: z.string(),
    event_type_id: z.number(),
    district_id: z.number(),
    user_id: z.number(),
    name: z.string(),
    address: z.string(),
    postal_code: z.string(),
    lat: z.string(),
    long: z.string(),
    date_start: z.string(),
    date_end: z.string(),
    time_start: z.string(),
    time_end: z.string(),
    description: z.string(),
    image: z.null(),
    status: z.boolean(),
    created_at: z.string(),
    updated_at: z.string(),
    status_text: z.string(),
    image_url: z.null(),
    time_start_formatted: z.string(),
    time_end_formatted: z.string(),
    distance: z.null()
});

export const EventLeaderboardResponseSchema = z.object({
    my_impact: z.object({ total_points: z.number() }),
    leaderboard_position: z.object({
        my_rank: z.number().nullable(),
        total_contributors: z.number()
    }),
    top_contributors: z.array(
        z.object({
            rank: z.number(),
            name: z.string(),
            initials: z.string(),
            total_points: z.number()
        })
    ),
    my_rank_data: z
        .object({
            name: z.string(),
            initials: z.string(),
            total_points: z.number()
        })
        .nullable()
});

const EventLeaderboardHistoryItemSchema = z.object({
    id: z.number(),
    user_id: z.number().optional(),
    bin_id: z.number(),
    reward: z.number().optional(),
    photo: z.string().optional(),
    created_at: z.string(),
    updated_at: z.string().optional(),
    photo_url: z.string().optional(),
    bin: z
        .object({
            id: z.number(),
            bin_type_id: z.number(),
            status_text: z.string().optional(),
            google_maps_url: z.string().optional(),
            apple_maps_url: z.string().optional(),
            point: z.null().optional(),
            qrcode_content: z.string().optional(),
            type: z.object({
                id: z.number(),
                name: z.string().optional(),
                qrcode_type: z.string().optional(),
                image_url: z.null().optional(),
                icon_url: z.null().optional()
            })
        })
        .optional()
});

export const EventLeaderboardHistorySchema = z.record(z.string(), z.array(EventLeaderboardHistoryItemSchema));
declare global {
    type EventType = z.infer<typeof EventTypeResponseSchema>;

    type EventResponse = z.infer<typeof EventResponseSchema>;

    type CashForTrashLocation = z.infer<typeof CashForTrashLocationSchema>;

    type EventDetailResponse = z.infer<typeof EventDetailResponseSchema>;

    type JoinEventResponse = z.infer<typeof JoinEventResponseSchema>;

    type EventLeaderboardResponse = z.infer<typeof EventLeaderboardResponseSchema>;

    type EventLeaderboardHistoryResponse = z.infer<typeof EventLeaderboardHistorySchema>;

    type EventLeaderboardHistoryItem = z.infer<typeof EventLeaderboardHistoryItemSchema>;
}
