const EndPoint = {
    auth: {
        login: "auth/Login",
        registerIndividual: "auth/Register",
        logout: "auth/Logout",
        refreshToken: "auth/RefreshToken",
        resetPassword: "auth/ResetPassword",
        registerEntity: "auth/RegisterEntity/{entity}",
        getAddressByPostalCode: "onemap/getLocationByPostalCode/{postalCode}"
    },
    validate: {
        validateEmail: "auth/validateEmail",
        validatePostalCode: "auth/validatePostalCode",
        validatePhoneNumber: "auth/validatePhoneNumber",
        validateNickname: "auth/validateNickname"
    },
    verify: {
        verify: "auth/Verify",
        resendCode: "auth/ResendCode"
    },
    user: {
        currentUser: "currentUser",
        currentUserUpdate: "currentUser/Update",
        myVoucher: "currentUser/reward/{status}",
        rewardDetail: "currentUser/reward/{id}/detail",
        pointGoal: "currentUser/point-goal",
        sendEmailVerification: "currentUser/send-email-verification-code",
        verifyEmail: "currentUser/verify-email",
        eventsJoined: "currentUser/events/joined/{eventId}",
        eventsJoinedAll: "currentUser/events/joined",
        leaderboard: "currentUser/leaderboard",
        recyclingHistory: "currentUser/recycling-history"
    },
    bin: {
        getAll: "bin-management/getAll",
        getByWasteTypeId: "bin-management/getAll?waste_type_id={wasteTypeId}",
        get: "bin-management/get/{binId}",
        getByOldQR: "bin-management/getByOldQR?qrcode_value={qrcode_value}",
        recycling: "recyclings/submit",
        scan: "bin-management/scan?qrcode_value={qrcode_value}"
    },
    acceptedRecyclables: {
        getAll: "accepted-recyclables/getAll"
    },
    event: {
        eventTypes: "event-types",
        events: "events",
        eventDetail: "events/{id}",
        eventLeaderboard: "events/{id}/leaderboard",
        joinEvent: "events/join"
    },
    districts: {
        search: "districts"
    },
    rewards: {
        getAll: "rewards/getAll",
        detail: "rewards/detail/{id}",
        redeem: "rewards/{id}/redeem"
    },
    pointsHistory: {
        get: "currentUser/points-history"
    },
    policies: {
        terms: "terms-and-conditions",
        privacy: "privacy-policy"
    },
    banners: {
        banners: "banners"
    },
    contact: {
        send: "contact/send"
    }
} as const;

export default EndPoint;
