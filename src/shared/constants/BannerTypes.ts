export const BannerTypes = {
    HOMEPAGE: 1,
    POPUP_HOMEPAGE: 2,
    SEARCH_PAGE: 3,
    SUCCESS_RECYCLE: 4,
    REWARD: 5,
    ACCOUNT: 6
} as const;

export type BannerType = (typeof BannerTypes)[keyof typeof BannerTypes];

export const getBannerTypeById = (id: number): BannerType | undefined => {
    return Object.values(BannerTypes).find((bannerId) => bannerId === id);
};

export const getBannerTypeName = (id: number): string | undefined => {
    const bannerType = getBannerTypeById(id);
    if (!bannerType) return undefined;

    const bannerNames: Record<BannerType, string> = {
        1: "Homepage",
        2: "Pop Up Homepage",
        3: "Search Page",
        4: "Success Recycle",
        5: "Reward",
        6: "Account"
    };

    return bannerNames[bannerType];
};
