import { capitalize } from "radash";

const BusinessType = {
    School: "school" as const,
    Enterprise: "enterprise" as const,
    User: "user" as const,
    Entity: "entity" as const,
    Public: "public" as const
} as const;

export const BUSINESS_TYPE_INDIVIDUAL = capitalize(BusinessType.Public);

export type BusinessType = (typeof BusinessType)[keyof typeof BusinessType];

export default BusinessType;
