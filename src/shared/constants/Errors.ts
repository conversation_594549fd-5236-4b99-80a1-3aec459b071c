const Errors = {
    REQUIRED_EMAIL_INPUT: "Please enter your email",
    REQUIRED_USERNAME_INPUT: "Please enter your username",
    REQUIRED_NICKNAME_INPUT: "Please enter your nickname",
    NICKNAME_MIN_LENGTH: "Nickname must be at least 3 characters",
    REQUIRED_PASSWORD_INPUT: "Please enter your password",
    EMAIL_INVALID: "Invalid email address",
    IS_NOT_EMAIL: "Email must end with .com",
    REQUIRED_FULLNAME_INPUT: "Please enter your full name",
    REQUIRED_CONFIRM_PASSWORD_INPUT: "Please confirm your password",
    PASSWORD_NOT_MATCH: "Passwords do not match",
    PASSWORD_MIN_LENGTH: "Password must be at least 8 characters",
    REQUIRED_PHONE_NUMBER_INPUT: "Please enter your phone number",
    INVALID_PHONE_NUMBER_LENGTH: "Phone number must be 8 digits",
    R<PERSON><PERSON><PERSON><PERSON><PERSON>_FIRST_NAME_INPUT: "Please enter your first name",
    R<PERSON><PERSON><PERSON>RED_LAST_NAME_INPUT: "Please enter your last name",
    RE<PERSON><PERSON>RED_DOB_INPUT: "Please enter your date of birth",
    REQUIRED_ADDRESS_INPUT: "Please enter your address",
    REQUIRED_POSTAL_CODE_INPUT: "Please enter your postal code",
    REQUIRED_SCHOOL_NAME_INPUT: "Please enter your school name",
    REQUIRED_OTP_INPUT: "Please enter your OTP",
    INVALID_OTP_LENGTH: "OTP must be {length} characters long",
    INVALID_OTP_FORMAT: "OTP must be a number",
    USERNAME_INVALID: "Username must be at least 3 characters and contain only letters, numbers, and underscores",
    NICKNAME_INVALID: "Nickname must be at least 3 characters and contain only letters, numbers, and underscores",
    REQUIRED_NAME_INPUT: "Please enter your name",
    DATE_FUTURE: "Date of birth cannot be in the future",
    TOO_OLD: "You must be at least 18 years old",
    MINIMUM_AGE: "You must be at least 18 years old",
    REQUIRED_BUSINESS_TYPE_INPUT: "Please select your business type",
    REQUIRED_MESSAGE_INPUT: "Please enter your message",
    REQUIRED_NATURE_OF_QUERY_INPUT: "Please select nature of query"
} as const;

export default Errors;
