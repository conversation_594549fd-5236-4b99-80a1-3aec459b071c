import { QueryClientProvider } from "@tanstack/react-query";
import { persistQueryClient } from "@tanstack/react-query-persist-client";
import React from "react";

import { cacheService, createSQLitePersister } from "@/data/services/database";
import { reactotron } from "@/data/services/reactotron";

const QueryProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const sqlitePersister = createSQLitePersister();

    React.useEffect(() => {
        const preloadDB = async () => {
            try {
                await cacheService.preloadDatabase();
            } catch (error) {
                console.error("Failed to preload database:", error);
            }
        };

        preloadDB();
    }, []);

    persistQueryClient({
        queryClient: reactotron.query.client,
        persister: sqlitePersister
    });

    return <QueryClientProvider client={reactotron.query.client}>{children}</QueryClientProvider>;
};

export default QueryProvider;
