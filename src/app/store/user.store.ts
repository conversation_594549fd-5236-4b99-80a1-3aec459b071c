import { createStore } from "./store.factory";

type UserState = UserStateData & {
    setCurrentUser: (user?: User) => void;
    clearUser: () => void;
    reset: () => void;
    setPointGoal: (pointGoal?: PointGoalResponse) => void;
    setGuestMode: (isGuest: boolean) => void;
    isGuestMode: () => boolean;
    isAuthenticated: () => boolean;
};

const initialState: UserStateData = {
    user: undefined,
    pointGoal: undefined,
    isGuest: false
};

export const useUserStore = createStore<
    UserState & {
        addPoint: (amount: number) => boolean;
        subtractPoint: (amount: number, onInsufficientPoint?: () => void) => boolean;
        hasEnoughPoint: (amount: number) => boolean;
        getCurrentPoint: () => number;
        updateTotalPoint: (newTotal: number) => boolean;
        setPointGoal: (pointGoal?: PointGoalResponse) => void;
    }
>("User", (set, get) => ({
    ...initialState,
    setCurrentUser: (user) => set({ user, isGuest: false }),
    clearUser: () => set({ user: undefined, isGuest: false }),
    reset: () => set(initialState),
    setPointGoal: (pointGoal) => set({ pointGoal }),
    setGuestMode: (isGuest) => set({ isGuest, user: undefined }),
    isGuestMode: () => get().isGuest || false,
    isAuthenticated: () => {
        const state = get();
        return !!state.user && !state.isGuest;
    },
    getCurrentPoint: () => {
        const user = get().user;
        return user ? Number(user.point) || 0 : 0;
    },

    hasEnoughPoint: (amount: number) => {
        if (amount < 0) return false;
        const currentPoint = get().getCurrentPoint();
        return currentPoint >= amount;
    },

    updateTotalPoint: (newTotal: number) => {
        if (newTotal < 0) {
            return false;
        }

        const prev = get().user;
        if (!prev) {
            return false;
        }

        set({ user: { ...prev, point: String(newTotal) } });
        return true;
    },

    addPoint: (amount: number) => {
        if (amount <= 0) {
            return false;
        }

        const prev = get().user;
        if (!prev) {
            return false;
        }

        const before = Number(prev.point) || 0;
        const after = before + amount;

        set({ user: { ...prev, point: String(after) } });
        return true;
    },

    subtractPoint: (amount: number, onInsufficientPoint?: () => void) => {
        if (amount <= 0) {
            return false;
        }

        const prev = get().user;
        if (!prev) {
            return false;
        }

        const before = Number(prev.point) || 0;

        if (before < amount) {
            if (onInsufficientPoint) onInsufficientPoint();
            return false;
        }

        const after = before - amount;

        set({ user: { ...prev, point: String(after) } });
        return true;
    }
}));
