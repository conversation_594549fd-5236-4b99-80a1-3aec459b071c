import { createStore } from "./store.factory";

import { HttpClient } from "@/data/services/httpClient";

type LoginState = LoginStateData & {
    setLogin: (login?: LoginResponse) => void;
    clearLogin: () => void;
    reset: () => void;
};

const initialState: LoginStateData = {
    login: undefined
};

export const useLoginStore = createStore<LoginState>("Login", (set) => ({
    ...initialState,
    setLogin: async (login) => {
        await HttpClient.getTokenService().setSession({
            accessToken: login?.access_token,
            refreshToken: login?.refresh_token,
            accessTokenExpiresAt: login?.access_token_expires_at,
            refreshTokenExpiresAt: login?.refresh_token_expires_at
        });
        set((state) => ({ login: login ? { ...state.login, login } : state.login }));
    },
    clearLogin: () => set({ login: undefined }),
    reset: () => set(initialState)
}));
