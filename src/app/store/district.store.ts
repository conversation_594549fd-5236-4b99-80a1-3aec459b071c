import { createStore } from "./store.factory";

type DistrictState = DistrictStateData & {
    setDistrictPicked: (district: DistrictResponse) => void;
    clearDistrictPicked: () => void;
    reset: () => void;
};

const initialState: DistrictStateData = {
    districtPicked: undefined
};

export const useDistrictStore = createStore<DistrictState>("District", (set) => ({
    ...initialState,
    setDistrictPicked: (district) => set({ districtPicked: district }),
    clearDistrictPicked: () => set({ districtPicked: undefined }),
    reset: () => set(initialState)
}));
