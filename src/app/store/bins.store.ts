import { createStore } from "./store.factory";

type BinState = BinStateData & {
    setBins: (bins: BinResponse[]) => void;
    setBinsByWasteTypeId: (bins: BinResponse[] | undefined) => void;
    setBinsByWasteTypeIdAndAcceptedRecyclables: (bins: BinResponse[] | undefined) => void;
    clearBins: () => void;
    reset: () => void;
};

const initialState: BinStateData = {
    bins: undefined,
    binsByWasteTypeId: undefined,
    binsByWasteTypeIdAndAcceptedRecyclables: undefined
};

export const useBinStore = createStore<BinState>("Bin", (set) => ({
    ...initialState,
    setBins: (bins) => set({ bins }),
    setBinsByWasteTypeId: (bins) => set({ binsByWasteTypeId: bins }),
    setBinsByWasteTypeIdAndAcceptedRecyclables: (bins) => set({ binsByWasteTypeIdAndAcceptedRecyclables: bins }),
    clearBins: () => set({ bins: undefined }),
    reset: () => set(initialState)
}));
