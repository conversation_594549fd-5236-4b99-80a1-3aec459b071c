import { createStore } from "./store.factory";

export type FilterItem = {
    id: number;
    name: string;
};

type FilterState = {
    checkedItems: FilterItem[];
    toggleItem: (checkedItem: FilterItem) => void;
    resetFilter: () => void;
    setCheckedItems: (items: FilterItem[]) => void;
    getCheckedItemsArray: () => FilterItem[];
};

const initialState = {
    checkedItems: []
};

export const useFilterStore = createStore<FilterState>("Filter", (set, get) => ({
    ...initialState,

    toggleItem: (checkedItem: FilterItem) => {
        const currentCheckedItems = get().checkedItems;

        if (currentCheckedItems.some((item) => item.id === checkedItem.id)) {
            set({ checkedItems: currentCheckedItems.filter((item) => item.id !== checkedItem.id) });
        } else {
            set({ checkedItems: [...currentCheckedItems, checkedItem] });
        }
    },

    resetFilter: () => set({ checkedItems: [] }),

    setCheckedItems: (items: FilterItem[]) => set({ checkedItems: [...items] }),

    getCheckedItemsArray: () => get().checkedItems
}));
