import { binApi } from "../api/binApi";

import { binDatabase } from "./database";

export const SYNC_STATE = {
    IDLE: "idle" as const,
    DOWNLOADING: "downloading" as const,
    SYNCING: "syncing" as const,
    ERROR: "error" as const,
    BACKGROUND_REFRESH: "background_refresh" as const
} as const;

type DataSyncState = (typeof SYNC_STATE)[keyof typeof SYNC_STATE];

export interface BinStorageStatus {
    state: DataSyncState;
    progress: number;
    totalRecords: number;
    downloadedRecords: number;
    lastSync: number | null;
    error?: string;
    isInitialLoad: boolean;
}

class BinStorageService {
    private syncState: DataSyncState = SYNC_STATE.IDLE;
    private refreshInterval: ReturnType<typeof setInterval> | null = null;
    private statusListeners: Array<(status: BinStorageStatus) => void> = [];
    private isInitialized = false;
    private isInitialLoad = true;
    private currentProgress = 0;
    private totalRecords = 0;

    async initialize(): Promise<void> {
        if (this.isInitialized) return;

        try {
            await binDatabase.initialize();
            this.isInitialized = true;
            this.startBackgroundRefresh();
        } catch (error) {
            console.error("BinStorageService initialization failed:", error);
            throw error;
        }
    }

    async getLocalBins(): Promise<BinResponse[]> {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const localBins = await binDatabase.getAllBins();

            if (localBins.length === 0) {
                this.isInitialLoad = true;
                this.triggerDataDownload(false);
            } else {
                this.isInitialLoad = false;

                const isDataStale = await binDatabase.isDataStale(30);
                if (isDataStale) {
                    this.triggerDataDownload(true);
                }
            }

            return localBins;
        } catch (error) {
            console.error("Failed to get local bins:", error);
            return [];
        }
    }

    async getLocalBinsByWasteTypeId(wasteTypeId: number): Promise<BinResponse[]> {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            return await binDatabase.getBinsByWasteTypeId(wasteTypeId);
        } catch (error) {
            console.error("Failed to get local bins by waste type:", error);
            return [];
        }
    }

    private async triggerDataDownload(isBackgroundRefresh: boolean = false): Promise<void> {
        if (this.syncState === SYNC_STATE.DOWNLOADING || this.syncState === SYNC_STATE.SYNCING) {
            return;
        }

        try {
            const downloadState = isBackgroundRefresh ? SYNC_STATE.BACKGROUND_REFRESH : SYNC_STATE.DOWNLOADING;
            this.updateSyncState(downloadState);

            this.currentProgress = 0;
            this.totalRecords = 0;

            const response = await binApi.getAll();

            if (response?.ok && response.data) {
                this.totalRecords = response.data.length;
                this.currentProgress = 50;

                if (!isBackgroundRefresh) {
                    this.updateSyncState(SYNC_STATE.SYNCING);
                }

                try {
                    await binDatabase.clearAllData();

                    await this.storeBinsWithProgress(response.data, isBackgroundRefresh);
                } catch (storageError) {
                    console.error("Failed to store bins in database:", storageError);
                    throw new Error(
                        `Database storage failed: ${storageError instanceof Error ? storageError.message : "Unknown error"}`
                    );
                }

                this.currentProgress = 100;
                this.isInitialLoad = false;
                this.updateSyncState(SYNC_STATE.IDLE);
                this.notifyStatusChange();
            } else {
                throw new Error("Failed to fetch bin data from API");
            }
        } catch (error) {
            console.error("Data download failed:", error);

            const localBins = await binDatabase.getAllBins();
            if (localBins.length > 0) {
                this.isInitialLoad = false;
                this.updateSyncState(SYNC_STATE.IDLE);
            } else {
                this.updateSyncState(SYNC_STATE.ERROR, error instanceof Error ? error.message : "Unknown error");
            }
        }
    }

    private async storeBinsWithProgress(bins: BinResponse[], isBackgroundRefresh: boolean): Promise<void> {
        const batchSize = 50;
        const totalBatches = Math.ceil(bins.length / batchSize);

        for (let i = 0; i < totalBatches; i++) {
            const start = i * batchSize;
            const end = Math.min(start + batchSize, bins.length);
            const batch = bins.slice(start, end);

            await binDatabase.storeBins(batch);

            if (!isBackgroundRefresh) {
                this.currentProgress = 50 + Math.round(((i + 1) / totalBatches) * 50);
                this.notifyStatusChange();
            }
        }
    }

    private startBackgroundRefresh(): void {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        this.refreshInterval = setInterval(
            async () => {
                try {
                    const isStale = await binDatabase.isDataStale(30);

                    if (isStale) {
                        await this.triggerDataDownload(true);
                    }
                } catch (error) {
                    console.error("Background refresh failed:", error);
                }
            },
            30 * 60 * 1000
        );
    }

    private updateSyncState(state: DataSyncState, error?: string): void {
        this.syncState = state;
        this.notifyStatusChange(error);
    }

    private async notifyStatusChange(error?: string): Promise<void> {
        try {
            const totalRecords = await binDatabase.getDataCount();
            const lastSync = await binDatabase.getLastUpdateTime();

            const status: BinStorageStatus = {
                state: this.syncState,
                progress: this.syncState === SYNC_STATE.IDLE ? 100 : this.currentProgress,
                totalRecords: this.totalRecords || totalRecords,
                downloadedRecords: totalRecords,
                lastSync,
                error,
                isInitialLoad: this.isInitialLoad
            };

            this.statusListeners.forEach((listener) => {
                try {
                    listener(status);
                } catch (listenerError) {
                    console.error("Status listener error:", listenerError);
                }
            });
        } catch (statusError) {
            console.error("Failed to notify status change:", statusError);
        }
    }

    addStatusListener(listener: (status: BinStorageStatus) => void): () => void {
        this.statusListeners.push(listener);

        return () => {
            const index = this.statusListeners.indexOf(listener);
            if (index > -1) {
                this.statusListeners.splice(index, 1);
            }
        };
    }

    async forceRefresh(): Promise<void> {
        this.isInitialLoad = false;
        await this.triggerDataDownload(false);
    }

    async clearCache(): Promise<void> {
        try {
            await binDatabase.clearAllData();
            this.notifyStatusChange();
        } catch (error) {
            console.error("Failed to clear cache:", error);
            throw error;
        }
    }

    getStatus(): BinStorageStatus {
        return {
            state: this.syncState,
            progress: this.syncState === SYNC_STATE.IDLE ? 100 : this.currentProgress,
            totalRecords: this.totalRecords,
            downloadedRecords: 0,
            lastSync: null,
            isInitialLoad: this.isInitialLoad
        };
    }

    destroy(): void {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
        this.statusListeners = [];
        this.isInitialized = false;
    }
}

export const binStorageService = new BinStorageService();
