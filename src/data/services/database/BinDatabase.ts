import * as SQLite from "expo-sqlite";

export interface LocalBinData {
    id: number;
    bin_type_id: number;
    address: string;
    lat: string;
    long: string;
    map_radius: string;
    status: boolean;
    created_at: string;
    updated_at: string;
    e_waste_bin_type_id: number | null;
    qrcode: string | null;
    remark: string | null;
    status_text: string;
    google_maps_url: string;
    apple_maps_url: string;
    code: string;
    type_json: string;
    last_updated: number;
}

class BinDatabase {
    private db: SQLite.SQLiteDatabase | null = null;
    private isInitialized = false;

    async initialize(): Promise<void> {
        if (this.isInitialized) return;

        try {
            this.db = await SQLite.openDatabaseAsync("bins.db");
            await this.createTables();
            this.isInitialized = true;
        } catch (error) {
            console.error("Database initialization failed:", error);
            throw error;
        }
    }

    private async createTables(): Promise<void> {
        if (!this.db) throw new Error("Database not initialized");

        const createTableQuery = `
            CREATE TABLE IF NOT EXISTS bins (
                id INTEGER PRIMARY KEY,
                bin_type_id INTEGER NOT NULL,
                address TEXT NOT NULL,
                lat TEXT NOT NULL,
                long TEXT NOT NULL,
                map_radius TEXT NOT NULL,
                status INTEGER NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                e_waste_bin_type_id INTEGER,
                qrcode TEXT,
                remark TEXT,
                status_text TEXT NOT NULL,
                google_maps_url TEXT NOT NULL,
                apple_maps_url TEXT NOT NULL,
                code TEXT NOT NULL,
                type_json TEXT NOT NULL,
                last_updated INTEGER NOT NULL,
                UNIQUE(id)
            );
        `;

        const createIndexQuery = `
            CREATE INDEX IF NOT EXISTS idx_bins_location ON bins (lat, long);
            CREATE INDEX IF NOT EXISTS idx_bins_type ON bins (bin_type_id);
            CREATE INDEX IF NOT EXISTS idx_bins_last_updated ON bins (last_updated);
        `;

        await this.db.execAsync(createTableQuery);
        await this.db.execAsync(createIndexQuery);
    }

    async storeBins(bins: BinResponse[]): Promise<void> {
        if (!this.db) throw new Error("Database not initialized");

        const now = Date.now();

        try {
            const insertQuery = `
                INSERT OR REPLACE INTO bins (
                    id, bin_type_id, address, lat, long, map_radius, status,
                    created_at, updated_at, e_waste_bin_type_id, qrcode, remark,
                    status_text, google_maps_url, apple_maps_url, code, type_json, last_updated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            await this.db.withTransactionAsync(async () => {
                for (const bin of bins) {
                    try {
                        const createdAt = bin.created_at || new Date().toISOString();
                        const updatedAt = bin.updated_at || new Date().toISOString();

                        await this.db!.runAsync(insertQuery, [
                            bin.id,
                            bin.bin_type_id,
                            bin.address || "",
                            bin.lat || "0",
                            bin.long || "0",
                            bin.map_radius || "0",
                            bin.status ? 1 : 0,
                            createdAt,
                            updatedAt,
                            bin.e_waste_bin_type_id,
                            bin.qrcode,
                            bin.remark,
                            bin.status_text || "",
                            bin.google_maps_url || "",
                            bin.apple_maps_url || "",
                            bin.code || "",
                            JSON.stringify(bin.type || {}),
                            now
                        ]);
                    } catch (insertError) {
                        console.error(`Failed to insert bin ${bin.id}:`, insertError);
                    }
                }
            });
        } catch (error) {
            console.error("Failed to store bins:", error);
            throw error;
        }
    }

    async getAllBins(): Promise<BinResponse[]> {
        if (!this.db) throw new Error("Database not initialized");

        const result = await this.db.getAllAsync("SELECT * FROM bins ORDER BY id");

        return result.map((row: any) => ({
            id: row.id,
            bin_type_id: row.bin_type_id,
            address: row.address,
            lat: row.lat,
            long: row.long,
            map_radius: row.map_radius,
            status: Boolean(row.status),
            created_at: row.created_at,
            updated_at: row.updated_at,
            e_waste_bin_type_id: row.e_waste_bin_type_id,
            qrcode: row.qrcode,
            remark: row.remark,
            status_text: row.status_text,
            google_maps_url: row.google_maps_url,
            apple_maps_url: row.apple_maps_url,
            code: row.code,
            type: JSON.parse(row.type_json)
        }));
    }

    async getBinsByWasteTypeId(wasteTypeId: number): Promise<BinResponse[]> {
        if (!this.db) throw new Error("Database not initialized");

        const query = `
            SELECT * FROM bins 
            WHERE type_json LIKE '%"waste_types":%"id":${wasteTypeId}%'
            ORDER BY id
        `;

        const result = await this.db.getAllAsync(query);

        return result.map((row: any) => ({
            id: row.id,
            bin_type_id: row.bin_type_id,
            address: row.address,
            lat: row.lat,
            long: row.long,
            map_radius: row.map_radius,
            status: Boolean(row.status),
            created_at: row.created_at,
            updated_at: row.updated_at,
            e_waste_bin_type_id: row.e_waste_bin_type_id,
            qrcode: row.qrcode,
            remark: row.remark,
            status_text: row.status_text,
            google_maps_url: row.google_maps_url,
            apple_maps_url: row.apple_maps_url,
            code: row.code,
            type: JSON.parse(row.type_json)
        }));
    }

    async getLastUpdateTime(): Promise<number | null> {
        if (!this.db) throw new Error("Database not initialized");

        const result = (await this.db.getFirstAsync("SELECT MAX(last_updated) as last_updated FROM bins")) as {
            last_updated: number;
        } | null;
        return result?.last_updated || null;
    }

    async isDataStale(maxAgeMinutes: number = 60): Promise<boolean> {
        const lastUpdate = await this.getLastUpdateTime();
        if (!lastUpdate) return true;

        const now = Date.now();
        const maxAge = maxAgeMinutes * 60 * 1000;
        return now - lastUpdate > maxAge;
    }

    async clearAllData(): Promise<void> {
        if (!this.db) throw new Error("Database not initialized");
        await this.db.execAsync("DELETE FROM bins");
    }

    async getDataCount(): Promise<number> {
        if (!this.db) throw new Error("Database not initialized");

        const result = (await this.db.getFirstAsync("SELECT COUNT(*) as count FROM bins")) as { count: number } | null;
        return result?.count || 0;
    }
}

export const binDatabase = new BinDatabase();
