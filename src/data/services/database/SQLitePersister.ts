import { PersistedClient, Persister } from "@tanstack/react-query-persist-client";
import { debounce } from "radash";

import { cacheService } from "./CacheService";

export const createSQLitePersister = (): Persister => {
    let lastPersistedState: string | null = null;

    const debouncedPersist = debounce({ delay: 1000 }, async (clientState: PersistedClient) => {
        const stateString = JSON.stringify(clientState);

        if (lastPersistedState !== stateString) {
            await cacheService.setQueryCache("react-query-client", clientState);
            lastPersistedState = stateString;
        }
    });

    return {
        persistClient: async (clientState) => {
            try {
                debouncedPersist(clientState);
            } catch (error) {
                console.error("Failed to persist React Query client state:", error);
            }
        },

        restoreClient: async () => {
            try {
                const clientState = await cacheService.getQueryCache("react-query-client");
                return clientState || undefined;
            } catch (error) {
                console.error("Failed to restore React Query client state:", error);
            }
        },

        removeClient: async () => {
            try {
                await cacheService.removeQueryCache("react-query-client");
                lastPersistedState = null;
            } catch (error) {
                console.error("Failed to remove React Query client state:", error);
            }
        }
    };
};
