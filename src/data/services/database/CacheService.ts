import * as SQLite from "expo-sqlite";

import Logger from "@/shared/helper/logger";

export interface CacheEntry {
    key: string;
    url: string;
    method: string;
    headers: string;
    response: string;
    status: number;
    timestamp: number;
}

export interface QueryCacheEntry {
    queryKey: string;
    data: string;
    timestamp: number;
}

class CacheService {
    private static readonly dbName = "app_cache.db";
    private static instance: CacheService;
    private db: SQLite.SQLiteDatabase | null = null;
    private isInitialized = false;
    private readonly currentDbVersion = 2;
    private recoveryAttempts = 0;
    private readonly maxRecoveryAttempts = 3;

    private constructor() {}

    static getInstance(): CacheService {
        if (!CacheService.instance) {
            CacheService.instance = new CacheService();
        }
        return CacheService.instance;
    }

    async ensureInitialized(): Promise<void> {
        if (this.isInitialized) return;

        await this.initialize();
    }

    async forceInitialize(): Promise<void> {
        this.isInitialized = false;
        this.recoveryAttempts = 0;
        await this.initialize();
    }

    resetRecoveryCounter(): void {
        this.recoveryAttempts = 0;
        Logger.info("CacheService", "Recovery counter reset");
    }

    async resetDatabase(): Promise<void> {
        if (!this.db) return;

        try {
            Logger.info("CacheService", "Resetting database completely...");

            await this.db.closeAsync();
            this.db = null;
            this.isInitialized = false;

            this.db = await SQLite.openDatabaseAsync(CacheService.dbName);

            await this.db.execAsync("DROP TABLE IF EXISTS query_cache_entries");
            await this.db.execAsync("DROP TABLE IF EXISTS cache_entries");
            await this.db.execAsync("DROP TABLE IF EXISTS db_version");

            await this.db.execAsync("VACUUM");

            Logger.info("CacheService", "Database reset completed");
        } catch (error) {
            Logger.error("CacheService", "Failed to reset database:", error);
            throw error;
        }
    }

    async preloadDatabase(): Promise<void> {
        try {
            await this.ensureInitialized();
            Logger.info("CacheService", "Database preloaded successfully");
        } catch (error) {
            Logger.error("CacheService", "Failed to preload database, attempting recovery...");

            if (this.recoveryAttempts >= this.maxRecoveryAttempts) {
                Logger.error("CacheService", "Max recovery attempts reached in preload, cannot recover");
                this.recoveryAttempts = 0;
                throw new Error("Database recovery failed after maximum attempts");
            }

            try {
                this.isInitialized = false;
                this.recoveryAttempts++;
                await this.resetDatabase();
                await this.ensureInitialized();
                this.recoveryAttempts = 0;
                Logger.info("CacheService", "Database recovery successful");
            } catch (recoveryError) {
                Logger.error("CacheService", "Database recovery failed:", recoveryError);
                throw recoveryError;
            }
        }
    }

    async initialize(): Promise<void> {
        if (this.isInitialized) return;

        try {
            this.db = await SQLite.openDatabaseAsync(CacheService.dbName);

            await this.createVersionTable();

            await this.createTables();

            await this.migrateDatabaseIfNeeded();

            this.isInitialized = true;
            Logger.info("CacheService", "Database initialized successfully");
        } catch (error) {
            Logger.error("CacheService", "Failed to initialize database:", error);

            if (this.db && this.recoveryAttempts < this.maxRecoveryAttempts) {
                try {
                    this.recoveryAttempts++;
                    Logger.info(
                        "CacheService",
                        `Attempting to reset corrupted database (attempt ${this.recoveryAttempts}/${this.maxRecoveryAttempts})...`
                    );
                    await this.resetDatabase();

                    await new Promise((resolve) => setTimeout(resolve, 100));

                    await this.createVersionTable();
                    await this.createTables();
                    await this.migrateDatabaseIfNeeded();
                    this.isInitialized = true;
                    this.recoveryAttempts = 0;
                    Logger.info("CacheService", "Database recovery completed");
                } catch (resetError) {
                    Logger.error(
                        "CacheService",
                        `Failed to reset and reinitialize database (attempt ${this.recoveryAttempts}):`,
                        resetError
                    );

                    if (this.recoveryAttempts >= this.maxRecoveryAttempts) {
                        Logger.error("CacheService", "Max recovery attempts reached, giving up");
                        this.recoveryAttempts = 0;
                        throw new Error("Database recovery failed after maximum attempts");
                    }

                    throw error;
                }
            } else {
                if (this.recoveryAttempts >= this.maxRecoveryAttempts) {
                    Logger.error("CacheService", "Max recovery attempts reached, cannot initialize database");
                    this.recoveryAttempts = 0;
                    throw new Error("Database recovery failed after maximum attempts");
                }
                throw error;
            }
        }
    }

    private async createVersionTable(): Promise<void> {
        if (!this.db) return;

        try {
            const tableExists = await this.db.getFirstAsync<{ count: number }>(
                "SELECT COUNT(*) as count FROM sqlite_master WHERE type='table' AND name='db_version'"
            );

            if (tableExists && tableExists.count > 0) {
                Logger.info("CacheService", "Version table already exists, skipping creation");
                return;
            }

            await this.db.execAsync(`
                CREATE TABLE IF NOT EXISTS db_version (
                    id INTEGER PRIMARY KEY,
                    version INTEGER NOT NULL,
                    updated_at INTEGER NOT NULL
                );
            `);

            const versionRecord = await this.db.getFirstAsync<{ version: number }>(
                "SELECT version FROM db_version ORDER BY id DESC LIMIT 1"
            );

            if (!versionRecord) {
                await this.db.runAsync("INSERT OR IGNORE INTO db_version (id, version, updated_at) VALUES (1, ?, ?)", [
                    1,
                    Date.now()
                ]);
                Logger.info("CacheService", "Initialized database version to 1");
            } else {
                Logger.info("CacheService", `Database version table exists with version ${versionRecord.version}`);
            }
        } catch (error) {
            Logger.error("CacheService", "Failed to create version table:", error);
            throw error;
        }
    }

    private async createTables(): Promise<void> {
        if (!this.db) return;

        try {
            await this.db.execAsync(`
                CREATE TABLE IF NOT EXISTS cache_entries (
                    key TEXT PRIMARY KEY,
                    url TEXT NOT NULL,
                    method TEXT NOT NULL,
                    headers TEXT,
                    response TEXT NOT NULL,
                    status INTEGER NOT NULL,
                    timestamp INTEGER NOT NULL
                );
                
                CREATE INDEX IF NOT EXISTS idx_url_method ON cache_entries(url, method);
                
                CREATE TABLE IF NOT EXISTS query_cache_entries (
                    queryKey TEXT PRIMARY KEY,
                    data TEXT NOT NULL,
                    timestamp INTEGER NOT NULL
                );
                
                CREATE INDEX IF NOT EXISTS idx_query_key ON query_cache_entries(queryKey);
            `);
        } catch (error) {
            Logger.error("CacheService", "Failed to create tables:", error);
            throw error;
        }
    }

    private async migrateDatabaseIfNeeded(): Promise<void> {
        if (!this.db) return;

        try {
            const currentVersion = await this.getCurrentDatabaseVersion();
            Logger.info("CacheService", `Current database version: ${currentVersion}`);

            if (currentVersion < this.currentDbVersion) {
                Logger.info(
                    "CacheService",
                    `Starting database migration from version ${currentVersion} to ${this.currentDbVersion}`
                );

                if (currentVersion === 1) {
                    await this.migrateToVersion2();
                }

                await this.updateDatabaseVersion(this.currentDbVersion);
                Logger.info("CacheService", "Database migration completed successfully");
            } else {
                Logger.info("CacheService", "Database is already up to date");
            }
        } catch (error) {
            Logger.error("CacheService", "Database migration failed:", error);
            await this.rollbackMigration();
            throw error;
        }
    }

    private async migrateToVersion2(): Promise<void> {
        if (!this.db) return;

        try {
            Logger.info("CacheService", "Migrating to version 2: adding query_cache_entries table");

            const tableExists = await this.db.getFirstAsync<{ count: number }>(
                "SELECT COUNT(*) as count FROM sqlite_master WHERE type='table' AND name='query_cache_entries'"
            );

            if (!tableExists || tableExists.count === 0) {
                await this.db.execAsync(`
                    CREATE TABLE query_cache_entries (
                        queryKey TEXT PRIMARY KEY,
                        data TEXT NOT NULL,
                        timestamp INTEGER NOT NULL
                    );
                    
                    CREATE INDEX idx_query_key ON query_cache_entries(queryKey);
                `);
                Logger.info("CacheService", "Successfully created query_cache_entries table");
            } else {
                Logger.info("CacheService", "query_cache_entries table already exists");
            }
        } catch (error) {
            Logger.error("CacheService", "Failed to migrate to version 2:", error);
            throw error;
        }
    }

    private async getCurrentDatabaseVersion(): Promise<number> {
        if (!this.db) return 1;

        try {
            const tableExists = await this.db.getFirstAsync<{ count: number }>(
                "SELECT COUNT(*) as count FROM sqlite_master WHERE type='table' AND name='db_version'"
            );

            if (!tableExists || tableExists.count === 0) {
                Logger.info("CacheService", "Version table does not exist, returning version 1");
                return 1;
            }

            const result = await this.db.getFirstAsync<{ version: number }>(
                "SELECT version FROM db_version ORDER BY id DESC LIMIT 1"
            );
            return result?.version || 1;
        } catch (error) {
            Logger.error("CacheService", "Failed to get current database version:", error);
            return 1;
        }
    }

    private async updateDatabaseVersion(version: number): Promise<void> {
        if (!this.db) return;

        try {
            await this.db.runAsync("UPDATE db_version SET version = ?, updated_at = ? WHERE id = 1", [
                version,
                Date.now()
            ]);
        } catch (error) {
            Logger.error("CacheService", "Failed to update database version:", error);
            throw error;
        }
    }

    private async rollbackMigration(): Promise<void> {
        if (!this.db) return;

        try {
            Logger.info("CacheService", "Rolling back migration...");

            await this.db.execAsync("DROP TABLE IF EXISTS query_cache_entries");

            await this.db.execAsync("DROP TABLE IF EXISTS db_version");

            Logger.info("CacheService", "Migration rollback completed");
        } catch (error) {
            Logger.error("CacheService", "Failed to rollback migration:", error);
        }
    }

    private generateCacheKey(url: string, method: string, headers?: Record<string, string>): string {
        const headersStr = headers ? JSON.stringify(headers) : "";
        return `${method.toUpperCase()}_${url}_${headersStr}`;
    }

    async set(
        url: string,
        method: string,
        response: any,
        status: number,
        headers?: Record<string, string>
    ): Promise<void> {
        await this.ensureInitialized();
        if (!this.db) return;

        const key = this.generateCacheKey(url, method, headers);
        const timestamp = Date.now();

        try {
            await this.db.runAsync(
                `INSERT OR REPLACE INTO cache_entries 
                 (key, url, method, headers, response, status, timestamp) 
                 VALUES (?, ?, ?, ?, ?, ?, ?)`,
                [
                    key,
                    url,
                    method.toUpperCase(),
                    headers ? JSON.stringify(headers) : null,
                    JSON.stringify(response),
                    status,
                    timestamp
                ]
            );

            Logger.info("CacheService", `Cached response for ${method.toUpperCase()} ${url}`);
        } catch (error) {
            Logger.error("CacheService", "Failed to cache response:", error);
        }
    }

    async get(url: string, method: string, headers?: Record<string, string>): Promise<CacheEntry | null> {
        await this.ensureInitialized();
        if (!this.db) return null;

        const key = this.generateCacheKey(url, method, headers);

        try {
            const result = await this.db.getFirstAsync<CacheEntry>("SELECT * FROM cache_entries WHERE key = ?", [key]);

            if (result) {
                Logger.info("CacheService", `Cache hit for ${method.toUpperCase()} ${url}`);
                return result;
            }

            Logger.info("CacheService", `Cache miss for ${method.toUpperCase()} ${url}`);
            return null;
        } catch (error) {
            Logger.error("CacheService", "Failed to get cached response:", error);
            return null;
        }
    }

    async delete(url: string, method: string, headers?: Record<string, string>): Promise<void> {
        await this.ensureInitialized();
        if (!this.db) return;

        const key = this.generateCacheKey(url, method, headers);

        try {
            await this.db.runAsync("DELETE FROM cache_entries WHERE key = ?", [key]);
            Logger.info("CacheService", `Deleted cache entry for ${method.toUpperCase()} ${url}`);
        } catch (error) {
            Logger.error("CacheService", "Failed to delete cached response:", error);
        }
    }

    async clear(): Promise<void> {
        await this.ensureInitialized();
        if (!this.db) return;

        try {
            await this.db.runAsync("DELETE FROM cache_entries");
            Logger.info("CacheService", "Cache cleared");
        } catch (error) {
            Logger.error("CacheService", "Failed to clear cache:", error);
        }
    }

    async getCacheSize(): Promise<number> {
        await this.ensureInitialized();
        if (!this.db) return 0;

        try {
            const result = await this.db.getFirstAsync<{ count: number }>(
                "SELECT COUNT(*) as count FROM cache_entries"
            );
            return result?.count || 0;
        } catch (error) {
            Logger.error("CacheService", "Failed to get cache size:", error);
            return 0;
        }
    }

    async getCacheStats(): Promise<{ totalEntries: number }> {
        await this.ensureInitialized();
        if (!this.db) {
            return { totalEntries: 0 };
        }

        try {
            const total = await this.db.getFirstAsync<{ count: number }>("SELECT COUNT(*) as count FROM cache_entries");
            const totalEntries = total?.count || 0;

            return { totalEntries };
        } catch (error) {
            Logger.error("CacheService", "Failed to get cache stats:", error);
            return { totalEntries: 0 };
        }
    }

    async setQueryCache(queryKey: string, data: any): Promise<void> {
        await this.ensureInitialized();
        if (!this.db) return;

        const timestamp = Date.now();

        try {
            await this.db.runAsync(
                "INSERT OR REPLACE INTO query_cache_entries (queryKey, data, timestamp) VALUES (?, ?, ?)",
                [queryKey, JSON.stringify(data), timestamp]
            );

            Logger.info("CacheService", `Cached query data for key: ${queryKey}`);
        } catch (error) {
            Logger.error("CacheService", "Failed to cache query data:", error);
        }
    }

    async getQueryCache(queryKey: string): Promise<any | null> {
        await this.ensureInitialized();
        if (!this.db) return null;

        try {
            const result = await this.db.getFirstAsync<QueryCacheEntry>(
                "SELECT * FROM query_cache_entries WHERE queryKey = ?",
                [queryKey]
            );

            if (result) {
                Logger.info("CacheService", `Query cache hit for key: ${queryKey}`);
                return JSON.parse(result.data);
            }

            Logger.info("CacheService", `Query cache miss for key: ${queryKey}`);
            return null;
        } catch (error) {
            Logger.error("CacheService", "Failed to get cached query data:", error);
            return null;
        }
    }

    async removeQueryCache(queryKey: string): Promise<void> {
        await this.ensureInitialized();
        if (!this.db) return;

        try {
            await this.db.runAsync("DELETE FROM query_cache_entries WHERE queryKey = ?", [queryKey]);
            Logger.info("CacheService", `Deleted query cache entry for key: ${queryKey}`);
        } catch (error) {
            Logger.error("CacheService", "Failed to delete cached query data:", error);
        }
    }
}

export const cacheService = CacheService.getInstance();
