import { Subject, catchError, filter, of, share } from "rxjs";

import { TypeBottomSheet } from "@/shared/constants";

class ShowBottomSheetObs {
    private static subject$ = new Subject<ShowBottomSheetObsPayload<TypeBottomSheet>>();
    private static readonly stream$ = ShowBottomSheetObs.subject$.pipe(
        filter((payload): payload is ShowBottomSheetObsPayload<TypeBottomSheet> => {
            if (!payload) return false;
            return true;
        }),
        catchError((error) => {
            console.error("ShowBottomSheetObs stream error:", error);
            return of(null);
        }),
        share()
    );

    static subscribe(callback: (payload: ShowBottomSheetObsPayload<TypeBottomSheet>) => void) {
        return ShowBottomSheetObs.stream$
            .pipe(filter((payload): payload is ShowBottomSheetObsPayload<TypeBottomSheet> => payload !== null))
            .subscribe({
                next: callback,
                error: (error) => {
                    console.error("ShowBottomSheetObs subscription error:", error);
                }
            });
    }

    static action(params?: ShowBottomSheetObsPayload<TypeBottomSheet>): void {
        try {
            ShowBottomSheetObs.subject$.next(params || {});
        } catch (error) {
            console.error("ShowBottomSheetObs action error:", error);
        }
    }
}

export default ShowBottomSheetObs;

declare global {
    export type ShowBottomSheetObsPayload<T extends TypeBottomSheet> = T extends TypeBottomSheet.BIN_PRESS
        ? ModalObsCommonPayload<T> & {
              type: T;
              height?: number;
              binId: number;
          }
        : ModalObsCommonPayload<T>;

    export type ModalObsCommonPayload<T extends TypeBottomSheet> = {
        type?: T;
        title?: string;
        message?: string;
        titleButtonConfirm?: string;
        titleButtonCancel?: string;
        closeOnDragDown?: boolean;
        closeOnPressMask?: boolean;
        onConfirm?: () => void;
        onCancel?: () => void;
        height?: number;
    };
}
