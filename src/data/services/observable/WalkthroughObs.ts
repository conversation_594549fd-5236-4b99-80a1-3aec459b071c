import { Subject } from "rxjs";

export enum WalkthroughStep {
    HOME = "home",
    SCAN = "scan",
    TOOK_PHOTO = "took_photo",
    POINTS = "points"
}

class WalkthroughService {
    private walkthroughSubject = new Subject<{
        show: boolean;
        currentStep?: WalkthroughStep;
    }>();

    public walkthrough$ = this.walkthroughSubject.asObservable();
    private currentStep: WalkthroughStep = WalkthroughStep.HOME;

    public showWalkthrough = (show: boolean, step?: WalkthroughStep): void => {
        if (step) {
            this.currentStep = step;
        }
        this.walkthroughSubject.next({ show, currentStep: this.currentStep });
    };

    public hideWalkthrough = (): void => {
        this.walkthroughSubject.next({ show: false });
    };

    public nextStep(): WalkthroughStep | null {
        const steps = [WalkthroughStep.HOME, WalkthroughStep.SCAN, WalkthroughStep.TOOK_PHOTO, WalkthroughStep.POINTS];
        const currentIndex = steps.indexOf(this.currentStep);
        if (currentIndex < steps.length - 1) {
            this.currentStep = steps[currentIndex + 1];
            return this.currentStep;
        }
        return null;
    }

    public previousStep(): WalkthroughStep | null {
        const steps = [WalkthroughStep.HOME, WalkthroughStep.SCAN, WalkthroughStep.TOOK_PHOTO, WalkthroughStep.POINTS];
        const currentIndex = steps.indexOf(this.currentStep);
        if (currentIndex > 0) {
            this.currentStep = steps[currentIndex - 1];
            return this.currentStep;
        }
        return null;
    }

    public getCurrentStep(): WalkthroughStep {
        return this.currentStep;
    }

    public setCurrentStep(step: WalkthroughStep): void {
        this.currentStep = step;
    }
}

export const walkthroughService = new WalkthroughService();
