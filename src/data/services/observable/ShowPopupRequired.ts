import { Subject, catchError, of, share } from "rxjs";

import { TypePopup } from "@/presentation/components/popup";

class ShowPopupRequiredObs {
    private static subject$ = new Subject<ShowPopupRequiredObsPayload<TypePopup> | null>();
    private static readonly stream$ = ShowPopupRequiredObs.subject$.pipe(
        catchError(() => {
            return of(null);
        }),
        share()
    );

    static subscribe(callback: (payload: ShowPopupRequiredObsPayload<TypePopup> | null) => void) {
        return ShowPopupRequiredObs.stream$.subscribe({
            next: callback
        });
    }

    static action(payload?: ShowPopupRequiredObsPayload<TypePopup> | null): void {
        try {
            ShowPopupRequiredObs.subject$.next(payload || null);
        } catch (error) {
            /* empty */
        }
    }
}

export default ShowPopupRequiredObs;

declare global {
    export type ShowPopupRequiredObsPayload<T extends TypePopup> = T extends TypePopup.ADVERTISE_POPUP
        ? ShowPopupRequiredObsCommonPayload<T> & {
              type: T;
              banner?: BannerResponse;
          }
        : ShowPopupRequiredObsCommonPayload<T>;

    export type ShowPopupRequiredObsCommonPayload<T extends TypePopup> = {
        type?: T;
    };
}
