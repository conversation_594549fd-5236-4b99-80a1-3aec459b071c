import { Subject, filter, share } from "rxjs";

class WebViewObs {
    private static subject$ = new Subject<WebViewObsPayload>();
    private static readonly stream$ = WebViewObs.subject$.pipe(
        filter((payload): payload is WebViewObsPayload => {
            if (!payload) return false;
            if (!payload.uri || typeof payload.uri !== "string") {
                console.warn("WebViewObs: Invalid URI");
                return false;
            }
            return true;
        }),
        share()
    );

    static subscribe(callback: (payload: WebViewObsPayload) => void) {
        return WebViewObs.stream$.subscribe({
            next: callback,
            error: (error) => {
                console.error("WebViewObs error:", error);
            }
        });
    }

    static action(params: WebViewObsPayload): void {
        try {
            WebViewObs.subject$.next(params);
        } catch (error) {
            console.error("WebViewObs action error:", error);
        }
    }

    static complete(): void {
        WebViewObs.subject$.complete();
    }
}

export default WebViewObs;

declare global {
    export type WebViewObsPayload = {
        uri: string;
        title?: string;
        onClose?: () => void;
    };
}
