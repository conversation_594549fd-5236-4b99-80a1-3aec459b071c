import { HttpStatusCode } from "axios";
import { Alert } from "react-native";

import { networkService } from "../network/NetworkService";

import useShowToast from "@/presentation/hooks/useShowToast";
import Logger from "@/shared/helper/logger";

export const apiProblem = <T extends Data>(response: ErrorResponse<T>): ErrorResponse<T> => {
    try {
        const errorResponse: ErrorResponse<T> = {
            ok: false,
            data: response.data as T,
            status: response.status
        };
        showErrorDialog(errorResponse);
        return errorResponse;
    } catch (error) {
        if (__DEV__) {
            Logger.error("ApiProblem", "Unexpected error:", error);
        }
        const unexpectedErrorResponse: ErrorResponse<T> = {
            ok: false,
            data: (response.data ?? "An unexpected error occurred") as T,
            status: HttpStatusCode.InternalServerError
        };
        showErrorDialog(unexpectedErrorResponse);
        return unexpectedErrorResponse;
    }
};

let showToast: ReturnType<typeof useShowToast> | null = null;

export const setToastInstance = (toastInstance: ReturnType<typeof useShowToast> | null) => {
    showToast = toastInstance;
};

const showErrorDialog = <T extends Data>(errorResponse: ErrorResponse<T>) => {
    if (__DEV__) {
        console.error("Error occurred:", JSON.stringify(errorResponse.data, null, 2));
    }

    if (networkService.isOffline()) {
        Logger.info("HttpProblem", "Suppressing error dialog while offline", errorResponse);
        return;
    }

    let errorMessage = "An unexpected error occurred";

    if (errorResponse.data) {
        if (typeof errorResponse.data === "object" && errorResponse.data !== null) {
            const data = errorResponse.data as any;

            if (data.errors && typeof data.errors === "object") {
                const fieldErrors = Object.entries(data.errors)
                    .filter(([_, val]) => val !== null && val !== undefined)
                    .map(([field, val]) => {
                        if (Array.isArray(val)) {
                            return val.map((msg) => `${field}: ${msg}`).join("\n");
                        }
                        return `${field}: ${String(val)}`;
                    })
                    .filter((msg) => msg.length > 0);

                if (fieldErrors.length > 0) {
                    errorMessage = fieldErrors.join("\n");
                    return showToast?.showError(errorMessage);
                }
            }

            if (data.message) {
                if (typeof data.message === "object" && data.message !== null) {
                    const fieldErrors = Object.values(data.message)
                        .filter((val) => val !== null && val !== undefined)
                        .map((val) => {
                            if (Array.isArray(val)) {
                                return val.join("\n");
                            }
                            return String(val);
                        })
                        .filter((msg) => msg.length > 0);
                    if (fieldErrors.length > 0) {
                        errorMessage = fieldErrors.join("\n");
                    }
                } else {
                    errorMessage = String(data.message);
                }
            } else if (data.error) {
                errorMessage = String(data.error);
            }
        } else if (typeof errorResponse.data === "string") {
            errorMessage = errorResponse.data;
        }
    }

    Alert.alert("Error", errorMessage);

    // showToast?.showError(errorMessage);
};

declare global {
    type Data = Record<string, any> | string | boolean | undefined | void;

    type SuccessfulResponse<D extends Data, S = HttpStatusCode> = {
        ok: true;
        data?: D;
        status?: S;
    };

    type ErrorResponse<D extends Data, S = HttpStatusCode> = {
        ok: false;
        data: D;
        status?: S;
    };
    type BaseResponse<D extends Data> = SuccessfulResponse<D> | ErrorResponse<D> | undefined;
}
