import { debounce } from "radash";

import { resetAllStores } from "@/app/store";

import { FirebaseAuthService } from "../../firebase";
import { RootNavigator } from "../../navigation";
import { networkService } from "../../network/NetworkService";
import ApiMethod from "../ApiMethod";
import { HttpClient } from "../HttpClient";
import { ITokenService, Session } from "../interfaces/IHttpClient";

import { EndPoint, RouteName } from "@/shared/constants";
import { clearToken, getAccessTokenExpiration, getToken, setToken } from "@/shared/helper";
import Logger from "@/shared/helper/logger";

export class TokenService implements ITokenService {
    private readonly maxTimeout = 2 * 60 * 60 * 1000;
    private readonly refreshBuffer = 30 * 1000;
    private tokenRefreshDebounced: ReturnType<typeof debounce> | null = null;
    private readonly httpClient: HttpClient;

    constructor(httpClient: HttpClient) {
        this.httpClient = httpClient;
    }

    async setSession(session: Session): Promise<void> {
        this.httpClient.setAccessToken(session.accessToken);

        let expiresAt: Date | undefined | null = session.accessTokenExpiresAt;

        if (expiresAt && !(expiresAt instanceof Date)) {
            try {
                expiresAt = new Date(expiresAt);
            } catch (error) {
                Logger.error("TokenService", "Invalid date format for accessTokenExpiresAt", error);
                expiresAt = null;
            }
        }

        await setToken({
            refreshToken: session.refreshToken,
            accessTokenExpiresAt: expiresAt
        });

        if (!expiresAt) return;

        this.scheduleTokenRefresh(expiresAt);
    }

    private scheduleTokenRefresh(expirationDate: Date): void {
        if (this.tokenRefreshDebounced) {
            this.tokenRefreshDebounced.cancel();
            this.tokenRefreshDebounced = null;
        }

        const now = new Date();
        const expirationTime = new Date(expirationDate).getTime();
        const timeUntilExpiration = expirationTime - now.getTime();

        if (timeUntilExpiration <= this.refreshBuffer) {
            this.refreshToken();
            return;
        }

        const delay = Math.min(timeUntilExpiration - this.refreshBuffer, this.maxTimeout);

        this.tokenRefreshDebounced = debounce({ delay }, () => {
            const currentTime = new Date().getTime();
            const remainingTime = expirationTime - currentTime - this.refreshBuffer;

            if (remainingTime <= 0) {
                this.refreshToken();
            } else if (remainingTime <= this.maxTimeout) {
                if (this.tokenRefreshDebounced) this.tokenRefreshDebounced.cancel();
                this.tokenRefreshDebounced = debounce({ delay: remainingTime }, () => this.refreshToken());
                this.tokenRefreshDebounced();
            } else {
                this.scheduleTokenRefresh(expirationDate);
            }
        });

        this.tokenRefreshDebounced();
    }

    async clearSession(): Promise<void> {
        if (this.tokenRefreshDebounced) {
            this.tokenRefreshDebounced.cancel();
            this.tokenRefreshDebounced = null;
        }

        await setToken({
            refreshToken: null,
            accessTokenExpiresAt: null
        });
        this.httpClient.clearSession();
    }

    async getRefreshToken(): Promise<string | null> {
        const token = await getToken();
        return token ?? null;
    }

    async refreshToken(): Promise<boolean> {
        try {
            const refreshToken = await this.getRefreshToken();
            if (!refreshToken) return false;

            if (networkService.isOffline()) {
                const expiresAt = await getAccessTokenExpiration();
                if (expiresAt && expiresAt > new Date()) {
                    Logger.info("TokenService", "Token still valid, allowing offline access");
                    return true;
                }
                Logger.info("TokenService", "Token expired and offline, denying access");
                return false;
            }

            const response = await this.httpClient.request<{
                data: LoginResponse;
            }>({
                endpoint: EndPoint.auth.refreshToken,
                method: ApiMethod.POST,
                body: {
                    refresh_token: refreshToken
                }
            });

            if (!response?.ok) {
                await FirebaseAuthService.logout();
                await clearToken();
                this.httpClient.clearSession();
                resetAllStores();
                RootNavigator.replaceName(RouteName.Welcome);
                return false;
            }

            const data = response.data?.data;
            const accessTokenExpiresAt = data?.access_token_expires_at
                ? new Date(data.access_token_expires_at)
                : undefined;
            const refreshTokenExpiresAt = data?.refresh_token_expires_at
                ? new Date(data.refresh_token_expires_at)
                : undefined;

            await this.setSession({
                accessToken: data?.access_token,
                refreshToken: data?.refresh_token,
                accessTokenExpiresAt,
                refreshTokenExpiresAt
            });
            return true;
        } catch (e) {
            Logger.error("TokenService", "Error refreshing token:", e);

            if (networkService.isOffline()) {
                const expiresAt = await getAccessTokenExpiration();
                if (expiresAt && expiresAt > new Date()) {
                    Logger.info("TokenService", "Network error but token still valid, allowing offline access");
                    return true;
                }
            }

            await this.clearSession();
            return false;
        }
    }

    async logout(): Promise<void> {
        const logoutResponse = await this.httpClient.request<void>({
            endpoint: EndPoint.auth.logout,
            method: ApiMethod.GET
        });

        if (!logoutResponse?.ok) return;
        await FirebaseAuthService.logout();
        await clearToken();
        this.httpClient.clearSession();
        resetAllStores();
        RootNavigator.replaceName(RouteName.Welcome);
    }
}
