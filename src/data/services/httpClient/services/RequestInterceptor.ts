import { AxiosError, AxiosInstance, AxiosResponse, HttpStatusCode } from "axios";
import { Alert } from "react-native";

import { resetAllStores } from "@/app/store";

import { FirebaseAuthService } from "../../firebase";
import { RootNavigator } from "../../navigation";

import { TokenService } from "./TokenService";

import { EndPoint, RouteName } from "@/shared/constants";
import { clearToken } from "@/shared/helper";

interface ErrorResponseData {
    message: string;
    status?: number;
}

export class RequestInterceptor {
    constructor(
        private readonly axiosInstance: AxiosInstance,
        private readonly tokenService: TokenService
    ) {}

    setupInterceptors(): void {
        this.axiosInstance.interceptors.request.use(this.handleRequest.bind(this), this.handleRequestError.bind(this));

        this.axiosInstance.interceptors.response.use(this.handleResponse.bind(this), async (error: AxiosError) => {
            if (this.isTokenExpiredError(error)) {
                try {
                    await FirebaseAuthService.logout();
                    await clearToken();
                    this.tokenService.clearSession();
                    resetAllStores();
                    RootNavigator.replaceName(RouteName.Welcome);
                } catch (refreshError) {
                    /* empty */
                }
            }

            if (this.isForbiddenLoginError(error)) {
                const errorData = error.response?.data as ErrorResponseData;
                const errorMessage = errorData?.message;

                Alert.alert("Update Required", errorMessage, [
                    {
                        text: "Continue",
                        onPress: async () => {
                            RootNavigator.navigate(RouteName.ForgotPassword, {
                                title: "Set New Password",
                                forOldUser: true
                            });
                        }
                    },
                    {
                        text: "Cancel"
                    }
                ]);

                return Promise.reject({
                    ...error,
                    message: errorMessage,
                    status: error.response?.status,
                    _dialogShown: true
                });
            }

            if (error.response?.data) {
                const errorData = error.response.data as ErrorResponseData;
                return Promise.reject({
                    ...error,
                    message: errorData.message,
                    status: error.response.status
                });
            }

            return Promise.reject(error);
        });
    }

    private async handleRequest(config: any) {
        return config;
    }

    private handleRequestError(error: AxiosError) {
        return Promise.reject(error);
    }

    private handleResponse(response: AxiosResponse) {
        return response;
    }

    private isTokenExpiredError(error: AxiosError): boolean {
        return error.response?.status === HttpStatusCode.Unauthorized;
    }

    private isForbiddenLoginError(error: AxiosError): boolean | undefined {
        return error.response?.status === HttpStatusCode.Forbidden && error.config?.url?.includes(EndPoint.auth.login);
    }
}
