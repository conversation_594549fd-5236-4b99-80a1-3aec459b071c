import { AxiosError, HttpStatusCode } from "axios";

import { apiProblem } from "../HttpProblem";

import Logger from "@/shared/helper/logger";

interface ErrorWithDialogFlag extends AxiosError {
    _dialogShown?: boolean;
}

export interface IErrorHandler {
    handleError(error: AxiosError): Promise<never>;
    extractErrorData(error: AxiosError): Error;
    handleOfflineError(): Promise<undefined>;
}

export class ErrorHandler implements IErrorHandler {
    async handleError(error: AxiosError): Promise<never> {
        const errorData = this.extractErrorData(error);
        Logger.error("HttpError", {
            status: error.response?.status,
            data: error.response?.data,
            config: {
                url: error.config?.url,
                method: error.config?.method,
                headers: error.config?.headers
            }
        });
        return Promise.reject(errorData);
    }

    extractErrorData(error: AxiosError): Error {
        const errorResponse: ErrorResponse<Data> = {
            ok: false,
            data: error.response?.data || error.message || "An unexpected error occurred",
            status: error.response?.status || HttpStatusCode.InternalServerError
        };

        const errorWithFlag = error as ErrorWithDialogFlag;
        if (errorWithFlag._dialogShown) {
            return new Error(JSON.stringify(errorResponse));
        }

        return new Error(JSON.stringify(apiProblem(errorResponse)));
    }

    async handleOfflineError(): Promise<undefined> {
        Logger.info("ErrorHandler", "Request failed: Device is offline, using cached data or showing offline state");
        return undefined;
    }
}
