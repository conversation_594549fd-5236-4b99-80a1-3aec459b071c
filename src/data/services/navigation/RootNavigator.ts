import { CommonActions, createNavigationContainerRef } from "@react-navigation/native";

import { INavigationService } from "./INavigationService";
import { NavigationLogger } from "./NavigationLogger";

import { RouteName } from "@/shared/constants";

export interface NavigatorParamsType {}

class RootNavigator implements INavigationService {
    public readonly navigationRef = createNavigationContainerRef();

    async navigate<RouteName extends keyof RootStackParamList, <PERSON><PERSON> extends RootStackParamList[RouteName]>(
        route: RouteName,
        params?: Param
    ): Promise<void> {
        if (!this.navigationRef.isReady()) return;

        NavigationLogger.logNavigation(route as string);

        return this.navigationRef.current?.dispatch(CommonActions.navigate(route, params));
    }

    goBack(): void {
        if (this.navigationRef.isReady()) {
            this.navigationRef.current?.dispatch(CommonActions.goBack());
        }
    }

    async replaceName<RouteName extends keyof RootStackParamList, <PERSON><PERSON> extends RootStackParamList[RouteName]>(
        route: RouteName,
        params?: Param
    ): Promise<void> {
        if (!this.navigationRef.isReady()) return;

        NavigationLogger.logReplace(route as string);

        return this.navigationRef.current?.dispatch(
            CommonActions.reset({
                index: 0,
                routes: [{ name: route, params: params }]
            })
        );
    }
}

export default new RootNavigator();

declare global {
    type DefaultStackParamList = Record<keyof typeof RouteName, NavigatorParamsType>;

    export type RootStackParamList = DefaultStackParamList & {
        [RouteName.Register]: {
            isSchool: boolean;
        };
        [RouteName.Verification]: {
            phoneNumber?: string;
            type: "register" | "login" | "forgotPassword" | "registerSchool";
        };
        [RouteName.SetNewPassword]: {
            phoneNumber?: string;
        };
        [RouteName.Home]: {
            fromWalkthrough?: boolean;
            fromWalkthroughBack?: boolean;
        };
        [RouteName.Scan]: {
            fromWalkthrough?: boolean;
            fromWalkthroughBack?: boolean;
        };
        [RouteName.TookPhoto]: {
            fromWalkthrough?: boolean;
            fromWalkthroughBack?: boolean;
            binId?: number;
        };
        [RouteName.ConfirmPhoto]: {
            photoPath?: string;
            binId?: number;
        };
        [RouteName.SubmittedRedeem]: {
            result?: RecyclingResponse;
        };
        [RouteName.EventDetail]: {
            eventId?: number;
        };
        [RouteName.RewardDetail]: {
            reward?: RewardDetailResponse | MyRewardResponse;
            myVoucher?: boolean;
        };
        [RouteName.RedeemPoint]: {
            reward?: RewardDetailResponse;
        };
        [RouteName.SetNewNickName]: {
            phoneNumber?: string;
            newPassword?: string;
        };
        [RouteName.SpecialEvent]: {
            eventId?: number;
        };
        [RouteName.AboutEvent]: {
            image?: string;
            title?: string;
            description?: string;
        };
        [RouteName.ForgotPassword]: {
            title?: string;
            forOldUser?: boolean;
        };
    };
}
