import NetInfo, { NetInfoState } from "@react-native-community/netinfo";
import { BehaviorSubject, Subject } from "rxjs";

export interface NetworkState {
    isConnected: boolean;
    isInternetReachable: boolean | null;
    type: string | null;
}

class NetworkService {
    private static instance: NetworkService;
    private networkState$ = new BehaviorSubject<NetworkState>({
        isConnected: true,
        isInternetReachable: null,
        type: null
    });

    private connectionChangeSubject$ = new Subject<NetworkState>();
    private isInitialized = false;

    private constructor() {}

    static getInstance(): NetworkService {
        if (!NetworkService.instance) {
            NetworkService.instance = new NetworkService();
        }
        return NetworkService.instance;
    }

    async initialize(): Promise<void> {
        if (this.isInitialized) return;

        try {
            const initialState = await NetInfo.fetch();
            this.updateNetworkState(initialState);

            NetInfo.addEventListener(this.handleNetworkStateChange.bind(this));

            this.isInitialized = true;
        } catch (error) {
            /* empty */
        }
    }

    private handleNetworkStateChange = (state: NetInfoState) => {
        this.updateNetworkState(state);
    };

    private updateNetworkState(state: NetInfoState) {
        const networkState: NetworkState = {
            isConnected: state.isConnected ?? false,
            isInternetReachable: state.isInternetReachable,
            type: state.type
        };

        const previousState = this.networkState$.value;
        this.networkState$.next(networkState);

        if (previousState.isConnected !== networkState.isConnected) {
            this.connectionChangeSubject$.next(networkState);
        }
    }

    isOnline(): boolean {
        return this.networkState$.value.isConnected;
    }

    isOffline(): boolean {
        return !this.isOnline();
    }

    getCurrentNetworkState(): NetworkState {
        return this.networkState$.value;
    }

    getNetworkState$() {
        return this.networkState$.asObservable();
    }

    getConnectionChanges$() {
        return this.connectionChangeSubject$.asObservable();
    }

    async refreshNetworkState(): Promise<NetworkState> {
        try {
            const state = await NetInfo.fetch();
            this.updateNetworkState(state);
            return this.getCurrentNetworkState();
        } catch (error) {
            return this.getCurrentNetworkState();
        }
    }
}

export const networkService = NetworkService.getInstance();
