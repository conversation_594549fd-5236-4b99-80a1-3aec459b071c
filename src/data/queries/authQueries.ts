import { useNavigation } from "@react-navigation/native";
import { useMutation } from "@tanstack/react-query";
import React from "react";

import { useLoginStore, useUserStore } from "@/app/store";

import { authApi, forgotPasswordApi } from "../api";
import { FirebaseAuthService } from "../services/firebase";
import { HttpClient } from "../services/httpClient";
import { RootNavigator } from "../services/navigation";
import { ShowBottomSheetObs } from "../services/observable";

import useShowToast from "@/presentation/hooks/useShowToast";
import { RouteName } from "@/shared/constants";

export const useRegisterIndividualQueries = () => {
    const { setGuestMode } = useUserStore();
    const { setLogin } = useLoginStore();

    const mutation = useMutation({
        mutationFn: authApi.registerIndividual,
        onSuccess: async (data) => {
            if (data?.ok) {
                setGuestMode(false);
                setLogin(data.data);
                RootNavigator.replaceName(RouteName.Bottom);
            }
        }
    });

    const registerIndividual = React.useCallback(
        async (registerRequest: RegisterRequest["individual"]) => {
            try {
                const result = await mutation.mutateAsync(registerRequest);
                if (result?.ok) {
                    result.data;
                }
            } catch (error) {
                console.error("Failed to register:", error);
            }
        },
        [mutation]
    );

    return {
        data: mutation.data,
        isLoading: mutation.isPending,
        error: mutation.error,
        registerIndividual
    };
};

export const useRegisterSchoolQueries = () => {
    const navigation = useNavigation();
    const mutation = useMutation({
        mutationFn: authApi.registerEntity,
        onSuccess: (data) => {
            if (data?.ok) {
                ShowBottomSheetObs.action({
                    title: "You have been registered",
                    message: "Please wait for account activation",
                    titleButtonConfirm: "Login",
                    closeOnDragDown: false,
                    closeOnPressMask: false,
                    onConfirm: () => {
                        navigation.replace(RouteName.Auth, { screen: RouteName.Login });
                    }
                });
            }
        }
    });

    const registerSchool = React.useCallback(
        async (registerRequest: RegisterRequest["school"]) => {
            try {
                const result = await mutation.mutateAsync(registerRequest);
                if (result?.ok) {
                    result.data;
                }
            } catch (error) {
                console.error("Failed to register:", error);
            }
        },
        [mutation]
    );

    return {
        data: mutation.data,
        isLoading: mutation.isPending,
        error: mutation.error,
        registerSchool
    };
};

export const useLoginQueries = () => {
    const { setGuestMode } = useUserStore();
    const { setLogin } = useLoginStore();

    const mutation = useMutation({
        mutationFn: authApi.login,
        onSuccess: async (data) => {
            if (data?.ok) {
                setGuestMode(false);
                setLogin(data.data);
                RootNavigator.replaceName(RouteName.Bottom);
            }
        }
    });

    const login = React.useCallback(
        async ({
            login: loginField,
            password,
            isOrganization
        }: {
            login: string;
            password: string;
            isOrganization: boolean;
        }) => {
            try {
                mutation.mutateAsync({ login: loginField, password, isOrganization });
            } catch (error) {
                return;
            }
        },
        [mutation]
    );

    return {
        data: mutation.data,
        isLoading: mutation.isPending,
        error: mutation.error,
        login
    };
};

export const useLogoutQueries = () => {
    const [isLoading, setIsLoading] = React.useState(false);

    const logout = React.useCallback(async () => {
        setIsLoading(true);
        try {
            await FirebaseAuthService.logout();
            await HttpClient.getTokenService().logout();
        } catch (error) {
            /* empty */
        } finally {
            setIsLoading(false);
        }
    }, []);

    return {
        isLoading,
        logout
    };
};

export const useUpdatePasswordQueries = () => {
    const showToast = useShowToast();
    const navigation = useNavigation();

    const mutation = useMutation({
        mutationFn: forgotPasswordApi.resetPassword,
        onSuccess: (data) => {
            if (data?.ok) {
                navigation.replace(RouteName.Auth, { screen: RouteName.Login });
                showToast.showSuccess("Password reset successfully");
            }
        }
    });

    const updatePassword = React.useCallback(
        async (forgotPasswordRequest: ForgotPasswordRequest) => {
            try {
                mutation.mutateAsync(forgotPasswordRequest);
            } catch (error) {
                return;
            }
        },
        [mutation]
    );

    return {
        data: mutation.data,
        isLoading: mutation.isPending,
        error: mutation.error,
        updatePassword
    };
};

export const useAddressQueries = () => {
    const mutation = useMutation({
        mutationFn: authApi.getAddressByPostalCode
    });

    const getAddressByPostalCode = React.useCallback(
        async (postalCode: string) => {
            try {
                const result = await mutation.mutateAsync(postalCode);
                if (result?.ok) {
                    return result.data;
                }
            } catch (error) {
                /* empty */
            }
        },
        [mutation]
    );

    return {
        ...mutation,
        isLoading: mutation.isPending,
        getAddressByPostalCode
    };
};
