import { useQuery } from "@tanstack/react-query";

import { useUserStore } from "@/app/store";

import { pointApi } from "../api";

import { defaultQueryConfig } from "./queryConfig";

export const usePointQueries = () => {
    const { isGuestMode } = useUserStore();

    const query = useQuery({
        queryKey: ["pointHistory"],
        queryFn: pointApi.getPointHistory,
        enabled: !isGuestMode(),
        ...defaultQueryConfig
    });

    return {
        isLoading: query.isLoading,
        pointHistory: query.data?.data
    };
};
