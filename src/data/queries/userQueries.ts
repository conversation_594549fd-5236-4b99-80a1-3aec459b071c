import { useNavigation } from "@react-navigation/native";
import { useMutation, useQuery } from "@tanstack/react-query";
import React from "react";

import { useUserStore } from "@/app/store";

import { userApi } from "../api";

import { defaultQueryConfig } from "./queryConfig";

import { RewardStatusType, RouteName } from "@/shared/constants";

export const useUserQueries = () => {
    const { setCurrentUser, user, isGuestMode } = useUserStore();

    const mutation = useMutation({
        mutationFn: userApi.getCurrentUser,
        onSuccess: (data) => {
            if (data?.ok && data.data) {
                setCurrentUser(data.data);
            }
        }
    });

    const getUser = React.useCallback(async () => {
        if (isGuestMode()) return;
        try {
            const result = await mutation.mutateAsync();
            return result?.ok;
        } catch (error) {
            /* empty */
        }
    }, [mutation, isGuestMode]);

    return {
        isLoading: mutation.isPending,
        user,
        getUser,
        isGuestMode
    };
};

export const useUpdateUserQueries = () => {
    const { setCurrentUser } = useUserStore();

    const mutation = useMutation({
        mutationFn: userApi.updateUser
    });

    const updateUser = React.useCallback(
        async (user: UpdateUserRequest) => {
            try {
                const result = await mutation.mutateAsync({ user });
                if (result?.ok) {
                    setCurrentUser(result.data);
                }
            } catch (error) {
                /* empty */
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [mutation]
    );

    return {
        ...mutation,
        isLoading: mutation.isPending,
        updateUser
    };
};

export const useMyRewardQueries = () => {
    const [myReward, setMyReward] = React.useState<RewardResponse[] | undefined>(undefined);

    const mutation = useMutation({
        mutationFn: userApi.getMyVoucher,
        onSuccess: (data) => {
            if (data?.ok) {
                setMyReward(data.data);
            }
        }
    });

    const getMyReward = React.useCallback(
        async (status: RewardStatusType) => mutation.mutateAsync({ status }),

        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    return {
        ...mutation,
        isLoading: mutation.isPending,
        getMyReward,
        myReward
    };
};

export const useMyRewardDetailQueries = () => {
    const navigation = useNavigation();

    const mutation = useMutation({
        mutationFn: userApi.getRewardDetail,
        onSuccess: (data) => {
            if (data?.ok) {
                navigation.navigate(RouteName.RewardDetail, {
                    reward: data.data as MyRewardResponse,
                    myVoucher: true
                });
            }
        }
    });

    const getRewardDetail = React.useCallback(
        async (id: number) => mutation.mutateAsync({ id }),

        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    return {
        ...mutation,
        isLoading: mutation.isPending,
        getRewardDetail
    };
};

export const usePointGoalQueries = () => {
    const { setPointGoal, pointGoal, isGuestMode } = useUserStore();

    const mutation = useMutation({
        mutationFn: userApi.getPointGoal,
        onSuccess: (data) => {
            if (data?.ok && data.data) {
                setPointGoal(data.data);
            }
        }
    });

    const getPointGoal = React.useCallback(async () => {
        if (isGuestMode()) return;
        try {
            const result = await mutation.mutateAsync();
            return result?.ok;
        } catch (error) {
            /* empty */
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isGuestMode]);

    return {
        isLoading: mutation.isPending,
        getPointGoal,
        pointGoal
    };
};

export const useEventJoinedQueries = () => {
    const [currentEventId, setCurrentEventId] = React.useState<number | undefined>(undefined);

    const query = useQuery({
        queryKey: ["eventJoined", currentEventId],
        queryFn: () => userApi.getEventsJoined({ eventId: currentEventId! }),
        enabled: !!currentEventId,
        ...defaultQueryConfig
    });

    const getEventsJoined = React.useCallback((eventId: number) => {
        setCurrentEventId(eventId);
    }, []);

    return {
        isLoading: query.isLoading,
        getEventsJoined,
        eventJoined: query.data?.data
    };
};

export const useEventsJoinedAllQueries = () => {
    const { isGuestMode } = useUserStore();

    const query = useQuery({
        queryKey: ["eventsJoinedAll"],
        queryFn: userApi.getEventsJoinedAll,
        enabled: !isGuestMode()
    });

    const getEventsJoinedAll = React.useCallback(async () => {
        if (isGuestMode()) return;
        await query.refetch();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return {
        isLoading: query.isLoading,
        getEventsJoinedAll,
        eventsJoinedAll: query.data?.data
    };
};
