import { useQuery } from "@tanstack/react-query";
import React from "react";

import { bannerApi } from "../api";
import { ShowPopupRequiredObs } from "../services/observable";

import { defaultQueryConfig } from "./queryConfig";

import { TypePopup } from "@/presentation/components/popup";
import { BannerTypes } from "@/shared/constants/BannerTypes";

export const useBannerQueries = () => {
    const homePageQuery = useQuery({
        queryKey: ["banner", BannerTypes.HOMEPAGE],
        queryFn: () => bannerApi.getBanner(BannerTypes.HOMEPAGE),
        enabled: false,
        ...defaultQueryConfig
    });

    const popupHomePageQuery = useQuery({
        queryKey: ["banner", BannerTypes.POPUP_HOMEPAGE],
        queryFn: () => bannerApi.getBanner(BannerTypes.POPUP_HOMEPAGE),
        enabled: false,
        ...defaultQueryConfig
    });

    const searchPageQuery = useQuery({
        queryKey: ["banner", BannerTypes.SEARCH_PAGE],
        queryFn: () => bannerApi.getBanner(BannerTypes.SEARCH_PAGE),
        enabled: false,
        ...defaultQueryConfig
    });

    const successRecycleQuery = useQuery({
        queryKey: ["banner", BannerTypes.SUCCESS_RECYCLE],
        queryFn: () => bannerApi.getBanner(BannerTypes.SUCCESS_RECYCLE),
        enabled: false,
        ...defaultQueryConfig
    });

    const rewardQuery = useQuery({
        queryKey: ["banner", BannerTypes.REWARD],
        queryFn: () => bannerApi.getBanner(BannerTypes.REWARD),
        enabled: false,
        ...defaultQueryConfig
    });

    const accountQuery = useQuery({
        queryKey: ["banner", BannerTypes.ACCOUNT],
        queryFn: () => bannerApi.getBanner(BannerTypes.ACCOUNT),
        enabled: false,
        ...defaultQueryConfig
    });

    const getBannerHomePage = React.useCallback(async () => {
        const response = await homePageQuery.refetch();
        return response.data?.ok ? response.data.data : undefined;
    }, [homePageQuery]);

    const getBannerPopupHomePage = React.useCallback(async () => {
        try {
            const response = await popupHomePageQuery.refetch();
            if (response.data?.ok) {
                ShowPopupRequiredObs.action({
                    type: TypePopup.ADVERTISE_POPUP,
                    banner: response.data.data
                });
            }
        } catch (error) {
            /* empty */
        }
    }, [popupHomePageQuery]);

    const getBannerSearchPage = React.useCallback(async () => {
        const response = await searchPageQuery.refetch();
        return response.data?.ok ? response.data.data : undefined;
    }, [searchPageQuery]);

    const getBannerSuccessRecycle = React.useCallback(async () => {
        const response = await successRecycleQuery.refetch();
        return response.data?.ok ? response.data.data : undefined;
    }, [successRecycleQuery]);

    const getBannerReward = React.useCallback(async () => {
        const response = await rewardQuery.refetch();
        return response.data?.ok ? response.data.data : undefined;
    }, [rewardQuery]);

    const getBannerAccount = React.useCallback(async () => {
        const response = await accountQuery.refetch();
        return response.data?.ok ? response.data.data : undefined;
    }, [accountQuery]);

    const bannerHomePage = homePageQuery.data?.data;
    const bannerSearchPage = searchPageQuery.data?.data;
    const bannerSuccessRecycle = successRecycleQuery.data?.data;
    const bannerReward = rewardQuery.data?.data;
    const bannerAccount = accountQuery.data?.data;

    const isLoading =
        homePageQuery.isLoading ||
        popupHomePageQuery.isLoading ||
        searchPageQuery.isLoading ||
        successRecycleQuery.isLoading ||
        rewardQuery.isLoading ||
        accountQuery.isLoading;

    return {
        isLoading,
        bannerHomePage,
        bannerSearchPage,
        bannerSuccessRecycle,
        bannerReward,
        bannerAccount,
        getBannerHomePage,
        getBannerPopupHomePage,
        getBannerSearchPage,
        getBannerSuccessRecycle,
        getBannerReward,
        getBannerAccount
    };
};
