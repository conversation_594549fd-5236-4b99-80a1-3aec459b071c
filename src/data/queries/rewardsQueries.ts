import { useNavigation } from "@react-navigation/native";
import { useMutation } from "@tanstack/react-query";
import React from "react";

import { rewardsApi } from "../api";

import { RouteName } from "@/shared/constants";

const PAGE_SIZE = 10;

export const hasMoreRewards = (data?: RewardResponse[]): boolean => {
    if (!data || data.length === 0) {
        return false;
    }

    if (data.length < PAGE_SIZE) {
        return false;
    }

    return data.length >= PAGE_SIZE;
};

export const useRewardsQueries = () => {
    const [rewards, setRewards] = React.useState<RewardResponse[]>([]);
    const [page, setPage] = React.useState(1);
    const [hasMore, setHasMore] = React.useState(true);
    const [isLoadingMore, setIsLoadingMore] = React.useState(false);
    const [isRefreshing, setIsRefreshing] = React.useState(false);

    const mutation = useMutation({
        mutationFn: rewardsApi.getAll,
        onSuccess: (data) => {
            if (data?.ok && Array.isArray(data.data)) {
                const newRewards = data.data ?? [];
                setRewards((prev) => [...prev, ...newRewards]);
                setHasMore(hasMoreRewards(newRewards));
            } else {
                setHasMore(false);
            }
            setIsLoadingMore(false);
            setIsRefreshing(false);
        },
        onError: () => {
            setHasMore(false);
            setIsLoadingMore(false);
        }
    });

    React.useEffect(() => {
        setPage(1);
        setHasMore(true);
        setRewards([]);
        mutation.mutate({ page: 1 });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const fetchNextPage = React.useCallback(async () => {
        if (mutation.isPending || !hasMore || isLoadingMore) return;

        setIsLoadingMore(true);
        const nextPage = page + 1;
        setPage(nextPage);
        mutation.mutate({ page: nextPage });
    }, [hasMore, isLoadingMore, mutation, page]);

    const refetch = React.useCallback(() => {
        setPage(1);
        setHasMore(true);
        setRewards([]);
        setIsRefreshing(true);
        mutation.mutate({ page: 1 });
    }, [mutation]);

    return {
        ...mutation,
        isLoading: mutation.isPending && !isRefreshing && page === 1,
        isLoadingMore,
        rewards,
        hasMore,
        fetchNextPage,
        page,
        refetch
    };
};

export const useRewardDetailQueries = () => {
    const navigation = useNavigation();

    const mutation = useMutation({
        mutationFn: rewardsApi.getDetail,
        onSuccess: (data) => {
            if (data?.ok) {
                navigation.navigate(RouteName.RewardDetail, { reward: data.data as RewardDetailResponse });
            }
        }
    });

    const fetchRewardDetail = React.useCallback(async (id: number) => mutation.mutateAsync({ id }), [mutation]);

    return {
        ...mutation,
        isLoading: mutation.isPending,
        fetchRewardDetail
    };
};

export const useRewardRedeemQueries = () => {
    const mutation = useMutation({
        mutationFn: rewardsApi.redeem
    });

    const fetchRewardRedeem = React.useCallback(
        async (id: number) => {
            const result = await mutation.mutateAsync({ id });
            return result;
        },
        [mutation]
    );

    return {
        ...mutation,
        isLoading: mutation.isPending,
        fetchRewardRedeem
    };
};
