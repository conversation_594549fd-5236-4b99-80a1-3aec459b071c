import { useNavigation } from "@react-navigation/native";
import { useMutation, useQuery } from "@tanstack/react-query";
import React from "react";

import { eventApi } from "../api";

import { defaultQueryConfig } from "./queryConfig";
import { useEventsJoinedAllQueries } from "./userQueries";

import { useLocationServices } from "@/presentation/hooks/useLocationServices";
import { EventTypes, RouteName } from "@/shared/constants";
import { EventTypeK } from "@/shared/constants/EventTypes";

export const useEventTypesQueries = () => {
    const query = useQuery({
        queryKey: ["eventTypes"],
        queryFn: eventApi.getAllEventTypes,
        ...defaultQueryConfig
    });

    return {
        eventTypes: query.data?.data,
        isLoading: query.isLoading
    };
};

export const useEventListQueries = () => {
    const [currentEventTypeId, setCurrentEventTypeId] = React.useState<EventTypeK | undefined>(undefined);
    const [currentDistrictId, setCurrentDistrictId] = React.useState<number | undefined>(undefined);
    const { userLocation } = useLocationServices();
    const { eventsJoinedAll, getEventsJoinedAll, isLoading: isLoadingEventsJoinedAll } = useEventsJoinedAllQueries();

    const query = useQuery({
        queryKey: [`events-${currentEventTypeId}`, currentEventTypeId, currentDistrictId],
        queryFn: () =>
            eventApi.getAllEvents({
                eventTypeId: currentEventTypeId!,
                districtId: currentDistrictId,
                lat: userLocation?.latitude,
                long: userLocation?.longitude
            }),
        enabled: !!currentEventTypeId && !!userLocation,
        ...defaultQueryConfig
    });

    const mergedEventsJoinedAll = React.useMemo(() => {
        if (currentEventTypeId !== EventTypes.ALBA_EVENT || !eventsJoinedAll) {
            return undefined;
        }

        return eventsJoinedAll.map(
            (event): EventResponse => ({
                id: event.id,
                code: event.code,
                name: event.name,
                date_start: event.date_start,
                date_end: event.date_end,
                time_start: event.time_start,
                time_end: event.time_end,
                address: event.address,
                postal_code: event.postal_code,
                lat: event.lat,
                long: event.long,
                district_name: "",
                is_ongoing: event.status,
                status_text: event.status_text,
                image_url: event.image_url,
                time_start_formatted: event.time_start_formatted,
                time_end_formatted: event.time_end_formatted,
                description: event.description,
                joined: true
            })
        );
    }, [eventsJoinedAll, currentEventTypeId]);

    const finalEventList = React.useMemo(() => {
        const eventList = query.data?.data;

        if (currentEventTypeId === EventTypes.ALBA_EVENT) {
            const mergedEvents = mergedEventsJoinedAll || [];
            const regularEvents = eventList || [];

            const joinedEventIds = new Set(eventsJoinedAll?.map((event) => event.id) || []);

            const updatedRegularEvents = regularEvents.map((event: EventResponse) => ({
                ...event,
                joined: joinedEventIds.has(event.id)
            }));

            const allEventIds = new Set(mergedEvents.map((event) => event.id));
            const uniqueRegularEvents = updatedRegularEvents.filter(
                (event: EventResponse) => !allEventIds.has(event.id)
            );

            return [...mergedEvents, ...uniqueRegularEvents];
        } else {
            if (!eventList) return eventList;

            const joinedEventIds = new Set(eventsJoinedAll?.map((event) => event.id) || []);
            return (
                eventList?.map((event: EventResponse) => ({
                    ...event,
                    joined: joinedEventIds.has(event.id)
                })) || []
            );
        }
    }, [currentEventTypeId, query.data?.data, mergedEventsJoinedAll, eventsJoinedAll]);

    const getEventList = React.useCallback(
        async (eventTypeId?: EventTypeK, districtId?: number) => {
            if (!eventTypeId) return;
            setCurrentEventTypeId(eventTypeId);
            setCurrentDistrictId(districtId);

            if (eventTypeId === EventTypes.ALBA_EVENT) {
                await getEventsJoinedAll();
            }
        },
        [getEventsJoinedAll]
    );

    const refetch = React.useCallback(
        (eventTypeId?: number) => {
            const targetEventTypeId = eventTypeId || currentEventTypeId;
            if (!targetEventTypeId) return;
            query.refetch();
        },
        [currentEventTypeId, query]
    );

    return {
        isLoading: query.isLoading || isLoadingEventsJoinedAll,
        eventList: finalEventList,
        getEventList,
        refetch
    };
};

export const useEventDetailQueries = () => {
    const [manualEventId, setManualEventId] = React.useState<number | undefined>(undefined);

    const query = useQuery({
        queryKey: ["eventDetail", manualEventId],
        queryFn: () => eventApi.getEventDetail(manualEventId!),
        enabled: !!manualEventId,
        ...defaultQueryConfig
    });

    const getEventDetail = React.useCallback((targetEventId: number) => {
        if (targetEventId) {
            setManualEventId(targetEventId);
        }
    }, []);

    return {
        eventDetail: query.data?.data,
        isLoading: query.isLoading,
        getEventDetail
    };
};

export const useJoinEventQueries = () => {
    const navigation = useNavigation();

    const mutation = useMutation({
        mutationFn: eventApi.joinEvent,
        onSuccess: (data) => {
            if (data?.ok) {
                navigation.navigate(RouteName.SpecialEvent, { eventId: data?.data?.id });
            }
        }
    });

    const joinEvent = React.useCallback(
        async ({ code, eventId }: { code?: string; eventId?: number }) => {
            if (code || eventId) {
                const data = await mutation.mutateAsync({ code, eventId });
                return data?.ok ? data.data : undefined;
            }
        },
        [mutation]
    );

    return {
        ...mutation,
        isLoading: mutation.isPending,
        joinEvent
    };
};

export const useEventLeaderboardQueries = () => {
    const [currentEventId, setCurrentEventId] = React.useState<number | undefined>(undefined);

    const query = useQuery({
        queryKey: ["eventLeaderboard", currentEventId],
        queryFn: () => eventApi.getEventLeaderboard(currentEventId!),
        enabled: !!currentEventId,
        ...defaultQueryConfig
    });

    const getEventLeaderboard = React.useCallback((eventId?: number) => {
        if (eventId) {
            setCurrentEventId(eventId);
        }
    }, []);

    return {
        isLoading: query.isLoading,
        eventLeaderboard: query.data?.data,
        getEventLeaderboard
    };
};
