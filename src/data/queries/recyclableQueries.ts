import { useQuery } from "@tanstack/react-query";

import { recyclablesApi } from "../api";

import { defaultQueryConfig } from "./queryConfig";

export const useRecyclablesQueries = () => {
    const query = useQuery({
        queryKey: ["recyclables"],
        queryFn: recyclablesApi.getAll,
        ...defaultQueryConfig
    });

    return {
        isLoading: query.isLoading,
        recyclables: query.data?.data
    };
};
