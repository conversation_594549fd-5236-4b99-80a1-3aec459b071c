import { useMutation } from "@tanstack/react-query";
import React from "react";

import { contactApi } from "../api";

export const useContactQueries = () => {
    const mutation = useMutation({
        mutationFn: contactApi.sendContact
    });

    const sendContact = React.useCallback(
        async (formData: FormData) => {
            const result = await mutation.mutateAsync(formData);
            return result;
        },
        [mutation]
    );

    return {
        ...mutation,
        isLoading: mutation.isPending,
        sendContact
    };
};
