import { useQuery } from "@tanstack/react-query";
import React from "react";

import { districtApi } from "../api";

import { defaultQueryConfig } from "./queryConfig";

export const useDistrictQueries = () => {
    const [searchName, setSearchName] = React.useState<string | undefined>(undefined);

    const query = useQuery({
        queryKey: ["districts", searchName],
        queryFn: () => districtApi.search(searchName),
        enabled: true,
        ...defaultQueryConfig
    });

    const search = React.useCallback((name: string) => {
        setSearchName(name);
    }, []);

    return {
        districts: query.data?.data,
        isLoading: query.isLoading,
        search
    };
};
