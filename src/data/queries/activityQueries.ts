import { useQuery } from "@tanstack/react-query";

import { useUserStore } from "@/app/store";

import { activityApi } from "../api";

import { defaultQueryConfig } from "./queryConfig";

export const useActivityQueries = () => {
    const { isGuestMode } = useUserStore();

    const query = useQuery({
        queryKey: ["activityLeaderboard"],
        queryFn: activityApi.getLeaderboard,
        enabled: !isGuestMode(),
        ...defaultQueryConfig
    });

    return {
        isLoading: query.isLoading,
        leaderboard: query.data?.data
    };
};

export const useActivityHistoryQueries = () => {
    const query = useQuery({
        queryKey: ["activityHistory"],
        queryFn: activityApi.getRecyclingHistory,
        enabled: true,
        ...defaultQueryConfig
    });

    return {
        isLoading: query.isLoading,
        recyclingHistory: query.data?.data
    };
};
