import { useNavigation } from "@react-navigation/native";
import { useMutation } from "@tanstack/react-query";
import React from "react";

import { useBinStore } from "@/app/store";

import { useLocationServices } from "@/presentation/hooks";

import { binApi } from "../api";

import usePointGoal from "@/presentation/hooks/point/usePointGoal";
import { RouteName } from "@/shared/constants";
import { createDataFromDataObject } from "@/shared/helper";

export const useBinQueries = () => {
    const { setBins, bins } = useBinStore();
    const [isRefreshing, setIsRefreshing] = React.useState(false);
    const refreshIntervalRef = React.useRef<ReturnType<typeof setInterval> | null>(null);
    const [isRefreshPaused, setIsRefreshPaused] = React.useState(false);

    const mutation = useMutation({
        mutationFn: binApi.getAll,
        onSuccess: (data) => {
            if (data?.ok) {
                setBins(data.data || []);
            }
        }
    });

    const getBins = React.useCallback(
        async (silent = false) => {
            setBins([]);
            if (silent) {
                setIsRefreshing(true);
            }

            const result = await mutation.mutateAsync();

            if (silent) {
                setIsRefreshing(false);
            }

            if (result?.ok) {
                setBins(result.data || []);
            }
        },
        [mutation, setBins]
    );

    const pauseRefreshInterval = React.useCallback(() => {
        if (refreshIntervalRef.current) {
            clearInterval(refreshIntervalRef.current);
            refreshIntervalRef.current = null;
        }
        setIsRefreshPaused(true);
    }, []);

    const resumeRefreshInterval = React.useCallback(() => {
        if (!refreshIntervalRef.current) {
            refreshIntervalRef.current = setInterval(() => {
                getBins(true);
            }, 300000);
        }
        setIsRefreshPaused(false);
    }, [getBins]);

    React.useEffect(() => {
        getBins();

        if (!isRefreshPaused) {
            refreshIntervalRef.current = setInterval(() => {
                getBins(true);
            }, 300000);
        }

        return () => {
            if (refreshIntervalRef.current) {
                clearInterval(refreshIntervalRef.current);
            }
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isRefreshPaused]);

    return {
        ...mutation,
        isLoading: mutation.isPending && !isRefreshing,
        isRefreshing,
        bins,
        refreshBins: () => getBins(true),
        pauseRefreshInterval,
        resumeRefreshInterval,
        isRefreshPaused
    };
};

export const useBinByWasteTypeIdQueries = () => {
    const { setBinsByWasteTypeId, binsByWasteTypeId } = useBinStore();

    const mutation = useMutation({
        mutationFn: binApi.getByWasteTypeId
    });

    const getBinsByWasteTypeId = React.useCallback(
        async (wasteTypeId: number) => {
            setBinsByWasteTypeId([]);
            const result = await mutation.mutateAsync(wasteTypeId);
            if (result?.ok) {
                setBinsByWasteTypeId(result.data || []);
            }
        },
        [mutation, setBinsByWasteTypeId]
    );

    const clearBinsByWasteTypeId = React.useCallback(() => {
        setBinsByWasteTypeId(undefined);
    }, [setBinsByWasteTypeId]);

    return {
        ...mutation,
        isLoading: mutation.isPending,
        binsByWasteTypeId,
        getBinsByWasteTypeId,
        clearBinsByWasteTypeId
    };
};

export const useBinByWasteTypeIdAndAcceptedRecyclablesQueries = () => {
    const { setBinsByWasteTypeIdAndAcceptedRecyclables, binsByWasteTypeIdAndAcceptedRecyclables } = useBinStore();
    const mutation = useMutation({
        mutationFn: binApi.getByWasteTypeIdAndAcceptedRecyclables
    });
    const { userLocation } = useLocationServices();

    const getBinsByWasteTypeIdAndAcceptedRecyclables = React.useCallback(
        async (wasteTypeId: number[], acceptedRecyclables: string) => {
            const result = await mutation.mutateAsync({ wasteTypeId, acceptedRecyclables, userLocation });
            if (result?.ok) {
                setBinsByWasteTypeIdAndAcceptedRecyclables(result.data || []);
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [setBinsByWasteTypeIdAndAcceptedRecyclables, userLocation]
    );

    const clearBinsByWasteTypeIdAndAcceptedRecyclables = React.useCallback(() => {
        setBinsByWasteTypeIdAndAcceptedRecyclables(undefined);
    }, [setBinsByWasteTypeIdAndAcceptedRecyclables]);

    return {
        ...mutation,
        isLoading: mutation.isPending,
        binsByWasteTypeIdAndAcceptedRecyclables,
        getBinsByWasteTypeIdAndAcceptedRecyclables,
        clearBinsByWasteTypeIdAndAcceptedRecyclables
    };
};

export const useBinQRDetailQueries = () => {
    const mutation = useMutation({
        mutationFn: binApi.getQRBinDetail
    });

    const getBinDetail = React.useCallback(
        async (url: string): Promise<BinDetailResponse | RecyclingResponse | undefined> => {
            try {
                const result = await mutation.mutateAsync(url);
                if (result?.ok) {
                    return result.data;
                }
            } catch (error) {
                /* empty */
            }
        },
        [mutation]
    );

    return {
        ...mutation,
        isLoading: mutation.isPending,
        getBinDetail
    };
};

export const useRecyclingQueries = () => {
    const navigation = useNavigation();
    const { getPointGoal } = usePointGoal();

    const mutation = useMutation({
        mutationFn: binApi.recycling,
        onSuccess: (data) => {
            if (data?.ok) {
                navigation.navigate(RouteName.SubmittedRedeem, { result: data.data });
            }
        }
    });

    const recycling = React.useCallback(
        async (binId: number, photoPath: string) => {
            try {
                const uri = photoPath.startsWith("file://") ? photoPath : `file://${photoPath}`;

                const fileData = {
                    uri,
                    type: "image/jpeg",
                    name: "photo.jpg"
                };

                const formData = createDataFromDataObject({
                    bin_id: binId.toString(),
                    photo: fileData
                });

                const result = await mutation.mutateAsync(formData);
                if (result?.ok) {
                    await getPointGoal();
                    return result.data;
                }
            } catch (error) {
                /* empty */
            }
        },
        [mutation, getPointGoal]
    );

    return {
        ...mutation,
        isLoading: mutation.isPending,
        recycling
    };
};
