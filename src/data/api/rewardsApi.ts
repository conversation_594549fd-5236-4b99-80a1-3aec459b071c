import { ApiMethod, HttpClient } from "../services/httpClient";

import { EndPoint } from "@/shared/constants";

export const rewardsApi = {
    getAll: async ({ page }: { page: number }): Promise<BaseResponse<RewardResponse[]>> => {
        const response = await HttpClient.request<{
            data: { data: RewardResponse[] };
        }>({
            endpoint: EndPoint.rewards.getAll + `?page=${page}`,
            method: ApiMethod.POST
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data.data };
    },

    getDetail: async ({ id }: { id: number }): Promise<BaseResponse<RewardDetailResponse>> => {
        const response = await HttpClient.request<{
            data: RewardDetailResponse;
        }>({
            endpoint: EndPoint.rewards.detail.replace("{id}", id.toString()),
            method: ApiMethod.GET
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    },

    redeem: async ({ id }: { id: number }): Promise<BaseResponse<RewardRedeemResponse>> => {
        const response = await HttpClient.request<{
            data: RewardRedeemResponse;
        }>({
            endpoint: EndPoint.rewards.redeem.replace("{id}", id.toString()),
            method: ApiMethod.POST
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    }
};
