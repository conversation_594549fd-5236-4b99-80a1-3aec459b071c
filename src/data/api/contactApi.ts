import { ApiMethod, HttpClient } from "../services/httpClient";

import { EndPoint } from "@/shared/constants";

export const contactApi = {
    sendContact: async (formData: FormData): Promise<BaseResponse<boolean>> => {
        const response = await HttpClient.request<{
            data: boolean;
        }>({
            endpoint: EndPoint.contact.send,
            method: ApiMethod.POST,
            headers: {
                "Content-Type": "multipart/form-data"
            },
            body: formData
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    }
};
