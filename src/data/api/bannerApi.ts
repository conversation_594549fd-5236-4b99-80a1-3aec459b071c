import { ApiMethod, HttpClient } from "../services/httpClient";

import { EndPoint } from "@/shared/constants";
import { BannerType } from "@/shared/constants/BannerTypes";

export const bannerApi = {
    getBanner: async (bannerPlacementId: BannerType): Promise<BaseResponse<BannerResponse>> => {
        const response = await HttpClient.request<{
            data: BannerResponse[];
        }>({
            endpoint: EndPoint.banners.banners,
            method: ApiMethod.GET,
            params: { banner_placement_id: bannerPlacementId }
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data[0] };
    }
};
