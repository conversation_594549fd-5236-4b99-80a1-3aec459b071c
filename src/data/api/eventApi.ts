import { Api<PERSON>ethod, HttpClient } from "../services/httpClient";

import { EndPoint } from "@/shared/constants";
import { EventTypeK } from "@/shared/constants/EventTypes";

export const eventApi = {
    getAllEventTypes: async (): Promise<BaseResponse<EventType[]>> => {
        const response = await HttpClient.request<{
            data: {
                event_types: EventType[];
            };
        }>({
            endpoint: EndPoint.event.eventTypes,
            method: ApiMethod.GET
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data.event_types };
    },

    getAllEvents: async ({
        eventTypeId,
        districtId,
        lat,
        long
    }: {
        eventTypeId: EventTypeK;
        districtId?: number;
        lat?: number;
        long?: number;
    }): Promise<BaseResponse<EventResponse[]>> => {
        const response = await HttpClient.request<{
            data: {
                events: EventResponse[];
            };
        }>({
            endpoint: EndPoint.event.events,
            method: ApiMethod.GET,
            params: {
                event_type_id: eventTypeId,
                district_id: districtId,
                lat,
                long
            }
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data.events };
    },

    getEventDetail: async (eventId: number): Promise<BaseResponse<EventDetailResponse>> => {
        const response = await HttpClient.request<{
            data: EventDetailResponse;
        }>({
            endpoint: EndPoint.event.eventDetail.replace("{id}", eventId.toString()),
            method: ApiMethod.GET
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    },

    joinEvent: async ({
        code,
        eventId
    }: {
        code?: string;
        eventId?: number;
    }): Promise<BaseResponse<JoinEventResponse>> => {
        const response = await HttpClient.request<{
            data: JoinEventResponse;
        }>({
            endpoint: EndPoint.event.joinEvent,
            method: ApiMethod.POST,
            body: code ? { code } : { event_id: eventId }
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    },

    getEventLeaderboard: async (eventId: number): Promise<BaseResponse<EventLeaderboardResponse>> => {
        const response = await HttpClient.request<{
            data: EventLeaderboardResponse;
        }>({
            endpoint: EndPoint.event.eventLeaderboard.replace("{id}", eventId.toString()),
            method: ApiMethod.GET
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    }
};
