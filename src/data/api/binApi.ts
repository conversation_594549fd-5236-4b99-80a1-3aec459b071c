import { ApiMethod, HttpClient } from "../services/httpClient";

import { EndPoint } from "@/shared/constants";

export const binApi = {
    getAll: async (): Promise<BaseResponse<BinResponse[]>> => {
        const response = await HttpClient.request<{
            data: {
                locations: BinResponse[];
            };
        }>({
            endpoint: EndPoint.bin.getAll,
            method: ApiMethod.POST
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data.locations };
    },

    getByWasteTypeId: async (wasteTypeId: number): Promise<BaseResponse<BinResponse[]>> => {
        const response = await HttpClient.request<{
            data: {
                locations: BinResponse[];
            };
        }>({
            endpoint: EndPoint.bin.getByWasteTypeId.replace("{wasteTypeId}", wasteTypeId.toString()),
            method: ApiMethod.POST
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data.locations };
    },

    getByWasteTypeIdAndAcceptedRecyclables: async ({
        wasteTypeId,
        acceptedRecyclables,
        userLocation
    }: {
        wasteTypeId: number[];
        acceptedRecyclables: string;
        userLocation?: {
            latitude: number;
            longitude: number;
        } | null;
    }): Promise<BaseResponse<BinResponse[]>> => {
        let endpoint = EndPoint.bin.getAll + "?";

        endpoint += `waste_type_id=${wasteTypeId.join(",")}`;

        if (acceptedRecyclables) {
            endpoint += `&accepted_recyclables=${acceptedRecyclables}`;
        }

        if (userLocation) {
            endpoint += `&lat=${userLocation.latitude}&long=${userLocation.longitude}`;
        }

        const response = await HttpClient.request<{
            data: {
                locations: BinResponse[];
            };
        }>({
            endpoint: endpoint,
            method: ApiMethod.POST
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data.locations };
    },

    getQRBinDetail: async (url: string): Promise<BaseResponse<BinDetailResponse | RecyclingResponse>> => {
        const response = await HttpClient.request<{
            data: BinDetailResponse | RecyclingResponse;
        }>({
            endpoint: url,
            method: ApiMethod.GET
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    },

    recycling: async (formData: FormData): Promise<BaseResponse<RecyclingResponse>> => {
        const response = await HttpClient.request<{
            data: RecyclingResponse;
        }>({
            endpoint: EndPoint.bin.recycling,
            method: ApiMethod.POST,
            headers: {
                "Content-Type": "multipart/form-data"
            },
            body: formData
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    }
};
