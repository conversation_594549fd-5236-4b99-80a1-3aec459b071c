import { Api<PERSON>ethod, HttpClient } from "../services/httpClient";

import { EndPoint, RewardStatusType } from "@/shared/constants";
import { trimObjectStrings } from "@/shared/helper";

export const userApi = {
    getCurrentUser: async (): Promise<BaseResponse<User>> => {
        const response = await HttpClient.request<{
            data: User;
        }>({
            endpoint: EndPoint.user.currentUser,
            method: ApiMethod.GET
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    },

    updateUser: async ({ user }: { user: UpdateUserRequest }): Promise<BaseResponse<User>> => {
        const request = trimObjectStrings({
            first_name: user?.first_name,
            last_name: user?.last_name,
            display_name: user?.display_name,
            dob: user?.dob,
            address: user?.address,
            postal_code: user?.postal_code
        });

        const response = await HttpClient.request<{
            data: { user: User };
        }>({
            endpoint: EndPoint.user.currentUserUpdate,
            method: ApiMethod.POST,
            body: request
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data.user };
    },

    getMyVoucher: async ({ status }: { status: RewardStatusType }): Promise<BaseResponse<RewardResponse[]>> => {
        const response = await HttpClient.request<{
            data: { rewards: RewardResponse[] };
        }>({
            endpoint: EndPoint.user.myVoucher.replace("{status}", status),
            method: ApiMethod.GET
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data.rewards };
    },

    getRewardDetail: async ({ id }: { id: number }): Promise<BaseResponse<MyRewardResponse>> => {
        const response = await HttpClient.request<{
            data: MyRewardResponse;
        }>({
            endpoint: EndPoint.user.rewardDetail.replace("{id}", id.toString()),
            method: ApiMethod.GET
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    },

    getPointGoal: async (): Promise<BaseResponse<PointGoalResponse>> => {
        const response = await HttpClient.request<{
            data: PointGoalResponse;
        }>({
            endpoint: EndPoint.user.pointGoal,
            method: ApiMethod.GET
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    },
    postSendEmailVerification: async (): Promise<BaseResponse<void>> => {
        const response = await HttpClient.request({
            endpoint: EndPoint.user.sendEmailVerification,
            method: ApiMethod.POST
        });

        if (!response?.ok) return;

        return { ok: response.ok };
    },
    verifyEmail: async (otp: string): Promise<BaseResponse<void>> => {
        const response = await HttpClient.request({
            endpoint: EndPoint.user.verifyEmail,
            method: ApiMethod.POST,
            body: {
                code: otp
            }
        });

        if (!response?.ok) return;

        return { ok: response.ok };
    },

    getEventsJoined: async ({ eventId }: { eventId: number }): Promise<BaseResponse<EventJoinedResponse>> => {
        const response = await HttpClient.request<{
            data: { event: EventJoinedResponse };
        }>({
            endpoint: EndPoint.user.eventsJoined.replace("{eventId}", eventId.toString()),
            method: ApiMethod.GET
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data.event };
    },

    getEventsJoinedAll: async (): Promise<BaseResponse<EventsJoinedAllResponse[]>> => {
        const response = await HttpClient.request<{
            data: { events: EventsJoinedAllResponse[] };
        }>({
            endpoint: EndPoint.user.eventsJoinedAll,
            method: ApiMethod.GET
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data.events };
    }
};
