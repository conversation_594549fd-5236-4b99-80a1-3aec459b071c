import { environment } from "../services/environment";

import { EndPoint } from "@/shared/constants";
import { getFirstErrorMessage } from "@/shared/helper/getFirstErrorMessage";

export const validateApi = {
    validateEmail: async (email: string): Promise<string | undefined> => {
        try {
            const response = await fetch(environment.apiBaseUrl + EndPoint.validate.validateEmail, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({ email })
            });
            const result = await response.json();
            return getFirstErrorMessage(result);
        } catch (error) {
            console.error(error);
            return getFirstErrorMessage(error);
        }
    },

    validatePostalCode: async (postalCode: string): Promise<string | undefined> => {
        try {
            const response = await fetch(environment.apiBaseUrl + EndPoint.validate.validatePostalCode, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({ postal_code: postalCode })
            });
            const result = await response.json();
            return getFirstErrorMessage(result);
        } catch (error) {
            console.error(error);
            return getFirstErrorMessage(error);
        }
    },

    validatePhoneNumber: async (phoneNumber: string): Promise<string | undefined> => {
        try {
            const response = await fetch(environment.apiBaseUrl + EndPoint.validate.validatePhoneNumber, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({ phone: phoneNumber })
            });
            const result = await response.json();
            return getFirstErrorMessage(result);
        } catch (error) {
            console.error(error);
            return getFirstErrorMessage(error);
        }
    },

    validateNickname: async (nickname: string): Promise<string | undefined> => {
        try {
            const response = await fetch(environment.apiBaseUrl + EndPoint.validate.validateNickname, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({ nickname })
            });
            const result = await response.json();
            return getFirstErrorMessage(result);
        } catch (error) {
            console.error(error);
            return getFirstErrorMessage(error);
        }
    }
};
