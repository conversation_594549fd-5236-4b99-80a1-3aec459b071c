import { ApiMethod, HttpClient } from "../services/httpClient";

import { EndPoint } from "@/shared/constants";

export const pointApi = {
    getPointHistory: async (): Promise<BaseResponse<PointHistoryResponse[]>> => {
        const response = await HttpClient.request<{
            data: PointHistoryResponse[];
        }>({
            endpoint: EndPoint.pointsHistory.get,
            method: ApiMethod.GET
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    }
};
