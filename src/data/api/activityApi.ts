import { ApiMethod, HttpClient } from "../services/httpClient";

import { EndPoint } from "@/shared/constants";

export const activityApi = {
    getLeaderboard: async (): Promise<BaseResponse<EventLeaderboardResponse>> => {
        const response = await HttpClient.request<{
            data: EventLeaderboardResponse;
        }>({
            endpoint: EndPoint.user.leaderboard,
            method: ApiMethod.GET
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data };
    },
    getRecyclingHistory: async (): Promise<BaseResponse<EventLeaderboardHistoryResponse>> => {
        const response = await HttpClient.request<{
            data: { data: EventLeaderboardHistoryResponse };
        }>({
            endpoint: EndPoint.user.recyclingHistory,
            method: ApiMethod.GET
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data.data };
    }
};
