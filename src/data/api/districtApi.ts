import { ApiMethod, HttpClient } from "../services/httpClient";

import { EndPoint } from "@/shared/constants";

export const districtApi = {
    search: async (name?: string): Promise<BaseResponse<DistrictResponse[]>> => {
        const response = await HttpClient.request<{
            data: { districts: DistrictResponse[] };
        }>({
            endpoint: EndPoint.districts.search,
            method: ApiMethod.GET,
            params: {
                name
            }
        });

        if (!response?.ok) return;

        return { ok: response.ok, data: response.data?.data.districts };
    }
};
