/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React from "react";
import { StatusBar } from "react-native";
import { SafeAreaProvider } from "react-native-safe-area-context";
import "../global.css";

import { networkService } from "./data/services/network";
import { DateTimePicker } from "./presentation/components/dateTimePicker";
import { GluestackUIProvider } from "./presentation/components/ui";
import { useBackHandler, useFontFamily } from "./presentation/hooks";
import { AppStack } from "./presentation/navigator";
import { cleanupMapLibreErrorHandling, initializeMapLibreErrorHandling } from "./shared/helper/maplibreErrorHandler";

const App = () => {
    useFontFamily();
    useBackHandler();

    React.useEffect(() => {
        const initializeServices = async () => {
            await networkService.initialize();
            initializeMapLibreErrorHandling();
        };

        initializeServices();

        return () => {
            cleanupMapLibreErrorHandling();
        };
    }, []);

    return (
        <SafeAreaProvider>
            <GluestackUIProvider>
                <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
                <AppStack />
                <DateTimePicker />
            </GluestackUIProvider>
        </SafeAreaProvider>
    );
};

export default App;
