<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>AlbaGroup Dev</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0.0</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.alba.group</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>app-1-287795066792-ios-4d2e8c0405e2e84ebbcf9d</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.STEPUP.ALBA</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>app-1-287795066792-ios-8f2db0c00f150e7abbcf9d</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>29</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>This app uses your camera to scan QR codes on waste collection bins and take photos of waste items for recycling verification. Camera access is required to identify bin locations and document waste disposal activities.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app uses your location to show nearby waste collection bins and recycling points on the map, calculate distances to recycling facilities, and provide location-based services for waste disposal activities. Location access helps you find the closest recycling options and track your environmental impact.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>This app needs permission to save images to your photo library for documentation purposes.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs access to your photo library so you can attach images when contacting us for support or feedback. Photo access helps us better understand and resolve your inquiries.</string>
	<key>UIAppFonts</key>
	<array>
		<string>Heebo-Bold.ttf</string>
		<string>Heebo-Medium.ttf</string>
		<string>Heebo-Regular.ttf</string>
		<string>Inter-Bold.ttf</string>
		<string>Inter-Regular.ttf</string>
		<string>Inter-Medium.ttf</string>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Fontisto.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
