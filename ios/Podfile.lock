PODS:
  - boost (1.84.0)
  - DoubleConversion (1.1.6)
  - EXConstants (17.1.5):
    - ExpoModulesCore
  - EXImageLoader (5.1.0):
    - ExpoModulesCore
    - React-Core
  - Expo (53.0.6):
    - DoubleConversion
    - ExpoModulesCore
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactAppDependencyProvider
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - ExpoAsset (11.1.4):
    - ExpoModulesCore
  - ExpoBlur (14.1.5):
    - ExpoModulesCore
  - ExpoDevice (7.1.4):
    - ExpoModulesCore
  - ExpoFileSystem (18.1.8):
    - ExpoModulesCore
  - ExpoFont (13.3.1):
    - ExpoModulesCore
  - ExpoImageManipulator (13.1.7):
    - EXImageLoader
    - ExpoModulesCore
    - SDWebImageWebPCoder
  - ExpoImagePicker (16.1.4):
    - ExpoModulesCore
  - ExpoKeepAwake (14.1.4):
    - ExpoModulesCore
  - ExpoLinearGradient (14.1.4):
    - ExpoModulesCore
  - ExpoLinking (7.1.5):
    - ExpoModulesCore
  - ExpoLocation (18.1.5):
    - ExpoModulesCore
  - ExpoModulesCore (2.3.12):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - ExpoSQLite (14.0.6):
    - ExpoModulesCore
    - "sqlite3 (~> 3.45.3+1)"
    - "sqlite3/bytecodevtab (~> 3.45.3+1)"
    - "sqlite3/fts (~> 3.45.3+1)"
    - "sqlite3/fts5 (~> 3.45.3+1)"
  - fast_float (6.1.4)
  - FBLazyVector (0.79.2)
  - Firebase (11.15.0):
    - Firebase/Core (= 11.15.0)
  - Firebase/Auth (11.15.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.15.0)
  - Firebase/Core (11.15.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.15.0)
  - Firebase/CoreOnly (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - Firebase/Database (11.15.0):
    - Firebase/CoreOnly
    - FirebaseDatabase (~> 11.15.0)
  - FirebaseAnalytics (11.15.0):
    - FirebaseAnalytics/Default (= 11.15.0)
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/Default (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement/Default (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheckInterop (11.15.0)
  - FirebaseAuth (11.15.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.15.0)
    - FirebaseCoreExtension (~> 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (11.15.0)
  - FirebaseCore (11.15.0):
    - FirebaseCoreInternal (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - FirebaseCoreInternal (11.15.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseDatabase (11.15.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.15.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - leveldb-library (~> 1.22)
  - FirebaseInstallations (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseSharedSwift (11.15.0)
  - fmt (11.0.2)
  - glog (0.3.5)
  - GoogleAdsOnDeviceConversion (2.1.0):
    - GoogleUtilities/Logger (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/Core (11.15.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/Default (11.15.0):
    - GoogleAdsOnDeviceConversion (= 2.1.0)
    - GoogleAppMeasurement/Core (= 11.15.0)
    - GoogleAppMeasurement/IdentitySupport (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/IdentitySupport (11.15.0):
    - GoogleAppMeasurement/Core (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleUtilities (8.1.0):
    - GoogleUtilities/AppDelegateSwizzler (= 8.1.0)
    - GoogleUtilities/Environment (= 8.1.0)
    - GoogleUtilities/Logger (= 8.1.0)
    - GoogleUtilities/MethodSwizzler (= 8.1.0)
    - GoogleUtilities/Network (= 8.1.0)
    - "GoogleUtilities/NSData+zlib (= 8.1.0)"
    - GoogleUtilities/Privacy (= 8.1.0)
    - GoogleUtilities/Reachability (= 8.1.0)
    - GoogleUtilities/SwizzlerTestHelpers (= 8.1.0)
    - GoogleUtilities/UserDefaults (= 8.1.0)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (8.1.0):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher (4.5.0):
    - GTMSessionFetcher/Full (= 4.5.0)
  - GTMSessionFetcher/Core (4.5.0)
  - GTMSessionFetcher/Full (4.5.0):
    - GTMSessionFetcher/Core
  - hermes-engine (0.79.2):
    - hermes-engine/Pre-built (= 0.79.2)
  - hermes-engine/Pre-built (0.79.2)
  - leveldb-library (1.22.6)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - maplibre-react-native (10.1.4):
    - maplibre-react-native/DynamicLibrary (= 10.1.4)
    - React
    - React-Core
  - maplibre-react-native/DynamicLibrary (10.1.4):
    - React
    - React-Core
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - PromisesObjC (2.4.0)
  - RCT-Folly (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Default (= 2024.11.18.00)
  - RCT-Folly/Default (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
  - RCT-Folly/Fabric (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
  - RCTDeprecation (0.79.2)
  - RCTRequired (0.79.2)
  - RCTTypeSafety (0.79.2):
    - FBLazyVector (= 0.79.2)
    - RCTRequired (= 0.79.2)
    - React-Core (= 0.79.2)
  - React (0.79.2):
    - React-Core (= 0.79.2)
    - React-Core/DevSupport (= 0.79.2)
    - React-Core/RCTWebSocket (= 0.79.2)
    - React-RCTActionSheet (= 0.79.2)
    - React-RCTAnimation (= 0.79.2)
    - React-RCTBlob (= 0.79.2)
    - React-RCTImage (= 0.79.2)
    - React-RCTLinking (= 0.79.2)
    - React-RCTNetwork (= 0.79.2)
    - React-RCTSettings (= 0.79.2)
    - React-RCTText (= 0.79.2)
    - React-RCTVibration (= 0.79.2)
  - React-callinvoker (0.79.2)
  - React-Core (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default (= 0.79.2)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/Default (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/DevSupport (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default (= 0.79.2)
    - React-Core/RCTWebSocket (= 0.79.2)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTWebSocket (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTDeprecation
    - React-Core/Default (= 0.79.2)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-CoreModules (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety (= 0.79.2)
    - React-Core/CoreModulesHeaders (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-jsinspector
    - React-jsinspectortracing
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTFBReactNativeSpec
    - React-RCTImage (= 0.79.2)
    - ReactCommon
    - SocketRocket (= 0.7.1)
  - React-cxxreact (0.79.2):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker (= 0.79.2)
    - React-debug (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-jsinspector
    - React-jsinspectortracing
    - React-logger (= 0.79.2)
    - React-perflogger (= 0.79.2)
    - React-runtimeexecutor (= 0.79.2)
    - React-timing (= 0.79.2)
  - React-debug (0.79.2)
  - React-defaultsnativemodule (0.79.2):
    - hermes-engine
    - RCT-Folly
    - React-domnativemodule
    - React-featureflagsnativemodule
    - React-hermes
    - React-idlecallbacksnativemodule
    - React-jsi
    - React-jsiexecutor
    - React-microtasksnativemodule
    - React-RCTFBReactNativeSpec
  - React-domnativemodule (0.79.2):
    - hermes-engine
    - RCT-Folly
    - React-Fabric
    - React-FabricComponents
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.79.2)
    - React-Fabric/attributedstring (= 0.79.2)
    - React-Fabric/componentregistry (= 0.79.2)
    - React-Fabric/componentregistrynative (= 0.79.2)
    - React-Fabric/components (= 0.79.2)
    - React-Fabric/consistency (= 0.79.2)
    - React-Fabric/core (= 0.79.2)
    - React-Fabric/dom (= 0.79.2)
    - React-Fabric/imagemanager (= 0.79.2)
    - React-Fabric/leakchecker (= 0.79.2)
    - React-Fabric/mounting (= 0.79.2)
    - React-Fabric/observers (= 0.79.2)
    - React-Fabric/scheduler (= 0.79.2)
    - React-Fabric/telemetry (= 0.79.2)
    - React-Fabric/templateprocessor (= 0.79.2)
    - React-Fabric/uimanager (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.79.2)
    - React-Fabric/components/root (= 0.79.2)
    - React-Fabric/components/scrollview (= 0.79.2)
    - React-Fabric/components/view (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-renderercss
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/consistency (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/core (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/dom (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers/events (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager/consistency (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricComponents (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.79.2)
    - React-FabricComponents/textlayoutmanager (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.79.2)
    - React-FabricComponents/components/iostextinput (= 0.79.2)
    - React-FabricComponents/components/modal (= 0.79.2)
    - React-FabricComponents/components/rncore (= 0.79.2)
    - React-FabricComponents/components/safeareaview (= 0.79.2)
    - React-FabricComponents/components/scrollview (= 0.79.2)
    - React-FabricComponents/components/text (= 0.79.2)
    - React-FabricComponents/components/textinput (= 0.79.2)
    - React-FabricComponents/components/unimplementedview (= 0.79.2)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/iostextinput (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/modal (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/rncore (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/safeareaview (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/scrollview (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/text (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/textinput (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricImage (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - RCTRequired (= 0.79.2)
    - RCTTypeSafety (= 0.79.2)
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.79.2)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-featureflags (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
  - React-featureflagsnativemodule (0.79.2):
    - hermes-engine
    - RCT-Folly
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
  - React-graphics (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-utils
  - React-hermes (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-cxxreact (= 0.79.2)
    - React-jsi
    - React-jsiexecutor (= 0.79.2)
    - React-jsinspector
    - React-jsinspectortracing
    - React-perflogger (= 0.79.2)
    - React-runtimeexecutor
  - React-idlecallbacksnativemodule (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-ImageManager (0.79.2):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - ReactCommon/turbomodule/bridging
  - React-jsi (0.79.2):
    - boost
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
  - React-jsiexecutor (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-cxxreact (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-jsinspector
    - React-jsinspectortracing
    - React-perflogger (= 0.79.2)
  - React-jsinspector (0.79.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - React-featureflags
    - React-jsi
    - React-jsinspectortracing
    - React-perflogger (= 0.79.2)
    - React-runtimeexecutor (= 0.79.2)
  - React-jsinspectortracing (0.79.2):
    - RCT-Folly
    - React-oscompat
  - React-jsitooling (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly (= 2024.11.18.00)
    - React-cxxreact (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-jsinspector
    - React-jsinspectortracing
  - React-jsitracing (0.79.2):
    - React-jsi
  - React-logger (0.79.2):
    - glog
  - React-Mapbuffer (0.79.2):
    - glog
    - React-debug
  - React-microtasksnativemodule (0.79.2):
    - hermes-engine
    - RCT-Folly
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
  - react-native-date-picker (5.0.12):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-netinfo (11.4.1):
    - React-Core
  - react-native-safe-area-context (5.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common (= 5.4.0)
    - react-native-safe-area-context/fabric (= 5.4.0)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context/common (5.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context/fabric (5.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-slider (4.5.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - react-native-slider/common (= 4.5.7)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-slider/common (4.5.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-vector-icons (11.0.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-webview (13.15.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-NativeModulesApple (0.79.2):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-oscompat (0.79.2)
  - React-perflogger (0.79.2):
    - DoubleConversion
    - RCT-Folly (= 2024.11.18.00)
  - React-performancetimeline (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - React-cxxreact
    - React-featureflags
    - React-jsinspectortracing
    - React-perflogger
    - React-timing
  - React-RCTActionSheet (0.79.2):
    - React-Core/RCTActionSheetHeaders (= 0.79.2)
  - React-RCTAnimation (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
  - React-RCTAppDelegate (0.79.2):
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsitooling
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTNetwork
    - React-RCTRuntime
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-runtimescheduler
    - React-utils
    - ReactCommon
  - React-RCTBlob (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-jsinspectortracing
    - React-performancetimeline
    - React-RCTAnimation
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-renderercss
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTFBReactNativeSpec (0.79.2):
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTImage (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.79.2):
    - React-Core/RCTLinkingHeaders (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.79.2)
  - React-RCTNetwork (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
  - React-RCTRuntime (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-Core
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectortracing
    - React-jsitooling
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
  - React-RCTSettings (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
  - React-RCTText (0.79.2):
    - React-Core/RCTTextHeaders (= 0.79.2)
    - Yoga
  - React-RCTVibration (0.79.2):
    - RCT-Folly (= 2024.11.18.00)
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
  - React-rendererconsistency (0.79.2)
  - React-renderercss (0.79.2):
    - React-debug
    - React-utils
  - React-rendererdebug (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - RCT-Folly (= 2024.11.18.00)
    - React-debug
  - React-rncore (0.79.2)
  - React-RuntimeApple (0.79.2):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
  - React-RuntimeCore (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-cxxreact
    - React-Fabric
    - React-featureflags
    - React-hermes
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-runtimeexecutor (0.79.2):
    - React-jsi (= 0.79.2)
  - React-RuntimeHermes (0.79.2):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.11.18.00)
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectortracing
    - React-jsitooling
    - React-jsitracing
    - React-RuntimeCore
    - React-utils
  - React-runtimescheduler (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspectortracing
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
  - React-timing (0.79.2)
  - React-utils (0.79.2):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-debug
    - React-hermes
    - React-jsi (= 0.79.2)
  - ReactAppDependencyProvider (0.79.2):
    - ReactCodegen
  - ReactCodegen (0.79.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - ReactCommon (0.79.2):
    - ReactCommon/turbomodule (= 0.79.2)
  - ReactCommon/turbomodule (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker (= 0.79.2)
    - React-cxxreact (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-logger (= 0.79.2)
    - React-perflogger (= 0.79.2)
    - ReactCommon/turbomodule/bridging (= 0.79.2)
    - ReactCommon/turbomodule/core (= 0.79.2)
  - ReactCommon/turbomodule/bridging (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker (= 0.79.2)
    - React-cxxreact (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-logger (= 0.79.2)
    - React-perflogger (= 0.79.2)
  - ReactCommon/turbomodule/core (0.79.2):
    - DoubleConversion
    - fast_float (= 6.1.4)
    - fmt (= 11.0.2)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - React-callinvoker (= 0.79.2)
    - React-cxxreact (= 0.79.2)
    - React-debug (= 0.79.2)
    - React-featureflags (= 0.79.2)
    - React-jsi (= 0.79.2)
    - React-logger (= 0.79.2)
    - React-perflogger (= 0.79.2)
    - React-utils (= 0.79.2)
  - recaptcha-enterprise-react-native (18.7.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RecaptchaEnterprise (<= 18.7.1, >= 18.7.0)
    - Yoga
  - RecaptchaEnterprise (18.7.0):
    - RecaptchaEnterpriseSDK (= 18.7.0)
    - RecaptchaInterop (~> 101.0.0)
  - RecaptchaEnterpriseSDK (18.7.0)
  - RecaptchaInterop (101.0.0)
  - RNCAsyncStorage (2.1.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNCClipboard (1.16.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNFBApp (22.4.0):
    - Firebase/CoreOnly (= 11.15.0)
    - React-Core
  - RNFBAuth (22.4.0):
    - Firebase/Auth (= 11.15.0)
    - React-Core
    - RNFBApp
  - RNFBDatabase (22.4.0):
    - Firebase/Database (= 11.15.0)
    - React-Core
    - RNFBApp
  - RNFlashList (1.7.6):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNGestureHandler (2.24.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated (3.17.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.17.5)
    - RNReanimated/worklets (= 3.17.5)
    - Yoga
  - RNReanimated/reanimated (3.17.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated/apple (= 3.17.5)
    - Yoga
  - RNReanimated/reanimated/apple (3.17.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated/worklets (3.17.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/worklets/apple (= 3.17.5)
    - Yoga
  - RNReanimated/worklets/apple (3.17.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (4.10.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNScreens/common (= 4.10.0)
    - Yoga
  - RNScreens/common (4.10.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNSVG (15.11.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNSVG/common (= 15.11.2)
    - Yoga
  - RNSVG/common (15.11.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.11.18.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - SDWebImage/Core (5.21.1)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - SocketRocket (0.7.1)
  - "sqlite3 (3.45.3+1)":
    - "sqlite3/common (= 3.45.3+1)"
  - "sqlite3/bytecodevtab (3.45.3+1)":
    - sqlite3/common
  - "sqlite3/common (3.45.3+1)"
  - "sqlite3/fts (3.45.3+1)":
    - sqlite3/common
  - "sqlite3/fts5 (3.45.3+1)":
    - sqlite3/common
  - VisionCamera (4.6.4):
    - VisionCamera/Core (= 4.6.4)
    - VisionCamera/React (= 4.6.4)
  - VisionCamera/Core (4.6.4)
  - VisionCamera/React (4.6.4):
    - React-Core
  - Yoga (0.0.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EXConstants (from `../node_modules/expo-constants/ios`)
  - EXImageLoader (from `../node_modules/expo-image-loader/ios`)
  - Expo (from `../node_modules/expo`)
  - ExpoAsset (from `../node_modules/expo-asset/ios`)
  - ExpoBlur (from `../node_modules/expo-blur/ios`)
  - ExpoDevice (from `../node_modules/expo-device/ios`)
  - ExpoFileSystem (from `../node_modules/expo-file-system/ios`)
  - ExpoFont (from `../node_modules/expo-font/ios`)
  - ExpoImageManipulator (from `../node_modules/expo-image-manipulator/ios`)
  - ExpoImagePicker (from `../node_modules/expo-image-picker/ios`)
  - ExpoKeepAwake (from `../node_modules/expo-keep-awake/ios`)
  - ExpoLinearGradient (from `../node_modules/expo-linear-gradient/ios`)
  - ExpoLinking (from `../node_modules/expo-linking/ios`)
  - ExpoLocation (from `../node_modules/expo-location/ios`)
  - ExpoModulesCore (from `../node_modules/expo-modules-core`)
  - ExpoSQLite (from `../node_modules/expo-sqlite/ios`)
  - fast_float (from `../node_modules/react-native/third-party-podspecs/fast_float.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - Firebase
  - FirebaseAppCheckInterop
  - FirebaseAuth
  - FirebaseAuthInterop
  - FirebaseCore
  - FirebaseCoreExtension
  - FirebaseCoreInternal
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleUtilities
  - GTMSessionFetcher
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - "maplibre-react-native (from `../node_modules/@maplibre/maplibre-react-native`)"
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsinspectortracing (from `../node_modules/react-native/ReactCommon/jsinspector-modern/tracing`)
  - React-jsitooling (from `../node_modules/react-native/ReactCommon/jsitooling`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-slider (from `../node_modules/@react-native-community/slider`)"
  - "react-native-vector-icons (from `../node_modules/@react-native-vector-icons/common`)"
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-oscompat (from `../node_modules/react-native/ReactCommon/oscompat`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTFBReactNativeSpec (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTRuntime (from `../node_modules/react-native/React/Runtime`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-renderercss (from `../node_modules/react-native/ReactCommon/react/renderer/css`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactAppDependencyProvider (from `build/generated/ios`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "recaptcha-enterprise-react-native (from `../node_modules/@google-cloud/recaptcha-enterprise-react-native`)"
  - RecaptchaInterop
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBAuth (from `../node_modules/@react-native-firebase/auth`)"
  - "RNFBDatabase (from `../node_modules/@react-native-firebase/database`)"
  - "RNFlashList (from `../node_modules/@shopify/flash-list`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - VisionCamera (from `../node_modules/react-native-vision-camera`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseDatabase
    - FirebaseInstallations
    - FirebaseSharedSwift
    - GoogleAdsOnDeviceConversion
    - GoogleAppMeasurement
    - GoogleUtilities
    - GTMSessionFetcher
    - leveldb-library
    - libwebp
    - nanopb
    - PromisesObjC
    - RecaptchaEnterprise
    - RecaptchaEnterpriseSDK
    - RecaptchaInterop
    - SDWebImage
    - SDWebImageWebPCoder
    - SocketRocket
    - sqlite3

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EXConstants:
    :path: "../node_modules/expo-constants/ios"
  EXImageLoader:
    :path: "../node_modules/expo-image-loader/ios"
  Expo:
    :path: "../node_modules/expo"
  ExpoAsset:
    :path: "../node_modules/expo-asset/ios"
  ExpoBlur:
    :path: "../node_modules/expo-blur/ios"
  ExpoDevice:
    :path: "../node_modules/expo-device/ios"
  ExpoFileSystem:
    :path: "../node_modules/expo-file-system/ios"
  ExpoFont:
    :path: "../node_modules/expo-font/ios"
  ExpoImageManipulator:
    :path: "../node_modules/expo-image-manipulator/ios"
  ExpoImagePicker:
    :path: "../node_modules/expo-image-picker/ios"
  ExpoKeepAwake:
    :path: "../node_modules/expo-keep-awake/ios"
  ExpoLinearGradient:
    :path: "../node_modules/expo-linear-gradient/ios"
  ExpoLinking:
    :path: "../node_modules/expo-linking/ios"
  ExpoLocation:
    :path: "../node_modules/expo-location/ios"
  ExpoModulesCore:
    :path: "../node_modules/expo-modules-core"
  ExpoSQLite:
    :path: "../node_modules/expo-sqlite/ios"
  fast_float:
    :podspec: "../node_modules/react-native/third-party-podspecs/fast_float.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2025-03-03-RNv0.79.0-bc17d964d03743424823d7dd1a9f37633459c5c5
  maplibre-react-native:
    :path: "../node_modules/@maplibre/maplibre-react-native"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsinspectortracing:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/tracing"
  React-jsitooling:
    :path: "../node_modules/react-native/ReactCommon/jsitooling"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-slider:
    :path: "../node_modules/@react-native-community/slider"
  react-native-vector-icons:
    :path: "../node_modules/@react-native-vector-icons/common"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-oscompat:
    :path: "../node_modules/react-native/ReactCommon/oscompat"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTFBReactNativeSpec:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTRuntime:
    :path: "../node_modules/react-native/React/Runtime"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-renderercss:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/css"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactAppDependencyProvider:
    :path: build/generated/ios
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  recaptcha-enterprise-react-native:
    :path: "../node_modules/@google-cloud/recaptcha-enterprise-react-native"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBAuth:
    :path: "../node_modules/@react-native-firebase/auth"
  RNFBDatabase:
    :path: "../node_modules/@react-native-firebase/database"
  RNFlashList:
    :path: "../node_modules/@shopify/flash-list"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  VisionCamera:
    :path: "../node_modules/react-native-vision-camera"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 7e761d76ca2ce687f7cc98e698152abd03a18f90
  DoubleConversion: cb417026b2400c8f53ae97020b2be961b59470cb
  EXConstants: 26f334022fc0b454b7ecdc7c12d36ffa68c7c397
  EXImageLoader: ab4fcf9240cf3636a83c00e3fc5229d692899428
  Expo: 35e54368e7086668b0cc37af27052139f136c763
  ExpoAsset: cac9909bd4c502b54b87b4e7495121c1194b3c5b
  ExpoBlur: 846780b2c90f59e964b9a50385d4deb67174ebfb
  ExpoDevice: 7a961caede6638aa0d37d0d3aa884ab957a2e3d3
  ExpoFileSystem: 3bd7e34a164a9093c23b5a7b690efab8138ebf8e
  ExpoFont: 312c73403bbd4f98e1d6a5330641a56292583cd2
  ExpoImageManipulator: 094b748056f2654d5d7813370c910e20ef3d79b4
  ExpoImagePicker: d2fff488a0738343a3cc21e98544ee9feafaa0e6
  ExpoKeepAwake: e8dedc115d9f6f24b153ccd2d1d8efcdfd68a527
  ExpoLinearGradient: 42e58f6db5ebc20b6fbbf7d31013264644ab12a4
  ExpoLinking: 5d151d4a497d7e375308602f0a89b4e8acf7b5f8
  ExpoLocation: afc197fce1045ac7325f095b0c149308e1653aab
  ExpoModulesCore: 5411340438617e068df45f70127ff34640f3f337
  ExpoSQLite: 80476d564ec75b26ffe801f3953d876377b3c9fe
  fast_float: 06eeec4fe712a76acc9376682e4808b05ce978b6
  FBLazyVector: 84b955f7b4da8b895faf5946f73748267347c975
  Firebase: d99ac19b909cd2c548339c2241ecd0d1599ab02e
  FirebaseAnalytics: 6433dfd311ba78084fc93bdfc145e8cb75740eae
  FirebaseAppCheckInterop: 06fe5a3799278ae4667e6c432edd86b1030fa3df
  FirebaseAuth: a6575e5fbf46b046c58dc211a28a5fbdd8d4c83b
  FirebaseAuthInterop: 7087d7a4ee4bc4de019b2d0c240974ed5d89e2fd
  FirebaseCore: efb3893e5b94f32b86e331e3bd6dadf18b66568e
  FirebaseCoreExtension: edbd30474b5ccf04e5f001470bdf6ea616af2435
  FirebaseCoreInternal: 9afa45b1159304c963da48addb78275ef701c6b4
  FirebaseDatabase: 954eb5613d01573ea50ef839e380edcb68db3707
  FirebaseInstallations: 317270fec08a5d418fdbc8429282238cab3ac843
  FirebaseSharedSwift: e17c654ef1f1a616b0b33054e663ad1035c8fd40
  fmt: a40bb5bd0294ea969aaaba240a927bd33d878cdd
  glog: 5683914934d5b6e4240e497e0f4a3b42d1854183
  GoogleAdsOnDeviceConversion: 2be6297a4f048459e0ae17fad9bfd2844e10cf64
  GoogleAppMeasurement: 700dce7541804bec33db590a5c496b663fbe2539
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMSessionFetcher: fc75fc972958dceedee61cb662ae1da7a83a91cf
  hermes-engine: 314be5250afa5692b57b4dd1705959e1973a8ebe
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  maplibre-react-native: c2f06453adb392c99e436a4f115611e038ba87b5
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: 36fe2295e44b10d831836cc0d1daec5f8abcf809
  RCTDeprecation: 83ffb90c23ee5cea353bd32008a7bca100908f8c
  RCTRequired: eb7c0aba998009f47a540bec9e9d69a54f68136e
  RCTTypeSafety: 659ae318c09de0477fd27bbc9e140071c7ea5c93
  React: c2d3aa44c49bb34e4dfd49d3ee92da5ebacc1c1c
  React-callinvoker: 1bdfb7549b5af266d85757193b5069f60659ef9d
  React-Core: 7150cf9b6a5af063b37003062689f1691e79c020
  React-CoreModules: 15a85e6665d61678942da6ae485b351f4c699049
  React-cxxreact: 74f9de59259ac951923f5726aa14f0398f167af9
  React-debug: a9861ea2196e886642887e29fd1d86c6eee93454
  React-defaultsnativemodule: 0e85419763d183f937ec98ea01a27a2afd6f1aa2
  React-domnativemodule: 9b970dcaf8fe53250622daba1d42877f400b858b
  React-Fabric: 0be44adc205b650b4f6003fd8dd3d72b3a9ba6a8
  React-FabricComponents: ea062466367976df752a9d5847c7136a7f07bb3d
  React-FabricImage: 3add4d33413771218a062920378f3dac2abe6502
  React-featureflags: 4ef61c283dfae8f327dbae70f41bb0399bd9e0fc
  React-featureflagsnativemodule: 3324bd2b55b374897914cf8c6d03cc3c35889d86
  React-graphics: 29570df7b0b3d210ba5a4ec67024b117b5a8c74a
  React-hermes: 8b86e5f54a65ecb69cdf22b3a00a11562eda82d2
  React-idlecallbacksnativemodule: 8624d07a1b25045419521d5809cdf3c948dbc411
  React-ImageManager: 21a10bb92dde7cf5ec29c139769630dadbac0cc2
  React-jserrorhandler: 00697e19cde827e8134d543b404f3f016616e8bc
  React-jsi: 6af1987cfbb1b6621664fdbf6c7b62bd4d38c923
  React-jsiexecutor: 51f372998e0303585cb0317232b938d694663cbd
  React-jsinspector: d797824c7b7c931c5ad8ab41de3841a3d7a52e6f
  React-jsinspectortracing: d9acc748525e22e4c7b7241ea39ecbd99bfa6c22
  React-jsitooling: 5bf7d347f145ae47bcecd4107ba7fe7539ebf55b
  React-jsitracing: 6e27cce907f23131472594523cd6e30737a754e5
  React-logger: 368570a253f00879a1e4fea24ed4047e72e7bbf3
  React-Mapbuffer: f581f01e9c766bbfb7d1f57a6f6ceb86bdc310f1
  React-microtasksnativemodule: 8fc1bfccd27980ed6cfec1f52ffcd6af336f48f7
  react-native-date-picker: 152b88cc5dcf23eb0d2370cebcc35dacbc57a2d6
  react-native-netinfo: f0a9899081c185db1de5bb2fdc1c88c202a059ac
  react-native-safe-area-context: 6cec90ce53951d3b52dc73e873323a4a7662fc0c
  react-native-slider: 89552e4381bc34e492a7b53be7ec78221bbc9896
  react-native-vector-icons: c50b39534509191694fc7d6407b323bea0e9a4ab
  react-native-webview: 73e15e8ad1d4be940192bb81953fc0e2c756a174
  React-NativeModulesApple: 1b5e6bf9164371771e553f744da801f5d9f5c7e5
  React-oscompat: ef5df1c734f19b8003e149317d041b8ce1f7d29c
  React-perflogger: 6fd2f6811533e9c19a61e855c3033eecbf4ad2a0
  React-performancetimeline: df331d0764cc54204a73960408371111efbd34b7
  React-RCTActionSheet: a499b0d6d9793886b67ba3e16046a3fef2cdbbc3
  React-RCTAnimation: 2595dcb10a82216a511b54742f8c28d793852ac6
  React-RCTAppDelegate: f03604b70f57c9469a84a159d8abecf793a5bcff
  React-RCTBlob: e00f9b4e2f151938f4d9864cf33ebf24ac03328a
  React-RCTFabric: d3c06acaa6f6b4f51b37b24c1ae5393f55a2ff22
  React-RCTFBReactNativeSpec: 0f4d4f0da938101f2ca9d5333a8f46e527ad2819
  React-RCTImage: dac5e9f8ec476aefe6e60ee640ebc1dfaf1a4dbe
  React-RCTLinking: 494b785a40d952a1dfbe712f43214376e5f0e408
  React-RCTNetwork: b3d7c30cd21793e268db107dd0980cb61b3c1c44
  React-RCTRuntime: 8137d4927804480c952a40c422e40a6e697c616f
  React-RCTSettings: a060c7e381a3896104761b8eed7e284d95e37df3
  React-RCTText: 4f272b72dbb61f390d8c8274528f9fdbff983806
  React-RCTVibration: 0e5326220719aca12473d703aa46693e3b4ce67a
  React-rendererconsistency: 68db5a64f0c42b0337e25ba7b0e9513caae1389d
  React-renderercss: eeb482d2790028a9b47ce9c214397ae2f4e2a7c5
  React-rendererdebug: c6e3b7583c2f0802cb3b4cf19f714853fa9ac670
  React-rncore: 0f64cacb1becc6f89c99018ca920d012f9044ebd
  React-RuntimeApple: 3d34bd566375cbb61e94360329835fde05ced395
  React-RuntimeCore: 6140c26964655b66ddc8afb744c9df429f833357
  React-runtimeexecutor: d60846710facedd1edb70c08b738119b3ee2c6c2
  React-RuntimeHermes: 00ee3577308f9644df029168aa874ac75f25d710
  React-runtimescheduler: 2ad0d683dd8eac942e03cea82d45982fd5fac362
  React-timing: beb0ba912f9ffc1a6758afa767ae5c03302dc9ee
  React-utils: 5593ad221337dddfc69230fc32e94af714773ce5
  ReactAppDependencyProvider: d5dcc564f129632276bd3184e60f053fcd574d6b
  ReactCodegen: aea17732a7f6a3169e3b2800c58dc12034f874f9
  ReactCommon: 35a5629d7159876dd048c63751eaeb3aa3a6a53b
  recaptcha-enterprise-react-native: 7503528114e67c59702b5af00d641102a4096cc5
  RecaptchaEnterprise: a5914ad9db6ff79eaca7258d82ecef6f10009936
  RecaptchaEnterpriseSDK: 5e72d7f19d2f679b68c25071c33b40f817e21149
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba
  RNCAsyncStorage: e25fda4679876e5204c4c502d297757ec1f17ced
  RNCClipboard: dfcfff4ab54bba045a73273c00ab60a90940388b
  RNFBApp: d15dab17b5639fb7f95712d07c204912a534b8c6
  RNFBAuth: d671a6c27eac871a90c7fbc000e5236b070af055
  RNFBDatabase: 8047d7f35f9d182b4a1961249c55c8d157877de6
  RNFlashList: d698d1afdda4a2b1a7bce9b7c1437460b6110298
  RNGestureHandler: 85b0463dbfed54eaba4360e31c0aa1a0fe83a6a6
  RNReanimated: 7b926938d4190114e5848b5b022d83dbc2c0fe2c
  RNScreens: 4ada1709be95c3a4bf1f885378262ceadd9cc939
  RNSVG: 257f59c952e296c9c36035a8f49b61fda48e398a
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  sqlite3: 02d1f07eaaa01f80a1c16b4b31dfcbb3345ee01a
  VisionCamera: b5c2b3c45e641102902a5194af11dc532805221e
  Yoga: aa3de42163965879e7d68a5caaf3e1cedb2cbeb2

PODFILE CHECKSUM: 1dac1ab416f7b13bfc00f15860f9a9b27da54a43

COCOAPODS: 1.16.2
